<svg xmlns="http://www.w3.org/2000/svg" width="440.598" height="446.631" viewBox="0 0 440.598 446.631">
  <g id="Group_577" data-name="Group 577" transform="translate(-191.858 -297.793)">
    <circle id="Ellipse_50" data-name="Ellipse 50" cx="182.057" cy="182.057" r="182.057" transform="translate(222.51 315.497)" fill="#00b4ff" opacity="0.48"/>
    <g id="Group_548" data-name="Group 548" transform="translate(460.256 380.885)">
      <rect id="Rectangle_112" data-name="Rectangle 112" width="141.081" height="142.015" rx="12.637" transform="matrix(0.994, -0.109, 0.109, 0.994, 11.936, 17.951)" fill="#57321d"/>
      <rect id="Rectangle_113" data-name="Rectangle 113" width="147.447" height="148.424" rx="12.637" transform="translate(0 16.033) rotate(-6.243)" fill="#9a5734"/>
      <rect id="Rectangle_114" data-name="Rectangle 114" width="107.252" height="96.998" rx="12.637" transform="translate(17.36 40) rotate(-6.243)" fill="#a55c3c"/>
      <path id="Path_1028" data-name="Path 1028" d="M442.821,293.156,323.756,306.177a13.837,13.837,0,0,1-15.223-11.992c.208-.011.416-.033.624-.055l119.055-13.021a13.838,13.838,0,0,0,12.255-15.256L427.336,145.824l-.033-.263a13.846,13.846,0,0,1,14.642,12.31L455.065,277.9A13.837,13.837,0,0,1,442.821,293.156Z" transform="translate(-293.862 -144.111)" fill="#8e4c2f"/>
      <rect id="Rectangle_115" data-name="Rectangle 115" width="137.331" height="138.24" rx="12.637" transform="matrix(0.994, -0.109, 0.109, 0.994, 5.582, 20.545)" fill="none" stroke-dasharray="3" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke="#b78842"/>
      <rect id="Rectangle_116" data-name="Rectangle 116" width="97.027" height="38.557" rx="5.283" transform="translate(62.412 64.467) rotate(-6.242)" fill="#b96a42"/>
      <path id="Path_1029" data-name="Path 1029" d="M446.709,232.373l-84.951,9.287a5.767,5.767,0,0,1-6.374-5.114l-.011-.088,84.031-9.2a5.776,5.776,0,0,0,5.125-6.374L441.6,194.141l.91-.1a5.792,5.792,0,0,1,6.385,5.125l2.935,26.82A5.793,5.793,0,0,1,446.709,232.373Z" transform="translate(-289.404 -139.499)" fill="#ad5f3d"/>
      <rect id="Rectangle_117" data-name="Rectangle 117" width="90.839" height="31.916" rx="5.283" transform="matrix(0.994, -0.109, 0.109, 0.994, 65.849, 67.431)" fill="none" stroke-dasharray="3" stroke-linecap="round" stroke-linejoin="round" stroke-width="1" stroke="#b78842"/>
      <circle id="Ellipse_51" data-name="Ellipse 51" cx="11.3" cy="11.3" r="11.3" transform="matrix(0.997, -0.071, 0.071, 0.997, 73.455, 71.116)" fill="#ccc"/>
      <path id="Path_1030" data-name="Path 1030" d="M373.827,212.019c2.328,1.476,2.637,4.926.688,7.707s-5.416,3.839-7.745,2.362-2.637-4.926-.689-7.707S371.5,210.543,373.827,212.019Z" transform="translate(-288.507 -137.848)" fill="#e5e5e5"/>
      <path id="Path_1031" data-name="Path 1031" d="M375.11,230.9a11.26,11.26,0,0,1-8.607-2.683,11.037,11.037,0,0,0,4.129.318,11.3,11.3,0,0,0,6.177-19.768,11.293,11.293,0,0,1-1.7,22.133Z" transform="translate(-288.345 -138.094)" fill="#b2b2b2"/>
    </g>
    <g id="Group_555" data-name="Group 555" transform="translate(191.859 481.59)">
      <g id="Group_551" data-name="Group 551" transform="translate(8.255 4.846)" opacity="0.3">
        <g id="Group_549" data-name="Group 549" transform="translate(0 0)">
          <rect id="Rectangle_118" data-name="Rectangle 118" width="160.819" height="111.492" rx="4.447" transform="matrix(0.99, -0.142, 0.142, 0.99, 0, 22.914)" fill="#151515"/>
          <rect id="Rectangle_119" data-name="Rectangle 119" width="58.449" height="13.698" rx="1.058" transform="matrix(0.99, -0.142, 0.142, 0.99, 15.979, 36.7)" fill="#151515" opacity="0.61"/>
          <path id="Path_1032" data-name="Path 1032" d="M94,340.576a4.392,4.392,0,1,1-4.972-3.721A4.39,4.39,0,0,1,94,340.576Z" transform="translate(-54.966 -231.461)" fill="#151515" opacity="0.61"/>
          <circle id="Ellipse_52" data-name="Ellipse 52" cx="4.392" cy="4.392" r="4.392" transform="matrix(0.541, -0.841, 0.841, 0.541, 41.657, 109.178)" fill="#151515" opacity="0.61"/>
          <circle id="Ellipse_53" data-name="Ellipse 53" cx="4.392" cy="4.392" r="4.392" transform="translate(54.625 106.894) rotate(-53.408)" fill="#151515" opacity="0.61"/>
          <circle id="Ellipse_54" data-name="Ellipse 54" cx="4.392" cy="4.392" r="4.392" transform="translate(69.42 99.717)" fill="#151515" opacity="0.61"/>
          <circle id="Ellipse_55" data-name="Ellipse 55" cx="4.392" cy="4.392" r="4.392" transform="translate(82.462 97.839)" fill="#151515" opacity="0.61"/>
        </g>
        <g id="Group_550" data-name="Group 550" transform="translate(14.065 0.647)">
          <path id="Path_1033" data-name="Path 1033" d="M226.624,351.612,77.084,373.134a4.868,4.868,0,0,1-5.515-4.123l-1.128-7.841,124.01-17.859a4.871,4.871,0,0,0,4.123-5.516L185.2,244.928l25.529-3.673a4.876,4.876,0,0,1,5.516,4.123l14.5,100.718A4.88,4.88,0,0,1,226.624,351.612Z" transform="translate(-70.441 -241.206)" fill="#151515"/>
        </g>
      </g>
      <g id="Group_554" data-name="Group 554">
        <path id="Path_1034" data-name="Path 1034" d="M219.652,346.6,96.063,364.385l-25.944,3.734a4.878,4.878,0,0,1-5.52-4.129l-5.837-40.554L50.11,263.28a4.86,4.86,0,0,1,4.118-5.509l60.781-8.75,47.322-6.812,41.441-5.969a4.868,4.868,0,0,1,5.509,4.129l14.5,100.711A4.878,4.878,0,0,1,219.652,346.6Z" transform="translate(-50.06 -236.19)" fill="#ff6784"/>
        <g id="Group_552" data-name="Group 552" transform="translate(8.702 0.466)" opacity="0.32">
          <path id="Path_1035" data-name="Path 1035" d="M112.838,238.105c.1-.493.186-.986.274-1.489C112.849,238.686,112.762,239.179,112.838,238.105Z" transform="translate(-52.791 -236.616)" fill="#ff6784"/>
          <path id="Path_1036" data-name="Path 1036" d="M161.575,241.686c-1.807,16.449-9.9,31.573-20.479,44.365a126.667,126.667,0,0,1-17.358,16.734c-3.034,2.5-6.111,4.972-9.013,7.633-1.687,1.555-3.318,3.154-4.863,4.852-.405.427-.789.865-1.172,1.314-1.982,3.209-3.91,6.319-5.662,9.67-.153.285-.285.526-.383.734l-.011.011c-.044.153-.11.328-.175.526-.58,1.676-1.194,3.34-1.73,5.038A118.456,118.456,0,0,0,97.673,344.7c-.339,1.752-.624,3.515-.942,5.279-.055.285-.1.526-.131.734-.033.23-.055.482-.088.767-.46,4.118-.843,8.257-1.2,12.386L69.363,367.6a4.878,4.878,0,0,1-5.52-4.129l-5.837-40.554a99.675,99.675,0,0,1,10.1-24.192c9.188-15.5,23.48-24.488,35.877-36.907q1.938-1.938,3.767-3.975c.526-.591,2.552-3.121,2.344-2.76a66.528,66.528,0,0,0,4.162-6.582Z" transform="translate(-58.006 -236.134)" fill="#ff6784"/>
        </g>
        <rect id="Rectangle_120" data-name="Rectangle 120" width="34.059" height="13.698" rx="1.058" transform="matrix(0.99, -0.142, 0.142, 0.99, 13.963, 35.998)" fill="#ffb42b"/>
        <circle id="Ellipse_56" data-name="Ellipse 56" cx="4.392" cy="4.392" r="4.392" transform="translate(22.316 76.994)" fill="#c93f60" opacity="0.61"/>
        <path id="Path_1037" data-name="Path 1037" d="M91.084,308.546a4.392,4.392,0,1,1-4.973-3.721A4.392,4.392,0,0,1,91.084,308.546Z" transform="translate(-46.988 -229.663)" fill="#c93f60" opacity="0.61"/>
        <path id="Path_1038" data-name="Path 1038" d="M102.244,306.8a4.393,4.393,0,1,1-4.973-3.721A4.393,4.393,0,0,1,102.244,306.8Z" transform="translate(-45.926 -229.83)" fill="#c93f60" opacity="0.61"/>
        <path id="Path_1039" data-name="Path 1039" d="M114.152,305.086a4.392,4.392,0,1,1-4.973-3.721A4.392,4.392,0,0,1,114.152,305.086Z" transform="translate(-44.793 -229.993)" fill="#c93f60" opacity="0.61"/>
        <path id="Path_1040" data-name="Path 1040" d="M126.061,303.371a4.392,4.392,0,1,1-4.973-3.721A4.391,4.391,0,0,1,126.061,303.371Z" transform="translate(-43.659 -230.156)" fill="#c93f60" opacity="0.61"/>
        <path id="Path_1041" data-name="Path 1041" d="M138.4,301.509a4.392,4.392,0,1,1-4.973-3.721A4.392,4.392,0,0,1,138.4,301.509Z" transform="translate(-42.485 -230.333)" fill="#c93f60" opacity="0.61"/>
        <g id="Group_553" data-name="Group 553" transform="translate(13.417 0.003)">
          <path id="Path_1042" data-name="Path 1042" d="M218.494,346.6,68.954,368.121A4.869,4.869,0,0,1,63.439,364l-1.128-7.841,124.01-17.86a4.869,4.869,0,0,0,4.123-5.515l-13.371-92.867,25.529-3.673a4.876,4.876,0,0,1,5.516,4.123l14.5,100.718A4.882,4.882,0,0,1,218.494,346.6Z" transform="translate(-62.311 -236.193)" fill="#f4567b"/>
        </g>
        <rect id="Rectangle_121" data-name="Rectangle 121" width="15.447" height="4.02" rx="1.058" transform="translate(29.164 122.425) rotate(-8.192)" fill="#c93f60" opacity="0.61"/>
        <rect id="Rectangle_122" data-name="Rectangle 122" width="15.447" height="4.02" rx="1.058" transform="translate(46.349 119.951) rotate(-8.192)" fill="#c93f60" opacity="0.61"/>
        <path id="Path_1043" data-name="Path 1043" d="M94.113,261.828l-34.5,4.708a3.041,3.041,0,0,1-3.423-2.6h0a3.042,3.042,0,0,1,2.6-3.423l34.5-4.708a3.04,3.04,0,0,1,3.423,2.6h0A3.04,3.04,0,0,1,94.113,261.828Z" transform="translate(-49.48 -234.327)" fill="#ff8aa6" opacity="0.4"/>
      </g>
    </g>
    <g id="Group_559" data-name="Group 559" transform="translate(319.476 357.519)" opacity="0.26">
      <path id="Path_1044" data-name="Path 1044" d="M349.3,126.448c1.14-1.481,3.716-.753,3.716,1.048V414.8h-.034l-8.664-11.266a2.172,2.172,0,0,0-3.351,0l-7.019,9.1a2.172,2.172,0,0,1-3.35,0l-7-9.091a2.171,2.171,0,0,0-3.352,0l-6.988,9.086a2.172,2.172,0,0,1-3.352,0l-7.021-9.1a2.172,2.172,0,0,0-3.349,0l-7.035,9.1a2.172,2.172,0,0,1-3.35,0l-6.989-9.088a2.173,2.173,0,0,0-3.353,0l-6.989,9.088a2.172,2.172,0,0,1-3.35,0l-7.036-9.1a2.172,2.172,0,0,0-3.349,0l-7.021,9.1a2.171,2.171,0,0,1-3.351,0l-6.99-9.089a2.172,2.172,0,0,0-3.351,0l-7.019,9.1a2.172,2.172,0,0,1-3.35,0l-7.007-9.1a2.172,2.172,0,0,0-3.35,0l-7.02,9.1a2.173,2.173,0,0,1-3.351,0l-7.006-9.1a2.173,2.173,0,0,0-3.351,0l-7.019,9.1a2.173,2.173,0,0,1-3.351,0l-7-9.093a2.173,2.173,0,0,0-3.352,0l-4.878,6.334c-1.14,1.481-3.717.754-3.717-1.048V123.862a.834.834,0,0,1,1.487-.421l7.208,9.337a2.173,2.173,0,0,0,3.353,0l6.989-9.088a2.172,2.172,0,0,1,3.351,0l7.032,9.1a2.174,2.174,0,0,0,3.351,0l6.991-9.091a2.172,2.172,0,0,1,3.352,0l7.018,9.1a2.173,2.173,0,0,0,3.351,0l7.006-9.1a2.173,2.173,0,0,1,3.351,0l7.021,9.1a2.171,2.171,0,0,0,3.349,0l7.021-9.1a2.173,2.173,0,0,1,3.351,0l7.006,9.1a2.173,2.173,0,0,0,3.351,0l7.018-9.1a2.172,2.172,0,0,1,3.352,0l6.99,9.089a2.172,2.172,0,0,0,3.351,0l7.019-9.1a2.172,2.172,0,0,1,3.35,0l7.007,9.1a2.172,2.172,0,0,0,3.35,0l7.02-9.1a2.172,2.172,0,0,1,3.35,0l7.007,9.1a2.173,2.173,0,0,0,3.351,0l7.019-9.1a2.173,2.173,0,0,1,3.351,0l7,9.093a2.172,2.172,0,0,0,3.351,0Z" transform="translate(-166.589 -122.899)" fill="#151515"/>
      <rect id="Rectangle_123" data-name="Rectangle 123" width="71.181" height="8.147" transform="translate(27.405 34.518)" fill="#151515"/>
      <g id="Group_558" data-name="Group 558" transform="translate(27.405 54.121)">
        <g id="Group_556" data-name="Group 556">
          <line id="Line_20" data-name="Line 20" x2="99.71" transform="translate(0.283 11.965)" fill="#151515"/>
          <line id="Line_21" data-name="Line 21" x2="136.643" fill="#151515"/>
          <line id="Line_22" data-name="Line 22" x2="136.643" transform="translate(0 137.475)" fill="#151515"/>
          <line id="Line_23" data-name="Line 23" x2="99.71" transform="translate(0.283 27.75)" fill="#151515"/>
          <line id="Line_24" data-name="Line 24" x2="47.172" transform="translate(0.283 43.534)" fill="#151515"/>
          <line id="Line_25" data-name="Line 25" x2="81.068" transform="translate(0.283 59.318)" fill="#151515"/>
          <line id="Line_26" data-name="Line 26" x2="81.915" transform="translate(0.283 75.102)" fill="#151515"/>
          <line id="Line_27" data-name="Line 27" x2="69.204" transform="translate(0.283 90.886)" fill="#151515"/>
          <line id="Line_28" data-name="Line 28" x2="44.63" transform="translate(0.283 106.67)" fill="#151515"/>
          <line id="Line_29" data-name="Line 29" x2="68.357" transform="translate(0.283 122.454)" fill="#151515"/>
          <line id="Line_30" data-name="Line 30" x2="99.475" transform="translate(0.283 195.002)" fill="#151515"/>
          <line id="Line_31" data-name="Line 31" x2="50.74" transform="translate(0.283 205.366)" fill="#151515"/>
        </g>
        <g id="Group_557" data-name="Group 557" transform="translate(115.67 11.965)">
          <line id="Line_32" data-name="Line 32" x2="21.609" fill="#151515"/>
          <line id="Line_33" data-name="Line 33" x2="21.609" transform="translate(0 15.784)" fill="#151515"/>
          <line id="Line_34" data-name="Line 34" x2="19.49" transform="translate(2.118 31.568)" fill="#151515"/>
          <line id="Line_35" data-name="Line 35" x2="19.49" transform="translate(2.118 47.957)" fill="#151515"/>
          <line id="Line_36" data-name="Line 36" x2="17.752" transform="translate(3.856 63.136)" fill="#151515"/>
          <line id="Line_37" data-name="Line 37" x2="14.998" transform="translate(6.611 78.921)" fill="#151515"/>
          <line id="Line_38" data-name="Line 38" x2="9.672" transform="translate(11.937 94.705)" fill="#151515"/>
          <line id="Line_39" data-name="Line 39" x2="14.814" transform="translate(6.795 110.489)" fill="#151515"/>
        </g>
      </g>
      <rect id="Rectangle_124" data-name="Rectangle 124" width="58.949" height="12.302" rx="4.027" transform="translate(105.099 212.089)" fill="#151515"/>
    </g>
    <g id="Group_563" data-name="Group 563" transform="translate(313.271 355.694)">
      <path id="Path_1045" data-name="Path 1045" d="M343.635,124.582c1.139-1.4,3.716-.711,3.716.989V396.7h-.034l-8.665-10.632a2.256,2.256,0,0,0-3.351,0l-7.019,8.584a2.256,2.256,0,0,1-3.35,0l-7-8.579a2.256,2.256,0,0,0-3.352,0l-6.989,8.575a2.254,2.254,0,0,1-3.351,0l-7.021-8.586a2.256,2.256,0,0,0-3.349,0l-7.036,8.59a2.256,2.256,0,0,1-3.35,0l-6.989-8.576a2.256,2.256,0,0,0-3.353,0l-6.989,8.576a2.256,2.256,0,0,1-3.35,0l-7.035-8.59a2.256,2.256,0,0,0-3.349,0l-7.021,8.586a2.256,2.256,0,0,1-3.352,0l-6.99-8.577a2.256,2.256,0,0,0-3.351,0l-7.018,8.584a2.257,2.257,0,0,1-3.351,0l-7.007-8.583a2.255,2.255,0,0,0-3.35,0l-7.02,8.585a2.256,2.256,0,0,1-3.35,0l-7.007-8.583a2.255,2.255,0,0,0-3.35,0l-7.02,8.585a2.257,2.257,0,0,1-3.351,0l-7-8.581a2.255,2.255,0,0,0-3.351,0l-4.879,5.977c-1.14,1.4-3.716.711-3.716-.989V122.141c0-.68,1.029-.955,1.486-.4l7.209,8.811a2.256,2.256,0,0,0,3.353,0l6.988-8.575a2.256,2.256,0,0,1,3.351,0l7.033,8.587a2.256,2.256,0,0,0,3.35,0l6.993-8.579a2.256,2.256,0,0,1,3.351,0l7.018,8.584a2.257,2.257,0,0,0,3.351,0l7.006-8.583a2.256,2.256,0,0,1,3.351,0l7.021,8.586a2.256,2.256,0,0,0,3.35,0l7.021-8.586a2.255,2.255,0,0,1,3.35,0l7.007,8.583a2.256,2.256,0,0,0,3.35,0l7.019-8.584a2.256,2.256,0,0,1,3.351,0l6.99,8.577a2.255,2.255,0,0,0,3.351,0l7.019-8.584a2.255,2.255,0,0,1,3.35,0l7.007,8.583a2.257,2.257,0,0,0,3.351,0l7.019-8.585a2.256,2.256,0,0,1,3.351,0l7.006,8.583a2.257,2.257,0,0,0,3.351,0l7.02-8.585a2.255,2.255,0,0,1,3.35,0l7,8.581a2.256,2.256,0,0,0,3.352,0Z" transform="translate(-160.923 -121.233)" fill="#ededed"/>
      <rect id="Rectangle_125" data-name="Rectangle 125" width="71.181" height="7.688" transform="translate(27.405 32.575)" fill="#bbebff"/>
      <g id="Group_562" data-name="Group 562" transform="translate(27.405 51.075)">
        <g id="Group_560" data-name="Group 560">
          <line id="Line_40" data-name="Line 40" x2="99.71" transform="translate(0.282 11.292)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_41" data-name="Line 41" x2="136.643" stroke-width="1.5" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
          <line id="Line_42" data-name="Line 42" x2="136.643" transform="translate(0 129.738)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
          <line id="Line_43" data-name="Line 43" x2="99.71" transform="translate(0.282 26.188)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_44" data-name="Line 44" x2="47.172" transform="translate(0.282 41.084)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_45" data-name="Line 45" x2="81.068" transform="translate(0.282 55.979)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_46" data-name="Line 46" x2="81.915" transform="translate(0.282 70.875)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_47" data-name="Line 47" x2="69.204" transform="translate(0.282 85.771)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_48" data-name="Line 48" x2="44.63" transform="translate(0.282 100.667)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_49" data-name="Line 49" x2="68.357" transform="translate(0.282 115.563)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_50" data-name="Line 50" x2="99.475" transform="translate(0.282 184.028)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_51" data-name="Line 51" x2="50.74" transform="translate(0.282 193.809)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_561" data-name="Group 561" transform="translate(115.67 11.292)">
          <line id="Line_52" data-name="Line 52" x2="21.609" stroke-width="1" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
          <line id="Line_53" data-name="Line 53" x2="21.609" transform="translate(0 14.896)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_54" data-name="Line 54" x2="19.49" transform="translate(2.118 29.792)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_55" data-name="Line 55" x2="19.49" transform="translate(2.118 45.258)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_56" data-name="Line 56" x2="17.752" transform="translate(3.856 59.583)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_57" data-name="Line 57" x2="14.997" transform="translate(6.611 74.479)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_58" data-name="Line 58" x2="9.672" transform="translate(11.937 89.375)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_59" data-name="Line 59" x2="14.814" transform="translate(6.795 104.271)" fill="none" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
      </g>
      <rect id="Rectangle_126" data-name="Rectangle 126" width="58.949" height="11.609" rx="4.027" transform="translate(105.099 200.153)" fill="#bbebff"/>
    </g>
    <g id="Group_567" data-name="Group 567" transform="translate(287.386 335.297)" opacity="0.129">
      <path id="Path_1046" data-name="Path 1046" d="M319.071,104.54a2.04,2.04,0,0,1,3.826.717L360.449,421.8l-.033,0L350.34,410.415a2.041,2.041,0,0,0-3.329.392l-5.78,10.849a2.04,2.04,0,0,1-3.327.393l-8.144-9.192a2.041,2.041,0,0,0-3.329.4l-5.752,10.836a2.04,2.04,0,0,1-3.328.4l-8.161-9.2a2.04,2.04,0,0,0-3.326.393l-5.8,10.859a2.04,2.04,0,0,1-3.328.391l-8.128-9.188a2.039,2.039,0,0,0-3.329.394l-5.753,10.835a2.04,2.04,0,0,1-3.327.4l-8.176-9.2a2.039,2.039,0,0,0-3.325.4l-5.784,10.853a2.04,2.04,0,0,1-3.328.392l-8.129-9.191a2.04,2.04,0,0,0-3.329.392l-5.78,10.848a2.04,2.04,0,0,1-3.327.394l-8.147-9.2a2.04,2.04,0,0,0-3.327.393L217.766,436.3a2.04,2.04,0,0,1-3.327.393l-8.147-9.195a2.04,2.04,0,0,0-3.327.393l-5.781,10.85a2.041,2.041,0,0,1-3.328.394l-8.144-9.194a2.041,2.041,0,0,0-3.329.4l-4.016,7.554a2.04,2.04,0,0,1-3.828-.718L137.294,123.214a.817.817,0,0,1,1.422-.638l8.379,9.438a2.039,2.039,0,0,0,3.329-.4l5.753-10.835a2.04,2.04,0,0,1,3.327-.4l8.173,9.2a2.04,2.04,0,0,0,3.327-.4l5.755-10.84a2.04,2.04,0,0,1,3.328-.4l8.158,9.194a2.04,2.04,0,0,0,3.327-.4L197.34,115.9a2.041,2.041,0,0,1,3.328-.4l8.161,9.2a2.039,2.039,0,0,0,3.326-.394l5.784-10.852a2.039,2.039,0,0,1,3.327-.394l8.146,9.2a2.041,2.041,0,0,0,3.328-.393l5.78-10.849a2.04,2.04,0,0,1,3.328-.392l8.13,9.191a2.04,2.04,0,0,0,3.328-.392l5.78-10.849a2.041,2.041,0,0,1,3.328-.393l8.146,9.195a2.041,2.041,0,0,0,3.328-.393l5.781-10.85a2.039,2.039,0,0,1,3.327-.394l8.147,9.2a2.039,2.039,0,0,0,3.327-.394l5.781-10.85a2.041,2.041,0,0,1,3.328-.393l8.145,9.193a2.04,2.04,0,0,0,3.328-.394Z" transform="translate(-137.287 -102.608)" fill="#151515"/>
      <rect id="Rectangle_127" data-name="Rectangle 127" width="71.181" height="9.039" transform="translate(31.606 54.349) rotate(-6.765)" fill="#151515"/>
      <g id="Group_566" data-name="Group 566" transform="translate(34.169 59.851)">
        <g id="Group_564" data-name="Group 564">
          <line id="Line_60" data-name="Line 60" y1="11.746" x2="99.016" transform="translate(1.844 17.501)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_61" data-name="Line 61" y1="16.097" x2="135.691" stroke-width="1.5" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" fill="#151515"/>
          <line id="Line_62" data-name="Line 62" y1="16.097" x2="135.691" transform="translate(17.969 151.469)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"/>
          <line id="Line_63" data-name="Line 63" y1="11.746" x2="99.016" transform="translate(3.908 34.892)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_64" data-name="Line 64" y1="5.557" x2="46.843" transform="translate(5.971 58.472)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_65" data-name="Line 65" y1="9.55" x2="80.503" transform="translate(8.034 71.87)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_66" data-name="Line 66" y1="9.65" x2="81.345" transform="translate(10.097 89.161)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_67" data-name="Line 67" y1="8.152" x2="68.722" transform="translate(12.16 108.049)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_68" data-name="Line 68" y1="5.258" x2="44.319" transform="translate(14.223 128.335)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_69" data-name="Line 69" y1="8.053" x2="67.881" transform="translate(16.286 142.931)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_70" data-name="Line 70" y1="11.719" x2="98.782" transform="translate(25.769 219.199)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_71" data-name="Line 71" y1="5.977" x2="50.387" transform="translate(27.123 236.359)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
        <g id="Group_565" data-name="Group 565" transform="translate(116.428 13.109)">
          <line id="Line_72" data-name="Line 72" y1="2.546" x2="21.458" stroke-width="1" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" fill="#151515"/>
          <line id="Line_73" data-name="Line 73" y1="2.546" x2="21.458" transform="translate(2.063 17.391)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_74" data-name="Line 74" y1="2.296" x2="19.354" transform="translate(6.23 34.782)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_75" data-name="Line 75" y1="2.296" x2="19.354" transform="translate(8.372 52.839)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_76" data-name="Line 76" y1="2.091" x2="17.629" transform="translate(12.082 69.564)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_77" data-name="Line 77" y1="1.767" x2="14.893" transform="translate(16.881 86.955)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_78" data-name="Line 78" y1="1.139" x2="9.604" transform="translate(24.232 104.346)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
          <line id="Line_79" data-name="Line 79" y1="1.745" x2="14.711" transform="translate(21.189 121.736)" fill="#151515" stroke="#bbebff" stroke-linecap="round" stroke-linejoin="round" stroke-width="1"/>
        </g>
      </g>
      <rect id="Rectangle_128" data-name="Rectangle 128" width="58.949" height="13.649" rx="4.027" transform="translate(131.969 240.845) rotate(-6.765)" fill="#151515"/>
    </g>
    <g id="Group_574" data-name="Group 574" transform="translate(279.953 331.234)">
      <path id="Path_1047" data-name="Path 1047" d="M312.267,100.815a2.04,2.04,0,0,1,3.828.714l37.834,316.513-.034,0-10.086-11.382a2.04,2.04,0,0,0-3.328.4l-5.771,10.853a2.04,2.04,0,0,1-3.327.4l-8.151-9.185a2.04,2.04,0,0,0-3.328.4l-5.743,10.841a2.041,2.041,0,0,1-3.328.4l-8.169-9.191a2.041,2.041,0,0,0-3.326.4l-5.787,10.864a2.04,2.04,0,0,1-3.327.393l-8.137-9.182a2.04,2.04,0,0,0-3.329.4l-5.743,10.841a2.041,2.041,0,0,1-3.327.4L261.5,416.49a2.04,2.04,0,0,0-3.325.4L252.4,427.747a2.04,2.04,0,0,1-3.328.394l-8.137-9.183a2.04,2.04,0,0,0-3.328.4l-5.771,10.853a2.04,2.04,0,0,1-3.327.4l-8.155-9.188a2.041,2.041,0,0,0-3.327.4l-5.773,10.855a2.039,2.039,0,0,1-3.327.4l-8.155-9.187a2.039,2.039,0,0,0-3.327.4l-5.771,10.855a2.041,2.041,0,0,1-3.327.4l-8.152-9.186a2.041,2.041,0,0,0-3.328.4l-4.01,7.558a2.04,2.04,0,0,1-3.828-.714L130.506,119.651a.817.817,0,0,1,1.422-.64l8.387,9.43a2.04,2.04,0,0,0,3.33-.4l5.742-10.841a2.04,2.04,0,0,1,3.327-.4l8.182,9.191a2.039,2.039,0,0,0,3.326-.4l5.745-10.845a2.041,2.041,0,0,1,3.328-.4l8.165,9.187a2.04,2.04,0,0,0,3.327-.4l5.759-10.852a2.04,2.04,0,0,1,3.327-.4l8.169,9.191a2.04,2.04,0,0,0,3.326-.4l5.774-10.857a2.041,2.041,0,0,1,3.327-.4l8.155,9.188a2.04,2.04,0,0,0,3.327-.4l5.77-10.853a2.04,2.04,0,0,1,3.328-.4l8.138,9.183a2.039,2.039,0,0,0,3.328-.4l5.771-10.853a2.041,2.041,0,0,1,3.327-.4l8.155,9.188a2.04,2.04,0,0,0,3.327-.4l5.771-10.855a2.04,2.04,0,0,1,3.327-.4l8.155,9.188a2.041,2.041,0,0,0,3.327-.4l5.773-10.856a2.04,2.04,0,0,1,3.327-.4l8.152,9.186a2.041,2.041,0,0,0,3.328-.4Z" transform="translate(-130.499 -98.898)" fill="#fff"/>
      <path id="Path_1048" data-name="Path 1048" d="M350.942,418.036l-.033.011-10.086-11.39a2.042,2.042,0,0,0-3.329.405l-5.771,10.853a2.046,2.046,0,0,1-3.329.394l-8.148-9.188a2.042,2.042,0,0,0-3.329.405l-5.75,10.831a2.031,2.031,0,0,1-3.318.405l-8.17-9.188a2.04,2.04,0,0,0-3.329.394l-5.782,10.864a2.041,2.041,0,0,1-3.329.394l-8.137-9.177a2.035,2.035,0,0,0-3.329.394l-5.75,10.842a2.03,2.03,0,0,1-3.318.394l-8.192-9.188a2.036,2.036,0,0,0-3.318.394l-5.771,10.864a2.046,2.046,0,0,1-3.329.394l-8.137-9.188a2.046,2.046,0,0,0-3.329.394L228.854,430.2a2.042,2.042,0,0,1-3.329.405l-8.159-9.188a2.03,2.03,0,0,0-3.318.394l-5.782,10.853a2.031,2.031,0,0,1-3.318.394l-8.159-9.188a2.046,2.046,0,0,0-3.329.394l-5.771,10.864a2.046,2.046,0,0,1-3.329.394l-8.148-9.188a2.046,2.046,0,0,0-3.329.394l-4.008,7.568a2.044,2.044,0,0,1-3.833-.723L161.9,407.281l1.446-2.727a2.046,2.046,0,0,1,3.329-.394l8.159,9.188a2.031,2.031,0,0,0,3.318-.394l5.782-10.853a2.03,2.03,0,0,1,3.318-.394l8.159,9.188a2.042,2.042,0,0,0,3.329-.405l5.771-10.853a2.046,2.046,0,0,1,3.329-.394l8.137,9.188a2.046,2.046,0,0,0,3.329-.394l5.771-10.864a2.036,2.036,0,0,1,3.318-.394l8.192,9.188a2.03,2.03,0,0,0,3.318-.394l5.75-10.842a2.036,2.036,0,0,1,3.329-.394l8.137,9.177a2.041,2.041,0,0,0,3.329-.394l5.782-10.864a2.04,2.04,0,0,1,3.329-.394l8.17,9.188a2.031,2.031,0,0,0,3.318-.405l5.75-10.831a2.042,2.042,0,0,1,3.329-.405l8.148,9.188a2.046,2.046,0,0,0,3.329-.394l5.771-10.853a2.042,2.042,0,0,1,3.329-.405l10.086,11.39.033-.011L286.131,108.117l4.326-8.137a2.041,2.041,0,0,1,3.329-.394l8.159,9.188a2.042,2.042,0,0,0,3.329-.405l4.008-7.557a2.037,2.037,0,0,1,3.822.712Z" transform="translate(-127.512 -98.898)" fill="#f7f7f7"/>
      <g id="Group_573" data-name="Group 573" transform="translate(34.218 59.846)">
        <g id="Group_572" data-name="Group 572">
          <g id="Group_570" data-name="Group 570">
            <line id="Line_80" data-name="Line 80" y1="11.835" x2="99.006" transform="translate(1.856 17.532)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <g id="Group_568" data-name="Group 568">
              <line id="Line_81" data-name="Line 81" y1="16.218" x2="135.677" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            </g>
            <g id="Group_569" data-name="Group 569" transform="translate(18.104 151.453)">
              <line id="Line_82" data-name="Line 82" y1="16.218" x2="135.677" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            </g>
            <line id="Line_83" data-name="Line 83" y1="11.835" x2="99.006" transform="translate(3.935 34.921)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_84" data-name="Line 84" y1="5.599" x2="46.838" transform="translate(6.013 58.546)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_85" data-name="Line 85" y1="9.622" x2="80.495" transform="translate(8.092 71.912)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_86" data-name="Line 86" y1="9.722" x2="81.336" transform="translate(10.171 89.2)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_87" data-name="Line 87" y1="8.214" x2="68.715" transform="translate(12.249 108.098)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_88" data-name="Line 88" y1="5.297" x2="44.314" transform="translate(14.328 128.404)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_89" data-name="Line 89" y1="8.113" x2="67.873" transform="translate(16.406 142.977)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_90" data-name="Line 90" y1="11.807" x2="98.772" transform="translate(25.96 219.209)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_91" data-name="Line 91" y1="6.022" x2="50.382" transform="translate(27.325 236.411)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
          </g>
          <g id="Group_571" data-name="Group 571" transform="translate(116.428 13.107)">
            <line id="Line_92" data-name="Line 92" y1="2.565" x2="21.456" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_93" data-name="Line 93" y1="2.565" x2="21.456" transform="translate(2.079 17.389)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_94" data-name="Line 94" y1="2.313" x2="19.352" transform="translate(6.261 34.778)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_95" data-name="Line 95" y1="2.313" x2="19.352" transform="translate(8.419 52.833)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_96" data-name="Line 96" y1="2.107" x2="17.627" transform="translate(12.144 69.556)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_97" data-name="Line 97" y1="1.78" x2="14.891" transform="translate(16.957 86.945)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_98" data-name="Line 98" y1="1.148" x2="9.603" transform="translate(24.324 104.335)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
            <line id="Line_99" data-name="Line 99" y1="1.758" x2="14.709" transform="translate(21.297 121.724)" fill="none" stroke="#00b4ff" stroke-miterlimit="10" stroke-width="1"/>
          </g>
        </g>
      </g>
      <rect id="Rectangle_129" data-name="Rectangle 129" width="58.949" height="13.649" rx="4.027" transform="translate(132.166 240.874) rotate(-6.816)" fill="#00b4ff"/>
    </g>
    <g id="Group_575" data-name="Group 575" transform="translate(444.581 512.703)" opacity="0.3">
      <path id="Path_1049" data-name="Path 1049" d="M342.372,387.787h0a14.886,14.886,0,0,1-18.783-9.5l-27.83-84.866c-5.118-15.188-.776-25.829,7.034-28.391h0c7.812-2.562,16.988,6.77,21.254,19.114L351.878,369A14.885,14.885,0,0,1,342.372,387.787Z" transform="translate(-279.613 -264.599)" fill="#151515"/>
      <path id="Path_1050" data-name="Path 1050" d="M340.67,334.187s15.39-23.969,37.844-6.812c0,0,18.922-16.9,34.312,3.532,0,0,19.679-14.633,28.256,13.876,0,0,21.613,77.791-42.3,99.656,0,0-45.413,16.483-87.8-43.058,0,0-12.7-16.572-26.9-26.647a7.7,7.7,0,0,1,2.5-13.725c9.621-2.631,24.374-1.7,43.92,13.8Z" transform="translate(-280.823 -259.251)" fill="#151515"/>
      <path id="Path_1051" data-name="Path 1051" d="M398.439,327.073s9.377,6.518,8.746,22.875" transform="translate(-269.631 -258.655)" fill="#151515"/>
      <path id="Path_1052" data-name="Path 1052" d="M368.26,325.461s9.377,6.517,8.746,22.875" transform="translate(-272.503 -258.808)" fill="#151515"/>
      <path id="Path_1053" data-name="Path 1053" d="M337.411,330.766s5.75,6.845,7.622,22.814" transform="translate(-275.438 -258.303)" fill="#151515"/>
      <path id="Rectangle_130" data-name="Rectangle 130" d="M0,0H61.9a0,0,0,0,1,0,0V51.595A8.956,8.956,0,0,1,52.94,60.55H8.956A8.956,8.956,0,0,1,0,51.595V0A0,0,0,0,1,0,0Z" transform="translate(87.833 174.566) rotate(-19.278)" fill="#151515"/>
      <path id="Path_1054" data-name="Path 1054" d="M331.362,375.4a45.834,45.834,0,0,0-13.826-10.883" transform="translate(-277.33 -255.092)" fill="#151515"/>
    </g>
    <g id="Group_576" data-name="Group 576" transform="translate(436.369 512.155)">
      <path id="Path_1055" data-name="Path 1055" d="M436.329,460.782a9.8,9.8,0,0,1-6.012,12.5L390.409,487.23a9.8,9.8,0,0,1-12.5-6.012L365.276,445.1a70.448,70.448,0,0,1-11.412-2.935c-15.058-5.213-32.975-16.844-50.377-41.287,0,0-12.7-16.57-26.908-26.645a7.7,7.7,0,0,1,2.5-13.722c9.615-2.628,24.378-1.7,43.916,13.8l10.174-40.619s15.4-23.973,37.849-6.812c0,0,9.835-8.794,21.093-6.034,4.359,1.062,8.926,3.855,13.219,9.561,0,0,19.68-14.631,28.255,13.876,0,0,12.923,46.544-10.8,77.745Z" transform="translate(-273.325 -258.751)" fill="#ffc4a4"/>
      <path id="Path_1056" data-name="Path 1056" d="M427.208,460.23a9.8,9.8,0,0,1-6.012,12.5l-39.908,13.952a9.788,9.788,0,0,1-12.112-5.082,9.437,9.437,0,0,0,1.522-.394l39.908-13.952a9.813,9.813,0,0,0,6.023-12.5L403.071,416c23.721-31.2,10.809-77.745,10.809-77.745-1.665-5.531-3.756-9.44-6.034-12.145,5.826.2,12.507,3.943,16.614,17.621,0,0,12.923,46.544-10.8,77.745Z" transform="translate(-264.204 -258.199)" fill="#ffb08d"/>
      <path id="Path_1057" data-name="Path 1057" d="M334.872,387.287h0a14.887,14.887,0,0,1-18.783-9.5l-27.83-84.866c-5.118-15.188-.776-25.829,7.034-28.391h0c7.812-2.562,16.988,6.77,21.254,19.114L344.377,368.5A14.885,14.885,0,0,1,334.872,387.287Z" transform="translate(-272.115 -264.099)" fill="#ffa5a1"/>
      <path id="Path_1058" data-name="Path 1058" d="M334.872,387.287h0a14.887,14.887,0,0,1-18.783-9.5l-27.83-84.866c-5.118-15.188-.776-25.829,7.034-28.391h0c7.812-2.562,16.988,6.77,21.254,19.114L344.377,368.5A14.885,14.885,0,0,1,334.872,387.287Z" transform="translate(-272.115 -264.099)" fill="#ffc4a4"/>
      <path id="Path_1059" data-name="Path 1059" d="M390.939,326.573s9.377,6.518,8.746,22.875" transform="translate(-262.133 -258.155)" fill="none" stroke="#ffb08d" stroke-miterlimit="10" stroke-width="3"/>
      <path id="Path_1060" data-name="Path 1060" d="M360.76,324.961s9.377,6.517,8.746,22.875" transform="translate(-265.005 -258.308)" fill="none" stroke="#ffb08d" stroke-miterlimit="10" stroke-width="3"/>
      <path id="Path_1061" data-name="Path 1061" d="M396.3,358.656c0,38.9-17.282,72.587-42.437,88.861-15.058-5.213-32.975-16.843-50.377-41.287a167.549,167.549,0,0,0-14.073-15.7,107.634,107.634,0,0,0-12.835-10.941,7.546,7.546,0,0,1-2.968-8.268,7.642,7.642,0,0,1,5.465-5.454c8.487-2.322,20.983-1.862,37.235,8.936l-26.842-81.885c-3.088-9.156-2.738-16.657-.252-21.739.077-.186.175-.372.274-.559a12.4,12.4,0,0,1,7.009-6.089,9.774,9.774,0,0,1,7.557.832,18.531,18.531,0,0,1,4.194,2.946c3.888,3.548,7.338,9.057,9.506,15.332L335.1,336.512c4.786-5.673,18.136-17.884,35.921-4.282,0,0,9.835-8.794,21.093-6.034A125.935,125.935,0,0,1,396.3,358.656Z" transform="translate(-273.324 -264.099)" fill="#ffd0bb" opacity="0.346"/>
      <path id="Path_1062" data-name="Path 1062" d="M329.911,330.266s5.75,6.845,7.622,22.814" transform="translate(-267.94 -257.803)" fill="#ffd0bb" stroke="#f99595" stroke-miterlimit="10" stroke-width="3" opacity="0.346"/>
      <path id="Path_1063" data-name="Path 1063" d="M326.372,377.979s-7.918-9.789-16.336-13.963" transform="translate(-269.832 -254.592)" fill="#ffd0bb" stroke="#f99595" stroke-miterlimit="10" stroke-width="3" opacity="0.346"/>
      <path id="Path_1064" data-name="Path 1064" d="M329.418,372.436h0c-3.3,1.084-7.223-1.819-8.752-6.484l-16.62-50.678c-3.034-9.077-1.673-15.028,1.631-16.111h0c3.3-1.083,7.822,4.8,10.335,12.187l16.619,50.678C334.16,366.693,332.723,371.353,329.418,372.436Z" transform="translate(-270.562 -260.775)" fill="#ffdfd2" opacity="0.3"/>
    </g>
    <rect id="Rectangle_131" data-name="Rectangle 131" width="39.973" height="10.404" rx="3.488" transform="translate(565.172 454.813) rotate(-5.097)" fill="#d38262"/>
    <text id="Receipt" transform="translate(309.571 395.867) rotate(-6.431)" fill="#00b4ff" font-size="26" font-family="Quicksand-Bold, Quicksand" font-weight="700"><tspan x="0" y="0">Receipt</tspan></text>
    <circle id="Ellipse_57" data-name="Ellipse 57" cx="9.086" cy="9.086" r="9.086" transform="translate(254.936 434.399)" fill="#4dceff"/>
    <path id="Path_1065" data-name="Path 1065" d="M373.334,133.559a5.5,5.5,0,1,1-5.5-5.5A5.5,5.5,0,0,1,373.334,133.559Z" transform="translate(171.513 235.11)" fill="#fff" opacity="0.378"/>
    <circle id="Ellipse_58" data-name="Ellipse 58" cx="8.847" cy="8.847" r="8.847" transform="translate(203.423 381.638)" fill="#00b4ff"/>
    <circle id="Ellipse_59" data-name="Ellipse 59" cx="6.011" cy="6.011" r="6.011" transform="translate(237.568 469.274)" fill="#4dceff"/>
    <circle id="Ellipse_60" data-name="Ellipse 60" cx="7.69" cy="7.69" r="7.69" transform="translate(327.06 679.642)" fill="#4dceff"/>
    <circle id="Ellipse_61" data-name="Ellipse 61" cx="15.657" cy="15.657" r="15.657" transform="translate(435.375 676.656)" fill="#4dceff"/>
    <path id="Path_1066" data-name="Path 1066" d="M367.5,314.1a4.338,4.338,0,1,1-4.338-4.338A4.339,4.339,0,0,1,367.5,314.1Z" transform="translate(171.18 252.401)" fill="#4dceff"/>
    <circle id="Ellipse_62" data-name="Ellipse 62" cx="13.185" cy="13.185" r="13.185" transform="translate(476.32 316.439) rotate(-45)" fill="#4dceff"/>
    <circle id="Ellipse_63" data-name="Ellipse 63" cx="13.495" cy="13.495" r="13.495" transform="translate(233.31 653.045)" fill="#00b4ff"/>
    <path id="Path_1067" data-name="Path 1067" d="M141.742,417.95a6.3,6.3,0,1,1-6.3-6.3A6.3,6.3,0,0,1,141.742,417.95Z" transform="translate(149.325 262.097)" fill="#00b4ff"/>
    <circle id="Ellipse_64" data-name="Ellipse 64" cx="9.092" cy="9.092" r="9.092" transform="translate(442.566 684.494)" fill="#00b4ff"/>
    <circle id="Ellipse_65" data-name="Ellipse 65" cx="6.322" cy="6.322" r="6.322" transform="translate(538.865 314.38)" fill="#00b4ff"/>
    <circle id="Ellipse_66" data-name="Ellipse 66" cx="11.28" cy="11.28" r="11.28" transform="translate(566.869 329.666)" fill="#00b4ff"/>
    <circle id="Ellipse_67" data-name="Ellipse 67" cx="8.663" cy="8.663" r="8.663" transform="matrix(0.923, -0.385, 0.385, 0.923, 609.799, 560.78)" fill="#00b4ff"/>
  </g>
</svg>
