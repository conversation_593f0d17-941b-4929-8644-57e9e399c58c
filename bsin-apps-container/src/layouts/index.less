.ant-pro-page-container-children-container {
    padding-block-end: 0px !important;
    padding-inline: 0px !important;
}

.ant-page-header-no-children {
    height: 0px !important;
}

.ant-layout-sider-children ul {
    padding-top: 6px !important;
}

.bsin-base-menu-vertical-collapsed .bsin-base-menu-vertical-menu-item {
    height: 48px !important;
    line-height: 48px !important;
    padding: 5px 0px !important;
    width: 48px !important;
}

.ant-menu-submenu-popup .ant-menu-vertical {
    padding-left: 8px !important;
}

.bsin-layout-content {
    padding-block: 0px !important;
    padding-inline: 0px !important;
}

.ant-pro-setting-drawer-handle>span {
    color: white !important
}

.ant-menu-item-selected {
    background-color: #8943ff !important;
    color: #dfe0e3 !important;
}

.bsin-base-menu-vertical-collapsed {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
}

.bsin-base-menu-vertical-submenu {
    height: 48px !important;
    line-height: 48px !important;
    padding: 5px 0px !important;
}

// 最左侧菜单滚动条效果
.ant-layout-sider-children {
    padding-inline: 0.2px !important;
    width: 102.5% !important;
}

.ant-layout-sider-children div:first-child {
    // overflow: hidden auto !important;
    scrollbar-width: none !important;
    /* Firefox */
}

.bsin-base-menu-inline {
    padding: 0 20px 0 5px !important;
}

.ant-layout-sider-children div:first-child::-webkit-scrollbar {
    width: 0.3em !important;
    /* 设置滚动条宽度为0.5em */
}

.ant-layout-sider-children div:first-child::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条背景色为#f1f1f1 */
}

/* 设置滚动条上按钮（向上/向下）的样式 */
.ant-layout-sider-children div:first-child::-webkit-scrollbar-thumb {
    background-color: #cac6c6;
    /* 设置滚动条上按钮的背景色为#888 */
}

.ant-menu-item-selected::after {
    border-bottom: none !important;
}

.ant-menu-item-active::after {
    border-bottom: none !important;
}

.ant-tooltip-placement-right .ant-tooltip-arrow {
    transform: translateY(-50%) translateX(-100%) rotate(-90deg) !important;
}

.ant-tooltip-placement-top .ant-tooltip-arrow {
    transform: translateY(95%) translateX(-50%) rotate(180deg) !important;
}
.ant-spin-spinning{
    top: 100px !important;
}
.ant-tooltip .ant-tooltip-arrow {
    position: absolute;
    z-index: 1;
    display: block;
    pointer-events: none;
    width: 16px;
    height: 16px;
    overflow: hidden;
}

.ant-tooltip-placement-right {
    padding-left: 0 !important;
}

.ant-tooltip-placement-top {
    padding-bottom: 0 !important;
}

/* 当鼠标悬停在滚动条上时的样式 */
.ant-layout-sider-children div:first-child::-webkit-scrollbar-thumb:hover {
    background-color: #cac6c6;
    /* 设置滚动条上按钮的背景色为#555 */
}

.ant-menu-light>.ant-menu{
    background: #004FD9 !important;
}

.bsin-base-menu-vertical-item-icon{
    color: rgba(236, 229, 229, 0.88) !important;
}

// 左侧导航
.ant-layout-sider-children div:nth-child(2) {
    // overflow: hidden auto !important;
    scrollbar-width: none !important;
    /* Firefox */
}

.ant-layout-sider-children div:nth-child(2)::-webkit-scrollbar {
    width: 0.3em !important;
    /* 设置滚动条宽度为0.5em */
}

.ant-layout-sider-children div:nth-child(2)::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条背景色为#f1f1f1 */
}

/* 设置滚动条上按钮（向上/向下）的样式 */
.ant-layout-sider-children div:nth-child(2)::-webkit-scrollbar-thumb {
    background-color: #cac6c6;
    /* 设置滚动条上按钮的背景色为#888 */
}

/* 当鼠标悬停在滚动条上时的样式 */
.ant-layout-sider-children div:nth-child(2)::-webkit-scrollbar-thumb:hover {
    background-color: #cac6c6;
    /* 设置滚动条上按钮的背景色为#555 */
}
.bsin-base-menu-inline-item-icon .anticon{
    color: inherit !important;
}

.bsin-base-menu-vertical-item-icon .anticon{
    color: inherit !important;
}

.bsin-base-menu-horizontal-item-icon .anticon{
    color: inherit !important;
}

.ant-float-btn-icon .anticon{
    color: inherit !important;
}

.ant-menu-item-group-title{
    color: #fff !important;
}

// 右侧内容
.bsin-layout-content::-webkit-scrollbar {
    width: 0.3em !important;
    /* 设置滚动条宽度为0.5em */
}

.bsin-layout-content::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条背景色为#f1f1f1 */
}

/* 设置滚动条上按钮（向上/向下）的样式 */
.bsin-layout-content::-webkit-scrollbar-thumb {
    background-color: #cac6c6;
    /* 设置滚动条上按钮的背景色为#888 */
}

/* 当鼠标悬停在滚动条上时的样式 */
.bsin-layout-content::-webkit-scrollbar-thumb:hover {
    background-color: #cac6c6;
    /* 设置滚动条上按钮的背景色为#555 */
}

// 左右滚动条
.ant-table-content::-webkit-scrollbar {
    height: 0.3em !important;
    /* 设置滚动条宽度为0.5em */
}

.ant-table-content::-webkit-scrollbar-track {
    background-color: #f1f1f1;
    /* 设置滚动条背景色为#f1f1f1 */
}

/* 设置滚动条上按钮（向上/向下）的样式 */
.ant-table-content::-webkit-scrollbar-thumb {
    background-color: #cac6c6;
    /* 设置滚动条上按钮的背景色为#888 */
}

/* 当鼠标悬停在滚动条上时的样式 */
.ant-table-content::-webkit-scrollbar-thumb:hover {
    background-color: #cac6c6;
    /* 设置滚动条上按钮的背景色为#555 */
}

.ant-pro-page-container .ant-pro-page-container-warp-page-header {
    margin: auto !important;
    padding-inline-start: 40px !important;
    padding-inline-end: 40px !important;
}

:where(.css-1qneoxe).ant-menu .ant-menu-title-content {
    justify-content: center !important;
}

.bsin-base-menu-vertical-item-title-collapsed .bsin-base-menu-vertical-item-icon {
    height: 32px !important;
    width: 32px !important;
    line-height: 32px !important;
    justify-content: center;
}

.bsin-base-menu-vertical-item-title-collapsed {
    height: 36px !important;
    min-width: 48px !important;
}

body {
    display: block;
    margin: 0;
}

.loginNav {
    background-color: #dfe0e3;
    width: 160px;
    margin: 0 auto;
    margin-top: 10px;
    height: 30px;
    line-height: 30px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    /* 在交叉轴上居中 */
    justify-content: center;
    /* 在主轴上居中 */
}

.loginNavItem {
    height: 22px;
    line-height: 22px;
    padding: 0 15px;
    margin: 0 5px;
    color: #000;
    border-radius: 50px;
}

.loginForm {
    margin-top: 20px;
    text-align: center;
}

.loginNavItemA {
    height: 22px;
    line-height: 22px;
    padding: 0 15px;
    margin: 0 5px;
    color: #000;
    background-color: #ffffff;
    border-radius: 50px;
}