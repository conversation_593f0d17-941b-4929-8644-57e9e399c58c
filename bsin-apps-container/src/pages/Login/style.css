@import url('https://fonts.font.im/css?family=Poppins');

*,
*::before,
*::after {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
}

.main {
    width: 100%;
    min-height: 100vh;
    overflow: hidden;
    background-color: #3a89f7;
    padding: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.box {
    position: relative;
    width: 100%;
    max-width: 820px;
    height: 540px;
    background-color: #fff;
    border-radius: 3.3rem;
    box-shadow: 0 60px 40px -30px rgba(0, 0, 0, 0.3);
}

.inner_box {
    position: absolute;
    min-width: 346px;
    width: calc(100% - 4.1rem);
    height: calc(100% - 4.1rem);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* background-color: red; */
}

.forms_wrap {
    position: absolute;
    height: 100%;
    width: 45%;
    top: 0;
    left: 0;
    /* background-color: green; */
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
    transition: 0.8s ease-in-out;
}

.carousel {
    position: absolute;
    height: 100%;
    width: 55%;
    top: 0;
    left: 45%;
    background-color: #d2e4ff;
    border-radius: 2rem;
    display: grid;
    grid-template-rows: auto 1fr;
    overflow: hidden;
    transition: 0.8s ease-in-out;
}

.form {
    /* border: 2px solid #000; */
    max-width: 260px;
    width: 100%;
    /* background-color: red; */
    margin: 0 auto;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    grid-column: 1 / 2;
    grid-row: 1 / 2;
    transition: opacity 0.02s 0.4s;
}

.logo {
    /* border: 2px solid #000; */
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.logo img {
    width: 35px;
    margin: 0.3rem;
}

.logo h4 {
    font-size: 1.2rem;
    font-weight: 800;
    letter-spacing: -0.5px;
    color: #151111;
}

.heading {
    text-align: center;
}

.heading h2 {
    font-size: 2.1rem;
    font-weight: 600;
    color: #151111;
}

.heading h6 {
    color: #bababa;
    font-weight: 400;
    font-size: 0.9rem;
    display: inline;
}

.toggle {
    color: #151111;
    text-decoration: none;
    font-size: 0.8rem;
    font-weight: 600;
    transition: 0.3s;
    cursor: pointer;
}

.toggle:hover {
    color: #8371fd;
}

.input_wrap {
    position: relative;
    height: 37px;
    margin-bottom: 2rem;
    /* background-color: red; */
}

.input_wrap_radio {
    position: relative;
    height: 37px;
    margin-top: 2rem;
    margin-bottom: 0.5rem;
    /* background-color: red; */
}

.input_wrap_role {
    position: relative;
    height: 37px;
    margin-bottom: 1rem;
    /* background-color: red; */
}

.input_field {
    position: absolute;
    width: 100%;
    height: 100%;
    background: none;
    border: none;
    outline: none;
    border-bottom: 1px solid #bbb;
    padding: 0;
    font-size: 0.95rem;
    color: #151111;
    transition: 0.4s;
}

.label {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.95rem;
    color: #bbb;
    pointer-events: none;
    transition: 0.4s;
}

.input_field.active {
    border-bottom-color: #151111;
}

.input_field.active+label {
    font-size: 0.75rem;
    top: -2px;
}

.sign_btn {
    height: 2.5rem !important;
    width: 100% !important;
    background-color: #151111 !important;
    color: #fff !important;
    border: none !important;
    border-radius: 10px !important;
    font-size: 15px !important;
    cursor: pointer !important;
    transition: 0.3s !important;
}

.sign_btn:hover {
    background-color: #151111 !important;
}

.text {
    color: #bbb;
    font-size: 0.8rem;
    text-align: center;
    margin-top: 18px;
}

.text a {
    color: #151111;
    transition: 0.3s;
    text-decoration: none;
}

.text a:hover {
    color: #8371fd;
}

main.sign_up_mode .forms_wrap {
    left: 55%;
}

main.sign_up_mode .carousel {
    left: 0;
}

.images_wrapper {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
}

.image {
    width: 100%;
    height: 300px;
    grid-column: 1/2;
    grid-row: 1/2;
    opacity: 0;
    transition: opacity 0.3s, transform 0.5s;
}

.img1 {
    transform: translate(0, -50px);
}

.img2 {
    transform: scale(0.4, 0.5);
}

.img3 {
    transform: scale(0.3) rotate(-20deg);
}

.image.show {
    opacity: 1;
    transform: none;
}

.text_slider {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    /* margin-top: 5rem; */
}

.text_wrapper {
    max-height: 2.2rem;
    overflow: hidden;
    margin-bottom: 2.5rem;
}

.text_group {
    display: flex;
    flex-direction: column;
    text-align: center;
    transform: translateY(0);
    transition: 0.3s;
}

.text_group h2 {
    /* line-height: 2.2rem; */
    font-weight: 600;
    font-size: 1.55rem;
}

.bullets {
    display: flex;
    align-items: center;
    justify-content: center;
}

.bullets span {
    display: block;
    width: 0.5rem;
    height: 0.5rem;
    background-color: #aaa;
    margin: 0 0.25rem;
    border-radius: 50%;
    cursor: pointer;
    transition: 0.3s;
}

.bullets span.active {
    width: 1.1rem;
    background-color: #151111;
    border-radius: 1rem;
}

@media (max-width:850px) {
    .box {
        height: auto;
        max-width: 550px;
        overflow: hidden;
    }

    .inner_box {
        position: static;
        transform: none;
        width: revert;
        height: revert;
        padding: 2rem;
    }

    .forms_wrap {
        position: revert;
        width: 100%;
        height: auto;
    }

    .form {
        max-width: revert;
        padding: 1.5rem 2.5rem 2rem;
        transition: transform 0.8s ease-in-out, opacity 0.45s linear;
    }

    .heading {
        margin: 2rem 0;
    }

    .form.register_form {
        transform: translateX(100%);
    }

    main.sign_up_mode form.login_form {
        transform: translateX(-100%);
    }

    main.sign_up_mode form.register_form {
        transform: translateX(0);
    }

    .carousel {
        position: revert;
        width: 100%;
        height: auto;
        padding: 3rem 2rem;
        display: flex;
    }

    .images_wrapper {
        display: none;
    }

    .text_slider {
        width: 100%;
    }
}

@media(max-width:530px) {
    .main {
        padding: 1rem;
    }

    .box {
        border-radius: 2rem;
    }

    .inner_box {
        padding: 1rem;
    }

    .carousel {
        padding: 1.5rem 1rem;
        border-radius: 1.6rem;
    }

    .text_wrapper {
        margin-bottom: 1rem;
    }

    .text_group h2 {
        font-size: 1.4rem;
    }

    .form {
        padding: 1rem 2rem 1.5rem;
    }
}