<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="me.flyray.bsin.infrastructure.mapper.CustomerIdentityMapper">

    <resultMap id="BaseResultMap" type="me.flyray.bsin.domain.entity.CustomerIdentity">
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="delFlag" column="del_flag" jdbcType="INTEGER"/>
        <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
        <result property="merchantNo" column="merchant_no" jdbcType="VARCHAR"/>
        <result property="customerNo" column="customer_no" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="username" column="username" jdbcType="VARCHAR"/>
        <result property="bizRoleType" column="biz_role_type" jdbcType="TINYINT"/>
        <result property="bizRoleTypeNo" column="biz_role_type_no" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        serial_no
        ,create_by,create_time,
        customer_no,update_time,del_flag,
        tenant_id,merchant_no,name,username,
        biz_role_type,biz_role_type_no
    </sql>


</mapper>
