<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="gen" targetRuntime="MyBatis3" defaultModelType="flat">
        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"></plugin>
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"></plugin>
        <plugin type="org.mybatis.generator.plugins.ToStringPlugin"></plugin>

        <commentGenerator>
            <!-- 指定生成的java文件的编码,没有直接生成到项目时中文可能会乱码 -->
            <property name="javaFileEncoding" value="UTF-8"/>
            <!-- 这个元素用来去除指定生成的注释中是否包含生成的日期 false:表示保护 -->
            <!-- 如果生成日期，会造成即使修改一个字段，整个实体类所有属性都会发生变化，不利于版本控制，所以设置为true -->
            <property name="suppressDate" value="true"/>
            <!-- 是否去除自动生成的注释 true：是 ： false:否 -->
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!--数据库链接URL，用户名、密码 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="************************************************"
                        userId="root"
                        password="123456">
        </jdbcConnection>

        <javaTypeResolver>
        <!-- This property is used to specify whether MyBatis Generator should
            force the use of java.math.BigDecimal for DECIMAL and NUMERIC fields, -->
        <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
        <!-- 生成模型的包名和位置 -->
        <javaModelGenerator targetPackage="me.flyray.bsin.server.domain" targetProject="src\main\java">
            <!--<property name="constructorBased" value="true" />
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>-->
        </javaModelGenerator>
        <!-- 生成映射文件的包名和位置 -->
        <sqlMapGenerator targetPackage="mybatis.mapping.mysql" targetProject="src\main\resources">
            <!--<property name="enableSubPackages" value="true"/>-->
        </sqlMapGenerator>
        <!-- 生成mapper接口 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="me.flyray.bsin.server.mapper" targetProject="src\main\java">
            <!-- <property name="enableSubPackages" value="true"/>-->
        </javaClientGenerator>

        <!-- 要生成哪些表 -->
        <table tableName="hello"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>
    </context>
</generatorConfiguration>