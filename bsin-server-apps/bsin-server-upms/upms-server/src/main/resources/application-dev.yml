spring:
  #数据库配置
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***********************************************************************************************************************************************
    username: root
    password: root1234
  #redis
  redis:
    host: 127.0.0.1
    port: 6379
    password: 123456
    database: 9

shenyu:
  register:
    registerType: http #zookeeper #etcd #nacos #consul
    serverLists: http://localhost:9095 #localhost:2181 #http://localhost:2379 #localhost:8848
    props:
      username: admin
      password: 123456
  client:
    dubbo:
      props:
        contextPath: /upms
        appName: upms
    http:
      props:
        contextPath: /http
        appName: http

dubbo:
  application:
    name: ${spring.application.name}
    # 注册行为：应用级别  可选值 interface、instance、all
    register-mode: interface
    qos-enable: false
  registry:
    address: nacos://localhost:8848
    username: nacos
    password: nacos
  protocol:
    name: dubbo
    port: -1
  scan:
    base-packages: me.flyray.bsin.server.impl
  provider:
    validation: true
    version: dev
  consumer:
    check: false
    version: dev
  custom:
    log-level: full
    request-log: true

bsin:
  oss:
    upload:
      uploadPath: G:\/upload\/
    message:
      upPath: /home/<USER>/ipfsfile/
      preImgUrl: http://file.s11edao.com
    ipfs:
      gateway: http://ipfs.s11edao.com/ipfs/
      api: http://ipfsadmin.s11edao.com/api/v0

#dromara:
#  x-file-storage: #文件存储配置
#    default-platform: ${DROMARA_X_FILE_STORAGE_DEFAULT_PLATFORM} #默认使用的存储平台
#    aliyun-oss:
#      - platform: ${ALIYUN_OSS_PLATFORM} # 存储平台标识
#        enable-storage: true  # 启用存储
#        access-key: ${ALIYUN_OSS_ACCESS_KEY}
#        secret-key: ${ALIYUN_OSS_SECRET_KEY}
#        end-point: ${ALIYUN_OSS_ENDPOINT}
#        bucket-name: ${ALIYUN_OSS_BUCKET_NAME}
#        domain: ${ALIYUN_OSS_DOMAIN} # 访问域名，注意“/”结尾，例如：https://abc.oss-cn-shanghai.aliyuncs.com/
#        base-path: ${ALIYUN_OSS_BASE_PATH} # 基础路径