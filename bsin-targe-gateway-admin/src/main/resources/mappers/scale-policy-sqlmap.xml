<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to You under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "https://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.apache.shenyu.admin.mapper.ScalePolicyMapper">
  <resultMap id="BaseResultMap" type="org.apache.shenyu.admin.model.entity.ScalePolicyDO">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="num" jdbcType="INTEGER" property="num" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="date_created" jdbcType="TIMESTAMP" property="dateCreated" />
    <result column="date_updated" jdbcType="TIMESTAMP" property="dateUpdated" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sort, `status`, num, begin_time, end_time, date_created, date_updated
  </sql>
  <select id="existed" resultType="java.lang.Boolean">
    SELECT true
    FROM scale_policy
    WHERE id = #{id}
    LIMIT 1
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from scale_policy
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from scale_policy
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.apache.shenyu.admin.model.entity.ScalePolicyDO">
    insert into scale_policy (id, sort, `status`, num,
      begin_time, end_time, date_created,
      date_updated)
    values (#{id,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{num,jdbcType=INTEGER},
      #{beginTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{dateCreated,jdbcType=TIMESTAMP},
      #{dateUpdated,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.apache.shenyu.admin.model.entity.ScalePolicyDO">
    insert into scale_policy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      id,
      <if test="sort != null">
        sort,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="beginTime != null">
        begin_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="dateCreated != null">
        date_created,
      </if>
      <if test="dateUpdated != null">
        date_updated,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{id, jdbcType=VARCHAR},
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="beginTime != null">
        #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dateCreated != null">
        #{dateCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="dateUpdated != null">
        #{dateUpdated,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.apache.shenyu.admin.model.entity.ScalePolicyDO">
    update scale_policy
    <set>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=INTEGER},
      </if>
      <if test="beginTime != null">
        begin_time = #{beginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dateCreated != null">
        date_created = #{dateCreated,jdbcType=TIMESTAMP},
      </if>
      <if test="dateUpdated != null">
        date_updated = #{dateUpdated,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.apache.shenyu.admin.model.entity.ScalePolicyDO">
    update scale_policy
    set sort = #{sort,jdbcType=INTEGER},
      `status` = #{status,jdbcType=INTEGER},
      num = #{num,jdbcType=INTEGER},
      begin_time = #{beginTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      date_updated = #{dateUpdated,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectAll" resultType="org.apache.shenyu.admin.model.entity.ScalePolicyDO" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM scale_policy
  </select>
</mapper>
