daemon off;
user  root;
worker_processes  4;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       1205;
        server_name  localhost;

        # nginx静态资源文件压缩配置
        gzip on;
        gzip_buffers 32 4K;
        gzip_comp_level 6;
        gzip_min_length 100;
        gzip_types application/javascript text/css text/xml;
        gzip_disable "MSIE [1-6]\.";

        if ($request_method = 'OPTIONS') {
            return 204;
	    }
        # 跨域配置
        set $cors_origin $http_origin;
        set $cors_cred   true;
        add_header Access-Control-Allow-Origin      $cors_origin;
        add_header Access-Control-Allow-Credentials $cors_cred;
        add_header Access-Control-Allow-Headers    'Origin, X-Requested-With, Content-Type, Accept';#服务端可以接收的header
        add_header Access-Control-Allow-Methods     'GET,POST,OPTIONS';
        add_header Access-Control-Allow-Credentials, true;#服务端接收认证信息，如cookie
 
        location / {
            root   html/static;
            index  index.html index.htm;
        }
 
        error_page   500 502 503 504  /50x.html;
 
        location = /50x.html {
            root   html;
        }
 
        location ^~ /static/ {
            root html/static;
        }
    }

}