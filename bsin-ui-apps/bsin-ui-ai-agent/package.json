{"name": "bsin-ui-ai-admin", "private": true, "scripts": {"dev": "cross-env PORT=5555 max dev", "e2e:ci": "npm run e2e:ci:dev", "e2e:ci:dev": "cross-env PORT=8889 start-test start:all-qiankun:dev 8889 test:ci", "e2e:ci:preview": "cross-env PORT=8889 start-test start:all-qiankun:preview 8889 test:ci:preview", "e2e:local": "cross-env PORT=8889 start-test start:all-qiankun:dev 8889 test:local", "e2e:local:preview": "cross-env-shell PORT=8889 start-test start:all-qiankun:preview 8889 test:local", "preview": "cross-env PORT=5555 max preview --port 5555", "setup": "max setup", "start": "npm run dev", "start:all-qiankun:dev": "pnpm umi-scripts turbo --filter @example/qiankun-* --filter @example/mf-* dev", "start:all-qiankun:preview": "pnpm umi-scripts turbo --filter @example/qiankun-* --filter @example/mf-* preview", "test:ci": "pnpm umi-scripts cypress", "test:local": "cypress open", "build": "max build"}, "dependencies": {"@ant-design/compatible": "^5.1.1", "@ant-design/pro-layout": "^6.5.0", "@ant-design/pro-table": "^2.60.0", "@chatui/core": "^3.0.0", "@umijs/max": "^4.1.0", "@umijs/plugins": "^4.1.0", "antd": "^5.4.7", "antd-style": "^3.6.3", "bsin-agent-ui": "^1.2.2", "idb-keyval": "^6.2.1", "qrcode.react": "^4.0.1", "react": "18.1.0", "react-dom": "18.1.0", "reactflow": "^11.10.3", "socket.io-client": "^4.8.1"}, "devDependencies": {"@umijs/plugin-qiankun": "^2.44.1", "cross-env": "^7.0.3", "cypress": "^12.0.0", "start-server-and-test": "^1.15.2"}}