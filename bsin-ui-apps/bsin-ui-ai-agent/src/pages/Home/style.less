@import './utils.less';

.coverCardList {
  .card {
    :global {
      .ant-card-meta-title {
        margin-bottom: 4px;
        & > a {
          display: inline-block;
          max-width: 100%;
          color: @heading-color;
        }
      }
      .ant-card-meta-description {
        height: 44px;
        overflow: hidden;
        line-height: 22px;
      }
    }

    &:hover {
      :global {
        .ant-card-meta-title > a {
          color: @primary-color;
        }
      }
    }
  }

  .cardItemContent {
    display: flex;
    height: 20px;
    margin-top: 16px;
    margin-bottom: -4px;
    line-height: 20px;
    & > span {
      flex: 1;
      color: @text-color-secondary;
      font-size: 12px;
    }
    .avatarList {
      flex: 0 1 auto;
    }
  }
  .cardList {
    margin-top: 24px;
  }

  :global {
    .ant-list .ant-list-item-content-single {
      max-width: 100%;
    }
  }
}
