{"name": "bsin-ui-data-warehouse", "private": true, "scripts": {"dev": "cross-env PORT=5555 max dev", "e2e:ci": "npm run e2e:ci:dev", "e2e:ci:dev": "cross-env PORT=8889 start-test start:all-qiankun:dev 8889 test:ci", "e2e:ci:preview": "cross-env PORT=8889 start-test start:all-qiankun:preview 8889 test:ci:preview", "e2e:local": "cross-env PORT=8889 start-test start:all-qiankun:dev 8889 test:local", "e2e:local:preview": "cross-env-shell PORT=8889 start-test start:all-qiankun:preview 8889 test:local", "preview": "cross-env PORT=5555 max preview --port 5555", "setup": "max setup", "start": "npm run dev", "start:all-qiankun:dev": "pnpm umi-scripts turbo --filter @example/qiankun-* --filter @example/mf-* dev", "start:all-qiankun:preview": "pnpm umi-scripts turbo --filter @example/qiankun-* --filter @example/mf-* preview", "test:ci": "pnpm umi-scripts cypress", "test:local": "cypress open", "build": "max build"}, "dependencies": {"@ant-design/charts": "^2.2.7", "@ant-design/graphs": "^2.0.5", "@antv/g2plot": "^2.4.33", "@antv/g6": "^5.0.45", "@antv/g6-extension-react": "^0.2.1", "@umijs/max": "^4.1.10", "@umijs/plugins": "^4.1.10", "antd": "^5.4.7", "qrcode.react": "^4.2.0", "react": "18.1.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "18.1.0"}, "devDependencies": {"@umijs/plugin-qiankun": "^2.44.1", "cross-env": "^7.0.3", "cypress": "^12.0.0", "start-server-and-test": "^1.15.2"}}