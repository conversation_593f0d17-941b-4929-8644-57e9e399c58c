.rules-design-input {
  width: 500px;
  display: flex;

  .rules-design-input-text {
    width: 100px;
  }
}

.rules-design-title-line {
  display: flex;
  justify-content: space-between;
}

.rules-design-title-wrapper {
  display: flex;
  align-items: center;
  width: 300px;
}

.rules-design-indication-wrapper {
  display: flex;
}

.rules-design-indication-one {
  display: flex;
  margin-left: 24px;
}

.rules-design-square-one {
  width: 20px;
  height: 20px;
  background-color: blue;
  margin-right: 3px;
}

.rules-design-square-two {
  width: 20px;
  height: 20px;
  background-color: orange;
  margin-right: 3px;
}

.rules-design-square-three {
  width: 20px;
  height: 20px;
  background-color: green;
  margin-right: 3px;
}

.rules-design-rules-text {
  padding: 16px;
  background-color: #ccc;
}

.rules-design {
  display: flex;
  justify-content: space-between;
  background-color: #f0f0f0;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 24px;
}

.rules-design-name {
  width: 150px;
  margin-right: 20px;
  font-size: 16px;
}
