.canvasEditComp-contanier {
  display: flex;
  flex-wrap: wrap;

  .canvasEditComp-wrapper {
    width: 360px;
    min-height: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 16px 16px 0 0;
    border-radius: 10px;
  }

  .canvasEditComp-wrapper-green {
    border: 1px solid green;
  }

  .canvasEditComp-wrapper-orange {
    border: 1px solid orange;
  }

  .canvasEditComp-content {
    width: 250px;
    height: 70px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  .canvasEditComp-title {
    font-size: 16px;
    margin: 6px 0 4px 0;
  }

  .canvasEditComp-value {
    font-size: 16px;
    margin: 0;
  }
}

.canvasEditComp-icon {
  width: 36px;
  height: 70px;
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.canvasEditComp-icon-orange {
  background-color: orange;
}

.canvasEditComp-icon-green {
  background-color: green;
}

.canvasEditComp-close {
  width: 30px;
}
