export const listMock = {
  code: '000000',
  message: '操作成功',
  data: [
    {
      appName: 'DAO治理服务',
      remark: 'dao治理平台',
      updateTime: {
        date: {
          year: 2022,
          month: 8,
          day: 8,
        },
        time: {
          hour: 16,
          minute: 24,
          second: 3,
          nano: 0,
        },
      },
      appCode: 'bsin-ui-daobook',
      type: 0,
      delFlag: null,
      url: 'http://localhost:9091',
      createBy: null,
      tenantAppType: null,
      updateBy: null,
      createTime: {
        date: {
          year: 2022,
          month: 6,
          day: 22,
        },
        time: {
          hour: 13,
          minute: 56,
          second: 20,
          nano: 0,
        },
      },
      appId: '1539487429730373632',
      logo: 'icon',
      theme: null,
      appLanguage: 1,
      status: 1,
      roleId: '001',
    },
    {
      appName: '流程管理',
      remark: '流程管理',
      updateTime: {
        date: {
          year: 2023,
          month: 1,
          day: 6,
        },
        time: {
          hour: 15,
          minute: 28,
          second: 4,
          nano: 0,
        },
      },
      appCode: 'bsin-ui-workflow-admin',
      type: 0,
      delFlag: null,
      url: 'http://localhost:8008',
      createBy: null,
      tenantAppType: null,
      updateBy: null,
      createTime: {
        date: {
          year: 2022,
          month: 8,
          day: 7,
        },
        time: {
          hour: 13,
          minute: 39,
          second: 30,
          nano: 0,
        },
      },
      appId: '1556153036806688768',
      logo: 'icon',
      theme: null,
      appLanguage: 1,
      status: 1,
      roleId: '002',
    },
    {
      appName: 'vue应用',
      remark: 'vue应用',
      updateTime: {
        date: {
          year: 2022,
          month: 10,
          day: 19,
        },
        time: {
          hour: 23,
          minute: 30,
          second: 19,
          nano: 0,
        },
      },
      appCode: 'bsin-paas-vue',
      type: 0,
      delFlag: null,
      url: 'http://www.baidu.com',
      createBy: null,
      tenantAppType: null,
      updateBy: null,
      createTime: {
        date: {
          year: 2022,
          month: 8,
          day: 8,
        },
        time: {
          hour: 13,
          minute: 23,
          second: 55,
          nano: 0,
        },
      },
      appId: '1556511503778189312',
      logo: 'icon',
      theme: null,
      appLanguage: 0,
      status: 1,
      roleId: '003',
    },
    {
      appName: '低代码开发',
      remark: '低代码开发',
      updateTime: {
        date: {
          year: 2022,
          month: 9,
          day: 17,
        },
        time: {
          hour: 17,
          minute: 51,
          second: 0,
          nano: 0,
        },
      },
      appCode: 'bsin-app-dev',
      type: 0,
      delFlag: null,
      url: 'http://localhost:5556',
      createBy: null,
      tenantAppType: null,
      updateBy: null,
      createTime: {
        date: {
          year: 2022,
          month: 8,
          day: 12,
        },
        time: {
          hour: 17,
          minute: 49,
          second: 33,
          nano: 0,
        },
      },
      appId: '1558027900958150656',
      logo: 'icon',
      theme: null,
      appLanguage: 0,
      status: 1,
      roleId: '004',
    },
    {
      appName: '服务编排',
      remark: '业务服务编排',
      updateTime: null,
      appCode: 'bsin-ui-orchestration',
      type: 0,
      delFlag: null,
      url: 'http://location:3000',
      roleId: '005',
    },
  ],
};
