.standardList {}

.headerInfo {
    position: 'relative',
}

.listContent {
    font-size: '0',
}

.listContentItem {
    display: inline-block !important;
    margin-left: 40px !important;
    color: rgba(0, 0, 0, 0.65) !important;
    font-size: 14px !important;
    vertical-align: middle !important;
}

.extraContentSearch {
    width: 272px !important;
    margin-left: 16px !important;
}

.listCard {}

.standardListForm {}

.formResult {
    width: '100%',
}

.appIcon svg{
    width: 48px !important;
    height: 48px !important;
    line-height: 48px !important;
    color: #722ed1 !important;
}