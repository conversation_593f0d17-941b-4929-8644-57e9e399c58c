{"name": "b<PERSON>-ui-waas", "private": true, "scripts": {"dev": "cross-env PORT=5555 max dev", "e2e:ci": "npm run e2e:ci:dev", "e2e:ci:dev": "cross-env PORT=8889 start-test start:all-qiankun:dev 8889 test:ci", "e2e:ci:preview": "cross-env PORT=8889 start-test start:all-qiankun:preview 8889 test:ci:preview", "e2e:local": "cross-env PORT=8889 start-test start:all-qiankun:dev 8889 test:local", "e2e:local:preview": "cross-env-shell PORT=8889 start-test start:all-qiankun:preview 8889 test:local", "preview": "cross-env PORT=5555 max preview --port 5555", "setup": "max setup", "start": "npm run dev", "start:all-qiankun:dev": "pnpm umi-scripts turbo --filter @example/qiankun-* --filter @example/mf-* dev", "start:all-qiankun:preview": "pnpm umi-scripts turbo --filter @example/qiankun-* --filter @example/mf-* preview", "test:ci": "pnpm umi-scripts cypress", "test:local": "cypress open", "build": "max build"}, "dependencies": {"@ant-design/plots": "^1.0.4", "@ant-design/pro-components": "^2.6.7", "@ant-design/pro-table": "^2.59.1", "@umijs/max": "^4.1.10", "@umijs/plugins": "^4.1.10", "@web3-react/core": "^8.2.3", "@web3-react/injected-connector": "^6.0.7", "@web3-react/metamask": "^8.2.4", "@web3-react/walletconnect": "^8.2.3", "antd": "^5.4.7", "markdown-it": "^13.0.2", "qrcode.react": "^3.1.0", "react": "18.1.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "18.1.0", "react-markdown-editor-lite": "^1.3.4", "viewerjs": "^1.11.6", "web3": "^1.7.4", "web3-utils": "^4.0.3"}, "devDependencies": {"@umijs/plugin-qiankun": "^2.44.1", "cross-env": "^7.0.3", "cypress": "^12.0.0", "start-server-and-test": "^1.15.2"}}