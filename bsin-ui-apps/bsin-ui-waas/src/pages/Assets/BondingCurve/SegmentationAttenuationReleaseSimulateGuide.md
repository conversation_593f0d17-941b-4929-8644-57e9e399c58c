# 联合曲线仿真系统使用指南

## 系统概述

联合曲线仿真系统是一个基于衰减系数的积分发放模型可视化工具，用于模拟和分析积分奖励的发放机制。该系统确保在无限档期后，总积分发放数量等于预设的目标值，并支持智能参数推荐和自动计算功能。

## 核心原理

### 数学模型

1. **首档奖励计算**：R₀ = T × (1 - k)
   - T：总积分目标
   - k：衰减系数

2. **档期奖励计算**：Rₙ = R₀ × k^(n-1)
   - n：档位序号
   - k：衰减系数

3. **劳动价值计算**：C = δC × n
   - δC：档位宽度
   - n：档位序号

4. **理论釋放总积分**：ΣR = T / (1 - k)

5. **档位宽度计算**：δC = Tv / N
   - Tv：预估捕获劳动价值
   - N：档位总数

### 参数说明

| 参数 | 符号 | 说明 | 默认值 | 是否必填 |
|------|------|------|--------|----------|
| 总积分目标 | T | 系统预设的总积分发放目标 | 21,000,000 | ✅ |
| 预估劳动价值 | Tv | 预估捕获的劳动价值总量 | 105,000,000 | ✅ |
| 衰减系数 | k | 奖励衰减的比例系数 | 0.9975 | ⚪ |
| 档位宽度 | δC | 每个档位对应的劳动价值增量 | 2,100,000 | ⚪ |
| 档位总数 | N | 总档位数量 | 50 | ⚪ |
| 首档奖励 | R₀ | 第一个档位的奖励金额 | 52,500 | ⚪ |

> ⚪ 表示可选参数，系统会根据其他参数自动计算推荐值，优化目标为使积分释放达成率和劳动价值达成率都接近100%

## 功能特性

### 1. 智能参数输入
- 支持自定义总积分目标、预估劳动价值、衰减系数
- 档位宽度、档位总数、首档奖励支持自动计算
- 实时参数验证，确保输入值的合理性
- 一键重置为默认参数

### 2. 自动计算推荐
- **智能推荐模式**：根据已输入参数自动计算其他参数
- **优化目标**：使积分释放达成率和劳动价值达成率都无限接近于100%
- **动态重新计算**：当用户修改某个参数时，系统会基于积分释放达成率和劳动价值达成率都接近100%的原则，重新计算其他所有参数的推荐值
- **档位宽度计算**：δC = Tv / N
- **档位总数计算**：N = ⌈Tv / δC⌉
- **首档奖励计算**：R₀ = T × (1 - k)
- **衰减系数计算**：k = 1 - (R₀ / T)
- **智能优化**：自动调整参数使两个达成率都接近100%
- 实时显示推荐值和计算推导的参数

### 3. 仿真计算
- 自动计算指定档位数的奖励数据
- 实时生成档期奖励、累计奖励和累计劳动价值曲线
- 支持无限档期的理论计算

### 4. 可视化展示
- **折线图**：展示档期奖励、累计奖励和累计劳动价值随劳动价值的变化
- **关键指标**：显示首档奖励、档位总数、累计发放积分等
- **详细表格**：提供每个档位的具体数据
- **计算推导参数**：显示系统自动计算的参数值

### 5. 数据分析
- 积分释放达成率计算
- 劳动价值达成率分析
- 实际/理论比率分析
- 档位奖励衰减趋势分析

## 使用步骤

### 1. 设置基础参数
1. 在"仿真参数设置"区域输入必填参数：
   - **总积分目标 (T)**：系统预设的总积分发放目标
   - **预估劳动价值 (Tv)**：预估捕获的劳动价值总量
   - **衰减系数 (k)**：奖励衰减的比例系数（0-1之间，可选，系统可自动计算）

### 2. 选择计算模式
1. **自动计算模式**（推荐）：
   - 开启"自动计算"开关
   - 系统会根据基础参数自动计算推荐值
   - 参数变化时自动更新推荐值并执行仿真
   - **动态重新计算**：当用户修改任何参数时，系统会基于积分释放达成率和劳动价值达成率都接近100%的原则，重新计算其他所有参数的推荐值
   - 可以手动调整推荐值进行微调

2. **手动计算模式**：
   - 关闭"自动计算"开关
   - 手动输入档位宽度、档位总数、首档奖励、衰减系数
   - 点击"计算推荐值"按钮手动触发推荐计算
   - 系统会验证参数组合的合理性

### 3. 执行仿真
1. 点击"开始仿真"按钮
2. 系统将自动计算并生成结果
3. 查看计算状态和结果提示

### 4. 分析结果
1. **计算推导参数**：查看系统自动计算的参数值
2. **关键指标**：查看首档奖励、档位总数等关键数据
3. **仿真图表**：分析奖励曲线和劳动价值趋势
4. **档位详情**：查看具体档位数据

## 参数计算逻辑

### 自动计算规则

1. **档位宽度计算**：
   - 输入：预估劳动价值 + 档位总数
   - 计算：δC = Tv / N
   - 应用场景：已知劳动价值和档位总数，计算档位宽度

2. **档位总数计算**：
   - 输入：预估劳动价值 + 档位宽度
   - 计算：N = ⌈Tv / δC⌉
   - 应用场景：已知劳动价值和档位宽度，计算档位总数

3. **首档奖励计算**：
   - 输入：总积分目标 + 衰减系数
   - 计算：R₀ = T × (1 - k)
   - 应用场景：已知总积分目标和衰减系数，计算首档奖励

4. **衰减系数计算**：
   - 输入：总积分目标 + 首档奖励
   - 计算：k = 1 - (R₀ / T)
   - 应用场景：已知总积分目标和首档奖励，计算衰减系数
   - 优化策略：基于积分释放达成率自动调整，使达成率接近100%

5. **智能优化计算**：
   - 目标：使积分释放达成率和劳动价值达成率都接近100%
   - 策略：自动调整衰减系数和档位宽度
   - 优化条件：任一达成率低于99.5%时触发
   - 平衡原则：同时优化两个达成率，避免顾此失彼
   - 衰减系数优化：积分释放达成率过低时减小衰减系数

### 动态重新计算规则

当用户修改某个参数时，系统会根据以下规则重新计算其他参数：

1. **修改总积分目标 (T)**：
   - 重新计算首档奖励：R₀ = T × (1 - k)
   - 重新计算衰减系数：k = 1 - (R₀ / T)
   - 优化目标：确保积分释放达成率接近100%

2. **修改预估劳动价值 (Tv)**：
   - 重新计算档位宽度：δC = Tv / N
   - 重新计算档位总数：N = ⌈Tv / δC⌉
   - 优化目标：确保劳动价值达成率接近100%

3. **修改衰减系数 (k)**：
   - 重新计算首档奖励：R₀ = T × (1 - k)
   - 优化目标：平衡积分释放达成率和奖励衰减速度

4. **修改档位宽度 (δC)**：
   - 重新计算档位总数：N = ⌈Tv / δC⌉
   - 优化目标：确保劳动价值达成率接近100%

5. **修改档位总数 (N)**：
   - 重新计算档位宽度：δC = Tv / N
   - 优化目标：确保劳动价值达成率接近100%

6. **修改首档奖励 (R₀)**：
   - 重新计算衰减系数：k = 1 - (R₀ / T)
   - 优化目标：确保积分释放达成率接近100%

### 参数验证规则

1. **基础验证**：
   - 总积分目标 > 0
   - 预估劳动价值 > 0
   - 衰减系数在 0-1 之间

2. **组合验证**：
   - 档位宽度 > 0（如果提供）
   - 档位总数 > 0（如果提供）
   - 首档奖励 > 0（如果提供）
   - 档位宽度 × 档位总数 ≈ 预估劳动价值（误差 < 10%）

## 结果解读

### 图表分析
- **档期奖励曲线**：显示每个档位的奖励金额，呈指数衰减趋势
- **累计奖励曲线**：显示累计发放的积分总量，逐渐趋近于目标值
- **累计劳动价值曲线**：显示累计的劳动价值，线性增长

### 关键指标
- **首档奖励**：第一个档位的奖励金额，影响整体奖励水平
- **档位总数**：达到目标99.9%所需的档位数量
- **积分释放达成率**：当前计算档位达到目标值的百分比
- **累计劳动价值**：当前档位对应的累计劳动价值
- **劳动价值达成率**：当前劳动价值占预估劳动价值的百分比
- **理论釋放总积分**：无限档期后的理论总发放量

### 数据表格
- **档位序号**：档位的编号
- **档期奖励**：该档位的奖励金额
- **累计奖励**：到该档位为止的总奖励
- **劳动价值**：该档位对应的劳动价值
- **累计劳动价值**：到该档位为止的累计劳动价值

## 应用场景

### 1. 积分系统设计
- 设计合理的积分发放机制
- 平衡早期和后期用户的奖励
- 控制总积分发放量
- 优化劳动价值捕获策略

### 2. 激励机制优化
- 分析不同衰减系数的影响
- 调整档位宽度以平衡激励效果
- 预测长期激励效果
- 评估劳动价值与奖励的匹配度

### 3. 经济模型研究
- 研究衰减型奖励机制
- 分析劳动价值与奖励的关系
- 评估系统可持续性
- 优化参数配置

### 4. 参数调优
- 根据实际需求调整档位设置
- 平衡奖励分配和系统成本
- 优化用户体验和参与度
- 制定长期发展规划

## 使用技巧

### 1. 参数设置建议
- **衰减系数**：0.99-0.999 适合长期激励，0.95-0.99 适合短期激励
- **档位宽度**：根据劳动价值分布合理设置，避免档位过密或过疏
- **档位总数**：建议 20-100 个档位，平衡计算精度和性能
- **优化策略**：系统会自动优化参数使积分释放达成率和劳动价值达成率都接近100%
- **衰减系数优化**：积分释放达成率过低时，系统会自动减小衰减系数以提高达成率

### 2. 自动计算使用
- 优先使用自动计算模式，减少手动调整
- 系统会根据当前参数自动计算最优推荐值
- 自动计算开关打开时，参数变化会自动更新推荐值
- **动态重新计算优势**：修改任何参数都会触发其他参数的智能重新计算，确保整体优化
- 自动计算开关关闭时，需要手动点击"计算推荐值"按钮
- 根据推荐值进行微调，满足特定需求
- 注意参数间的相互影响关系

### 3. 结果分析
- 关注积分释放达成率和劳动价值达成率的平衡
- 分析奖励曲线的衰减趋势是否合理
- 评估系统可持续性和用户激励效果

## 注意事项

1. **参数合理性**：衰减系数越接近1，奖励衰减越慢；越接近0，衰减越快
2. **计算精度**：系统计算到目标值的99.9%，实际应用中可根据需要调整
3. **性能考虑**：大量档位计算可能影响性能，建议合理设置参数
4. **数据导出**：当前版本支持在线查看，如需数据导出可联系开发团队
5. **参数验证**：系统会验证参数组合的合理性，避免不合理的配置

## 技术实现

- **前端框架**：React + TypeScript
- **UI组件库**：Ant Design
- **图表库**：@ant-design/plots
- **计算引擎**：JavaScript数学计算
- **数据验证**：实时参数验证和错误处理
- **智能推荐**：基于数学模型的参数自动计算

## 更新日志

### v2.2.0 (当前版本)
- 新增动态重新计算功能：用户修改任何参数时，系统基于积分释放达成率和劳动价值达成率都接近100%的原则，重新计算其他所有参数的推荐值
- 增强参数验证：实时验证用户修改参数的合理性，提供错误和警告提示
- 智能参数关联：根据参数间的数学关系，自动推导最优参数组合
- 改进用户体验：实时显示重新计算的推荐值，自动执行仿真验证

### v2.1.0
- 新增智能参数优化功能，使积分释放达成率和劳动价值达成率都接近100%
- 衰减系数纳入自动计算推荐范围
- 改进自动计算开关行为：开启时自动更新，关闭时手动触发
- 新增参数平衡优化策略，避免顾此失彼
- 增强推荐值计算逻辑，支持更智能的参数组合

### v2.0.0
- 新增预估劳动价值参数
- 支持档位宽度、档位总数、首档奖励的自动计算
- 新增累计劳动价值曲线
- 智能参数推荐功能
- 增强的参数验证和错误处理
- 改进的用户界面和交互体验

### v1.0.0
- 基础仿真功能
- 参数输入和验证
- 可视化图表展示
- 详细数据表格
- 关键指标统计 