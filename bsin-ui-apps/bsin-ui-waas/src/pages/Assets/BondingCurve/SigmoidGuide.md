# Sigmoid联合曲线仿真系统使用指南

## 概述

Sigmoid联合曲线仿真系统是一个基于sigmoid函数的积分铸造价格模型，实现价格从初始值到最终值的平滑过渡。该系统提供了完整的参数设置、仿真计算、分析功能和交互式测试，并新增了DEX滑点分析功能，为用户提供深度的流动性分析。

## 核心原理

### Sigmoid函数基础
Sigmoid函数是一种S形曲线函数，具有以下特点：
- 在两端变化缓慢，中间变化最快
- 输出值在0到1之间
- 具有良好的数学性质，适合建模价格变化

### 价格计算公式
```
中点：midPoint = cap / 2
sigmoid变换：melo = flexible × (supply - midPoint) / midPoint
sigmoid函数：deno = 1 / (1 + exp(-melo))
当前价格：price = initialPrice - (initialPrice - finalPrice) × deno
```

### DEX滑点理论
在去中心化交易所中，滑点是指实际成交价格与预期价格之间的差异：
- **铸造滑点**: 实际平均价格 > 理想价格，用户获得积分减少
- **销毁滑点**: 实际平均价格 < 理想价格，用户获得法币减少
- **滑点公式**: slippage = (actualPrice - idealPrice) / idealPrice × 100%

### 参数说明
- **cap**: 代币供应上限
- **initialPrice**: 初始价格（供应量为0时的价格）
- **finalPrice**: 最终价格（供应量达到上限时的价格）
- **flexible**: 拉伸变换值，控制曲线的陡峭程度
- **currentSupply**: 当前代币供应量

## 功能特性

### 1. 参数设置
- 支持所有核心参数的输入和验证
- 提供参数推荐值计算
- 自动参数验证和错误提示

### 2. 自动计算模式
- 开启后参数变化时自动重新计算
- 关闭后需要手动点击计算按钮
- 避免频繁计算，提升性能

### 3. 仿真计算
- 生成完整的价格曲线数据
- 计算铸造和销毁收益
- 提供详细的汇总信息

### 4. 深度分析
- **供应量利用率**: 当前供应量占总供应量的百分比
- **价格进度**: 当前价格在价格区间中的位置
- **价格变化率**: 价格对供应量的敏感度
- **价格分布**: 不同供应量下的价格和变化率
- **最优铸造建议**: 基于价格趋势的投资建议

### 5. 交互式测试
- 多种劳动价值下的铸造测试
- 多种积分数量下的销毁测试
- 实时计算平均价格和收益

### 6. DEX滑点分析（新增功能）
- **流动性深度分析**: 评估当前市场的流动性状况
- **多规模交易测试**: 小额、中额、大额、超大额交易场景
- **滑点计算**: 精确计算不同规模交易的滑点百分比
- **效率评估**: 计算交易效率损失
- **理论分析**: 提供DEX滑点理论基础和优化建议
- **详细提示**: 所有指标都配有详细的tooltip说明，帮助用户理解计算逻辑

### 7. 详细提示系统（新增功能）
- **参数说明**: 每个输入参数都有详细的tooltip说明，包括计算公式、取值范围、建议值等
- **指标解释**: 所有统计指标和表格列都有详细的tooltip，解释计算逻辑和业务含义
- **按钮提示**: 所有操作按钮都有功能说明，帮助用户理解操作效果
- **公式展示**: 在tooltip中展示相关的数学公式，便于用户理解计算原理

### 8. 可视化展示
- 多线图表显示价格、铸造收益、销毁收益
- 关键指标统计展示
- 详细的数据表格
- 滑点分析结果可视化
- 完整的tooltip提示系统，提供详细的计算说明

## 使用步骤

### 1. 基本设置
1. 输入供应上限（cap）
2. 设置初始价格（initialPrice）
3. 设置最终价格（finalPrice）
4. 调整拉伸变换值（flexible）
5. 设置当前供应量（currentSupply）

### 2. 参数优化
1. 点击"计算推荐值"获取建议参数
   - 系统优先保证积分释放达成率=100%
   - 其次保证累计劳动价值=100%
   - 自动调整首档奖励、档位总数、档位宽度、衰减系数四个参数
   - 使用迭代优化算法精确计算最优参数组合
2. 根据实际需求调整参数
3. 观察参数验证结果和警告信息
4. 查看优化效果对比，了解参数调整前后的达成率变化

### 3. 仿真分析
1. 点击"开始仿真"执行计算
2. 查看关键指标和深度分析
3. 分析价格曲线和变化趋势

### 4. 交互测试
1. 点击"交互式测试"进行功能验证
2. 测试不同场景下的铸造和销毁
3. 分析收益和价格变化

### 5. 滑点分析（新增）
1. 点击"滑点分析"执行DEX流动性分析
2. 查看不同规模交易的滑点情况
3. 分析流动性深度和交易效率
4. 参考优化建议制定交易策略
5. 悬停查看详细的计算公式和说明（tooltip提示）

### 6. 参数说明查看（新增）
1. 悬停任何参数输入框旁的图标查看详细说明
2. 悬停表格列标题查看列数据含义
3. 悬停统计指标查看计算逻辑
4. 悬停操作按钮查看功能说明

## 参数选择建议

### 拉伸变换值（flexible）
- **2-3**: 曲线平缓，适合稳定型项目
- **4-6**: 理想S曲线，平衡激励性和稳定性
- **7-8**: 曲线陡峭，适合高激励项目
- **>8**: 过于陡峭，风险较高

### 价格区间设置
- **initialPrice**: 建议设置为0.01-0.1元
- **finalPrice**: 建议设置为0.5-2.0元
- **价格倍数**: 建议控制在10-100倍之间

### 供应量规划
- **cap**: 根据项目规模和预期用户数确定
- **currentSupply**: 反映当前项目发展阶段
- **利用率**: 建议保持在20%-80%之间

## 分析指标解读

### 供应量利用率
- **<20%**: 项目早期，价格波动较大
- **20%-50%**: 快速发展期，价格稳步上升
- **50%-80%**: 成熟期，价格变化趋缓
- **>80%**: 接近上限，上涨空间有限

### 价格进度
- **<30%**: 价格处于低位，适合早期投资
- **30%-70%**: 价格适中，平衡投资时机
- **>70%**: 价格较高，需要谨慎投资

### 价格变化率
- **正值**: 价格随供应量增加而上升
- **负值**: 价格随供应量增加而下降（异常情况）
- **绝对值大小**: 反映价格敏感度

### DEX滑点分析指标（新增）

#### 流动性深度
- **流动性深度**: 当前供应量 × 当前价格，反映市场容量
- **影响阈值**: 1%供应量变化的价格影响
- **大额交易阈值**: 5%供应量变化的价格影响

#### 滑点等级
- **小额交易**: < 1% 滑点，适合日常交易
- **中额交易**: 1-5% 滑点，需要谨慎考虑
- **大额交易**: 5-15% 滑点，建议分批执行
- **超大额交易**: > 15% 滑点，风险较高

#### 交易效率
- **>95%**: 效率优秀，滑点影响很小
- **90-95%**: 效率良好，可接受范围
- **<90%**: 效率较低，建议优化交易策略

#### 计算公式说明
- **滑点公式**: slippage = (actualPrice - idealPrice) / idealPrice × 100%
- **铸造效率**: efficiency = (idealMintAmount / actualMintAmount) × 100%
- **销毁效率**: efficiency = (actualBurnValue / idealBurnValue) × 100%
- **流动性深度**: liquidity = currentSupply × currentPrice

## 最佳实践

### 1. 参数设计
- 根据项目特点选择合适的flexible值
- 设置合理的价格区间，避免过度波动
- 考虑长期发展，预留足够的供应空间

### 2. 风险控制
- 监控价格变化率，避免剧烈波动
- 关注供应量利用率，及时调整策略
- 定期评估参数合理性

### 3. 用户引导
- 提供清晰的价格变化说明
- 展示不同投资时机的收益对比
- 帮助用户理解Sigmoid曲线的特性

### 4. DEX交易策略（新增）

#### 交易规模控制
- **小额交易**: 单次交易不超过当前供应量的0.5%
- **中额交易**: 单次交易不超过当前供应量的1%
- **大额交易**: 单次交易不超过当前供应量的2%
- **超大额交易**: 需要分批执行，每批不超过1%

#### 交易时机选择
- **价格变化率低时**: 适合大额交易，滑点较小
- **流动性充足时**: 市场深度好，交易效率高
- **避开高峰期**: 避免与其他大额交易冲突

#### 分批交易策略
- **时间分散**: 将大额交易分散到多个时间段
- **规模递减**: 先执行小额交易，逐步增加规模
- **监控滑点**: 实时监控滑点变化，及时调整策略

#### 用户界面指导
- **悬停提示**: 所有指标都配有详细的tooltip说明
- **计算公式**: 滑点、效率等关键指标显示完整计算公式
- **风险提示**: 不同规模交易的风险等级和注意事项
- **优化建议**: 基于当前参数的个性化交易建议

## 常见问题

### Q: 如何选择合适的flexible值？
A: 根据项目激励需求选择，4-6是理想值，平衡激励性和稳定性。

### Q: 价格变化过于剧烈怎么办？
A: 降低flexible值或缩小价格区间，增加曲线平缓度。

### Q: 如何优化铸造时机？
A: 关注价格进度和变化率，在价格较低且变化率适中的时候铸造。

### Q: 销毁功能如何影响价格？
A: 销毁会减少供应量，导致价格上涨，但影响程度取决于当前价格变化率。

### Q: 什么是DEX滑点？如何减少滑点影响？（新增）
A: 滑点是实际成交价格与预期价格的差异。减少滑点的方法：
1. 控制交易规模，避免大额单笔交易
2. 选择流动性充足的时间进行交易
3. 采用分批交易策略，分散风险
4. 监控实时滑点数据，及时调整策略

### Q: 如何评估当前市场的流动性状况？（新增）
A: 通过滑点分析功能可以评估：
1. 流动性深度：当前供应量 × 当前价格
2. 不同规模交易的滑点情况
3. 交易效率指标
4. 市场容量和风险阈值

### Q: 如何使用tooltip提示功能？（新增）
A: 滑点分析中的所有指标都配有详细的tooltip提示：
1. 悬停在任何指标标题上查看详细说明
2. 查看完整的计算公式和计算逻辑
3. 了解不同指标的含义和影响
4. 获取风险提示和优化建议

### Q: 如何理解参数说明？（新增）
A: 系统提供了详细的tooltip说明：
1. 悬停参数输入框旁的图标查看详细说明
2. 每个参数都包含计算公式、取值范围、建议值等信息
3. 表格列标题和统计指标都有详细的计算逻辑说明
4. 操作按钮都有功能说明，帮助理解操作效果

### Q: 为什么需要详细的参数说明？（新增）
A: 详细说明的好处：
1. 帮助用户理解复杂的数学模型
2. 提供最佳实践建议，避免参数设置错误
3. 展示计算公式，便于验证和理解
4. 提高系统的易用性和专业性

### Q: 计算推荐值的优化策略是什么？（新增）
A: 系统采用多阶段优化策略：
1. **第一优先级**: 保证积分释放达成率=100%
   - **阶段1**: 调整衰减系数，保持首档奖励不变
   - **阶段2**: 如果衰减系数调整效果不好，增加首档奖励倍数（1-5倍）
   - **阶段3**: 如果仍不理想，同时调整衰减系数和首档奖励
   - 使用迭代算法在合理范围内搜索最优参数组合
2. **第二优先级**: 保证累计劳动价值=100%
   - 在档位宽度合理范围内搜索最优值
   - 调整档位宽度使劳动价值达成率接近100%
3. **微调优化**: 当两个达成率都接近100%时进行微调
   - 确保两个达成率都尽可能精确
   - 提供最优的参数组合

### Q: 如何查看优化效果？（新增）
A: 点击"计算推荐值"后会显示：
1. 推荐的具体参数值
2. 优化前后的达成率对比
3. 格式：积分释放达成率 X% → Y%, 劳动价值达成率 A% → B%
4. 帮助用户了解参数调整的效果

## 技术实现

### 核心算法
- Sigmoid函数实现
- 数值积分计算
- 参数优化算法
- 风险分析模型
- DEX滑点计算算法（新增）
- Tooltip提示系统（新增）

### 性能优化
- 延迟计算避免频繁更新
- 数据缓存减少重复计算
- 异步处理提升响应速度

### 扩展性
- 模块化设计便于功能扩展
- 参数化配置支持不同场景
- 插件化架构支持自定义算法

## 版本历史

### v1.1.0 (当前版本)
- 新增DEX滑点分析功能
- 多规模交易测试
- 流动性深度分析
- 交易效率评估
- 优化建议系统
- 完整的tooltip提示系统
- 详细的计算公式说明
- 全面的参数和指标说明
- 详细的操作按钮提示
- 优先级优化算法（积分释放达成率优先）
- 迭代参数优化功能
- 优化效果对比显示

### v1.0.0
- 基础Sigmoid曲线实现
- 参数设置和验证
- 仿真计算和分析
- 交互式测试功能
- 可视化展示

### 计划功能
- 多币种支持
- 高级分析工具
- 历史数据对比
- 预测模型
- API接口
- 实时滑点监控
- 交互式tooltip编辑器
- 自定义计算公式
- 多语言tooltip支持
- 个性化提示设置
- 多目标优化算法
- 实时参数调整建议

## 技术支持

如有问题或建议，请联系开发团队或查看相关文档。 