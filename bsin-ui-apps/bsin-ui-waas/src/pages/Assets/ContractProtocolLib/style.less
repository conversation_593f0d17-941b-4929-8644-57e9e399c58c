
.cardList {
  .card {
    :global {
      .ant-card-meta-title {
        margin-bottom: 12px;
        & > a {
          display: inline-block;
          max-width: 100%;
        }
      }
      .ant-card-body:hover {
        .ant-card-meta-title > a {
        }
      }
    }
  }
  .item {
    height: 64px;
  }

  :global {
    .ant-list .ant-list-item-content-single {
      max-width: 100%;
    }
  }
}

.extraImg {
  width: 155px;
  margin-top: -20px;
  text-align: center;
  img {
    width: 100%;
  }
}

.newButton {
  width: 100%;
  height: 201px;
}

.cardAvatar {
  width: 48px;
  height: 48px;
  border-radius: 48px;
}

.cardDescription {
  .textOverflowMulti();
}

.pageHeaderContent {
  position: relative;
}

.contentLink {
  margin-top: 16px;
  a {
    margin-right: 32px;
    img {
      width: 24px;
    }
  }
  img {
    margin-right: 8px;
    vertical-align: middle;
  }
}

.textOverflow() {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    word-break: break-all;
  }
  
  .textOverflowMulti(@line: 3, @bg: #fff) {
    position: relative;
    max-height: @line * 1.5em;
    margin-right: -1em;
    padding-right: 1em;
    overflow: hidden;
    line-height: 1.5em;
    text-align: justify;
    &::before {
      position: absolute;
      right: 14px;
      bottom: 0;
      padding: 0 1px;
      background: @bg;
      content: '...';
    }
    &::after {
      position: absolute;
      right: 14px;
      width: 1em;
      height: 1em;
      margin-top: 0.2em;
      background: white;
      content: '';
    }
  }
  
  // mixins for clearfix
  // ------------------------
  .clearfix() {
    zoom: 1;
    &::before,
    &::after {
      display: table;
      content: ' ';
    }
    &::after {
      clear: both;
      height: 0;
      font-size: 0;
      visibility: hidden;
    }
  }