[{"desc": "是否启用普通商户模式", "name": "isNormalMerchantMode", "type": "radio", "titles": "启用,停用", "values": "1,0", "verify": "required"}, {"desc": "是否启用服务商子商户模式", "name": "isIsvSubMerchantMode", "type": "radio", "titles": "启用,停用", "values": "1,0", "verify": "required"}, {"desc": "支付宝应用ID", "name": "appId", "type": "text", "verify": "required", "group": "normalMerchantParams"}, {"desc": "支付宝商户号", "name": "mchId", "type": "text", "verify": "required", "group": "normalMerchantParams"}, {"desc": "支付宝公钥", "name": "alipayPublicKey", "type": "textarea", "verify": "required", "group": "normalMerchantParams"}, {"desc": "应用私钥", "name": "appPrivateKey", "type": "textarea", "verify": "required", "group": "normalMerchantParams"}, {"desc": "支付宝网关", "name": "gateway", "type": "text", "verify": "required", "group": "normalMerchantParams"}, {"desc": "回调通知地址", "name": "notifyUrl", "type": "text", "verify": "required", "group": "normalMerchantParams"}, {"desc": "返回地址", "name": "returnUrl", "type": "text", "verify": "", "group": "normalMerchantParams"}, {"desc": "加密方式", "name": "signType", "type": "radio", "titles": "RSA,RSA2", "values": "RSA,RSA2", "verify": "required", "group": "normalMerchantParams"}, {"desc": "字符编码", "name": "charset", "type": "radio", "titles": "UTF-8,GBK", "values": "UTF-8,GBK", "verify": "required", "group": "normalMerchantParams"}, {"desc": "服务商应用ID", "name": "serviceAppId", "type": "text", "verify": "required", "group": "isvParams"}, {"desc": "服务商商户号", "name": "serviceMchId", "type": "text", "verify": "required", "group": "isvParams"}, {"desc": "子商户号", "name": "subMchId", "type": "text", "verify": "required", "group": "isvParams"}, {"desc": "服务商公钥", "name": "servicePublicKey", "type": "textarea", "verify": "required", "group": "isvParams"}, {"desc": "服务商私钥", "name": "servicePrivateKey", "type": "textarea", "verify": "required", "group": "isvParams"}, {"desc": "子商户回调地址", "name": "subNotifyUrl", "type": "text", "verify": "required", "group": "isvParams"}, {"desc": "特约商户应用ID", "name": "specialAppId", "type": "text", "verify": "required", "group": "specialMerchantParams"}, {"desc": "特约商户号", "name": "specialMchId", "type": "text", "verify": "required", "group": "specialMerchantParams"}, {"desc": "特约商户公钥", "name": "specialPublicKey", "type": "textarea", "verify": "required", "group": "specialMerchantParams"}, {"desc": "特约商户私钥", "name": "specialPrivate<PERSON>ey", "type": "textarea", "verify": "required", "group": "specialMerchantParams"}, {"desc": "特约商户回调地址", "name": "specialNotifyUrl", "type": "text", "verify": "required", "group": "specialMerchantParams"}]