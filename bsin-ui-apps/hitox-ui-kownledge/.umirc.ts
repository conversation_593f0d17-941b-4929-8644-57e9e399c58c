import routes from './config/routes'

export default {
  // 开启request
  request: {
    dataField: '', //空为拿到后端的原始数据
  },
  define: {
    // 后台访问地址 - 开发环境使用代理，生产环境使用实际地址
    // 'process.env.baseUrl': process.env.NODE_ENV === 'development' ? '' : 'http://snbb.hitox.top:31114',
    'process.env.baseUrl': 'http://127.0.0.1:9195',
    'process.env.contextPath_knowledge': '/hitox/sg/api/v1',
    'process.env.contextPath_chat': '/fe-sg/api',
    'process.env.ipfsApiUrl': process.env.HITOX_IPFS_API_URL || 'https://ipfsadmin.s11edao.com/api/v0',
    'process.env.ipfsGatewauUrl': process.env.HITOX_IPFS_GATEWAY_URL || 'https://ipfs.s11edao.com/ipfs/',
    'process.env.fileUrl': process.env.HITOX_FILE_URL || 'http://file.s11edao.com/jiujiu/',
    'process.env.bsinFileUploadUrl': process.env.HITOX_FILE_UPLOAF_URL || 'http://127.0.0.1:9195/http/upload/aliOssUpload',
    'process.env.storeMethod': '3',
    'process.env.biganH5Url': process.env.HITOX_BIGAN_H5_URL || 'http://127.0.0.1:8080/',
    'process.env.tenantAppType': 'ai',
    'process.env.webScoketUrl': process.env.HITOX_WEBSOCKET_BASE_URL || 'ws://127.0.0.1:9195/ws-upms/myWs',
  },
  model: {},
  qiankun: {
    master: {
      // 注册权限管理信息
      apps: [
        {
          name: 'bsin-ui-upms', // 唯一 id
          entry: 'http://127.0.0.1:8001', // html entry
          //  entry: 'http://copilotupms.s11edao.com',
        },
      ],
      routes: [
        {
          path: '/bsin-ui-upms',
          microApp: 'bsin-ui-upms',
        },
      ],
    },
    slave: {},
  },
  base: '/hitox-ui-knowledge/',
  routes,
  hash: true,
  history: {
    type: 'hash',
  },
}
