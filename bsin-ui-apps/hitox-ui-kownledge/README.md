# hitox-ui-knowledge

## 介绍
hitox-paas 知识库管理系统(User Permissions Management System)前端UI

## 软件架构


## 安装教程
- node版本 >= 18.0.0
~~~bash
# 切换node版本
nvm list
nvm use <version>

# 报错
error An unexpected error occurred: "https://registry.npm.taobao.org/@ant-design%2fpro-chat: certificate has expired".
yarn config list # 首先通过查看yarn的配置清单里的strict-ssl：
yarn config set strict-ssl false   # 使用命令关闭HTTPS证书检测

# 安装依赖
yarn

# 运行
yarn start

# 打包
yarn build

~~~


## 使用说明


