
const routes = [
  {
    path: '/',
    component: '@/layouts/index',
    routes: [
      {
        path: '/',
        redirect: '/home',
      },
      {
        name: '首页',
        path: '/home',
        component: '@/pages/Home/index',
      },
      {
        name: '非基座运行',
        path: '/uncontainer',
        component: '@/pages/uncontainer'
      },
      {
        name: '个人知识库',
        path: '/personal-kb',
        component: '@/pages/KnowledgeManagement/PersonalKb/index',
      },
      {
        name: '企业知识库',
        path: '/enterprise-kb',
        component: '@/pages/KnowledgeManagement/EnterpriseKb/index',
      },
      {
        name: '全模域搜索',
        path: '/full-domain-search',
        component: '@/pages/KnowledgeManagement/FullDomainSearch/index',
      },
      {
        name: '知识库广场',
        path: '/kb-plaza',
        component: '@/pages/KnowledgeManagement/KbPlaza/index',
      },
      {
        name: 'AI哨兵',
        path: '/ai-sentinel',
        component: '@/pages/SentinelGuide/AiSentinel/index',
      },
      {
        name: 'AI向导',
        path: '/ai-guide',
        component: '@/pages/SentinelGuide/AiGuide/index',
      },
      {
        name: '生成报告',
        path: '/generate-report',
        component: '@/pages/DataReport/GenerateReport/index',
      },
      {
        name: '数据图谱',
        path: '/data-graph',
        component: '@/pages/DataReport/DataGraph/index',
      },
      {
        name: '报告模板',
        path: '/report-template',
        component: '@/pages/DataReport/ReportTemplate/index',
      },
      {
        name: '文件管理',
        path: '/file-management',
        component: '@/pages/FileCenter/FileManagement/index',
      },
      {
        name: '批量操作',
        path: '/batch-operations',
        component: '@/pages/FileCenter/BatchOperations/index',
      },
      {
        name: '个人设置',
        path: '/personal-settings',
        component: '@/pages/Settings/UserSettings/index',
      },
      {
        name: '应用设置',
        path: '/app-settings',
        component: '@/pages/Settings/AppSettings/index',
      },
    ]
  }

];
export default routes;
