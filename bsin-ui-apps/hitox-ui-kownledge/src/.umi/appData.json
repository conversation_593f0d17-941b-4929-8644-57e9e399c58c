{"cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "pkg": {"name": "hitox-ui-knowledge", "private": true, "scripts": {"dev": "cross-env PORT=5555 max dev", "e2e:ci": "npm run e2e:ci:dev", "e2e:ci:dev": "cross-env PORT=8889 start-test start:all-qiankun:dev 8889 test:ci", "e2e:ci:preview": "cross-env PORT=8889 start-test start:all-qiankun:preview 8889 test:ci:preview", "e2e:local": "cross-env PORT=8889 start-test start:all-qiankun:dev 8889 test:local", "e2e:local:preview": "cross-env-shell PORT=8889 start-test start:all-qiankun:preview 8889 test:local", "preview": "cross-env PORT=5555 max preview --port 5555", "setup": "max setup", "start": "npm run dev", "start:qiankun": "cross-env PORT=7897 HOST=local.snbb.hitox.top max dev", "start:all-qiankun:dev": "pnpm umi-scripts turbo --filter @example/qiankun-* --filter @example/mf-* dev", "start:all-qiankun:preview": "pnpm umi-scripts turbo --filter @example/qiankun-* --filter @example/mf-* preview", "test:ci": "pnpm umi-scripts cypress", "test:local": "cypress open", "build": "max build"}, "dependencies": {"@ant-design/charts": "^2.4.0", "@ant-design/icons": "^5.3.7", "@ant-design/pro-table": "^3.18.2", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@tailwindcss/typography": "^0.5.16", "@umijs/max": "^4.1.10", "antd": "^5.4.7", "antd-style": "^3.6.2", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.10", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "react": "18.1.0", "react-custom-scrollbars": "^4.2.1", "react-day-picker": "^9.8.0", "react-dom": "18.1.0", "react-hook-form": "^7.61.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^3.0.3", "reactflow": "^11.11.4", "recharts": "^3.1.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2"}, "devDependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^18.3.7", "@umijs/plugin-qiankun": "^2.44.1", "cross-env": "^7.0.3", "cypress": "^12.0.0", "start-server-and-test": "^1.15.2"}}, "pkgPath": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "preset", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "preset", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/max/dist/preset": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "preset", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/max/dist/preset.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/max/dist/preset", "key": "preset"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [186]}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/react", "react-dom": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/react-dom", "react-router": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/node_modules/react-router", "react-router-dom": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/node_modules/react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 40}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 40}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {"onStart": [0]}, "register": 19}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 10}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 16}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {"modifyAppData": [7], "onStart": [0]}, "register": 15}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/plugins/dist/access": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/access.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/access", "key": "access"}, "./node_modules/@umijs/plugins/dist/analytics": {"config": {"onChange": "reload"}, "time": {"hooks": {}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/analytics.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/analytics", "key": "analytics"}, "./node_modules/@umijs/plugins/dist/antd": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/antd.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/antd", "key": "antd"}, "./node_modules/@umijs/plugins/dist/dva": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/dva.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/dva", "key": "dva"}, "./node_modules/@umijs/plugins/dist/initial-state": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/initial-state.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/initial-state", "key": "initialState"}, "./node_modules/@umijs/plugins/dist/layout": {"config": {"onChange": "regenerateTmpFiles"}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/layout.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/layout", "key": "layout"}, "./node_modules/@umijs/plugins/dist/locale": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/locale.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/locale", "key": "locale"}, "./node_modules/@umijs/plugins/dist/mf": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/mf.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/mf", "key": "mf"}, "./node_modules/@umijs/plugins/dist/model": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/model.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/model", "key": "model"}, "./node_modules/@umijs/plugins/dist/moment2dayjs": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/moment2dayjs.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/moment2dayjs", "key": "moment2dayjs"}, "./node_modules/@umijs/plugins/dist/qiankun": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/qiankun.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/qiankun", "key": "qiankun"}, "./node_modules/@umijs/plugins/dist/qiankun/master": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/qiankun/master.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/qiankun/master", "key": "<PERSON><PERSON><PERSON>n-master"}, "./node_modules/@umijs/plugins/dist/qiankun/slave": {"config": {}, "time": {"hooks": {"modifyDefaultConfig": [0]}, "register": 0}, "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/qiankun/slave.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/qiankun/slave", "key": "qiankun-slave"}, "./node_modules/@umijs/plugins/dist/react-query": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/react-query.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/react-query", "key": "reactQuery"}, "./node_modules/@umijs/plugins/dist/request": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/request.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/request", "key": "request"}, "./node_modules/@umijs/plugins/dist/styled-components": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/styled-components.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/styled-components", "key": "styledComponents"}, "./node_modules/@umijs/plugins/dist/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/tailwindcss.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/tailwindcss", "key": "tailwindcss"}, "./node_modules/@umijs/plugins/dist/valtio": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "config", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/plugins/dist/valtio.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/plugins/dist/valtio", "key": "valtio"}, "./node_modules/@umijs/max/dist/plugins/maxAlias": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/max/dist/plugins/maxAlias.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/max/dist/plugins/maxAlias", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/max/dist/plugins/maxAppData": {"config": {}, "time": {"hooks": {"modifyAppData": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/max/dist/plugins/maxAppData.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/max/dist/plugins/maxAppData", "key": "maxAppData"}, "./node_modules/@umijs/max/dist/plugins/maxChecker": {"config": {}, "time": {"hooks": {"onCheckPkgJSON": [0]}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/max/dist/plugins/maxChecker.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/max/dist/plugins/maxChecker", "key": "max<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "dev", "args": {"_": []}, "userConfig": {"request": {"dataField": ""}, "define": {"process.env.baseUrl": "http://127.0.0.1:9195", "process.env.contextPath_knowledge": "/hitox/sg/api/v1", "process.env.contextPath_chat": "/fe-sg/api", "process.env.ipfsApiUrl": "https://ipfsadmin.s11edao.com/api/v0", "process.env.ipfsGatewauUrl": "https://ipfs.s11edao.com/ipfs/", "process.env.fileUrl": "http://file.s11edao.com/jiujiu/", "process.env.bsinFileUploadUrl": "http://127.0.0.1:9195/http/upload/aliOssUpload", "process.env.storeMethod": "3", "process.env.biganH5Url": "http://127.0.0.1:8080/", "process.env.tenantAppType": "ai", "process.env.webScoketUrl": "ws://127.0.0.1:9195/ws-upms/myWs"}, "model": {}, "qiankun": {"slave": {}}, "base": "/hitox-ui-knowledge/", "routes": [{"path": "/", "component": "@/layouts/index", "routes": [{"path": "/", "redirect": "/home"}, {"name": "首页", "path": "/home", "component": "@/pages/Home/index"}, {"name": "非基座运行", "path": "/uncontainer", "component": "@/pages/uncontainer"}, {"name": "个人知识库", "path": "/personal-kb", "component": "@/pages/KnowledgeManagement/PersonalKb/index"}, {"name": "企业知识库", "path": "/enterprise-kb", "component": "@/pages/KnowledgeManagement/EnterpriseKb/index"}, {"name": "全模域搜索", "path": "/full-domain-search", "component": "@/pages/KnowledgeManagement/FullDomainSearch/index"}, {"name": "知识库广场", "path": "/kb-plaza", "component": "@/pages/KnowledgeManagement/KbPlaza/index"}, {"name": "AI哨兵", "path": "/ai-sentinel", "component": "@/pages/SentinelGuide/AiSentinel/index"}, {"name": "AI向导", "path": "/ai-guide", "component": "@/pages/SentinelGuide/AiGuide/index"}, {"name": "生成报告", "path": "/generate-report", "component": "@/pages/DataReport/GenerateReport/index"}, {"name": "数据图谱", "path": "/data-graph", "component": "@/pages/DataReport/DataGraph/index"}, {"name": "报告模板", "path": "/report-template", "component": "@/pages/DataReport/ReportTemplate/index"}, {"name": "文件管理", "path": "/file-management", "component": "@/pages/FileCenter/FileManagement/index"}, {"name": "批量操作", "path": "/batch-operations", "component": "@/pages/FileCenter/BatchOperations/index"}, {"name": "个人设置", "path": "/personal-settings", "component": "@/pages/Settings/UserSettings/index"}, {"name": "应用设置", "path": "/app-settings", "component": "@/pages/Settings/AppSettings/index"}]}], "hash": true, "history": {"type": "hash"}}, "mainConfigFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/.umirc.ts", "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "hash"}, "base": "/hitox-ui-knowledge/", "svgr": {}, "publicPath": "/", "mfsu": {"strategy": "eager", "mfName": "mf_hitox_ui_knowledge"}, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/react", "react-dom": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/react-dom", "react-router": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/node_modules/react-router", "react-router-dom": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/@umijs/preset-umi/node_modules/react-router-dom", "@": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src", "@@": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/.umi", "regenerator-runtime": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/regenerator-runtime", "@umijs/max": "@@/exports"}, "runtimePublicPath": true, "qiankun": {"slave": {"devSourceMap": true}}, "request": {"dataField": ""}, "define": {"process.env.baseUrl": "http://127.0.0.1:9195", "process.env.contextPath_knowledge": "/hitox/sg/api/v1", "process.env.contextPath_chat": "/fe-sg/api", "process.env.ipfsApiUrl": "https://ipfsadmin.s11edao.com/api/v0", "process.env.ipfsGatewauUrl": "https://ipfs.s11edao.com/ipfs/", "process.env.fileUrl": "http://file.s11edao.com/jiujiu/", "process.env.bsinFileUploadUrl": "http://127.0.0.1:9195/http/upload/aliOssUpload", "process.env.storeMethod": "3", "process.env.biganH5Url": "http://127.0.0.1:8080/", "process.env.tenantAppType": "ai", "process.env.webScoketUrl": "ws://127.0.0.1:9195/ws-upms/myWs"}, "model": {}, "routes": [{"path": "/", "component": "@/layouts/index", "routes": [{"path": "/", "redirect": "/home"}, {"name": "首页", "path": "/home", "component": "@/pages/Home/index"}, {"name": "非基座运行", "path": "/uncontainer", "component": "@/pages/uncontainer"}, {"name": "个人知识库", "path": "/personal-kb", "component": "@/pages/KnowledgeManagement/PersonalKb/index"}, {"name": "企业知识库", "path": "/enterprise-kb", "component": "@/pages/KnowledgeManagement/EnterpriseKb/index"}, {"name": "全模域搜索", "path": "/full-domain-search", "component": "@/pages/KnowledgeManagement/FullDomainSearch/index"}, {"name": "知识库广场", "path": "/kb-plaza", "component": "@/pages/KnowledgeManagement/KbPlaza/index"}, {"name": "AI哨兵", "path": "/ai-sentinel", "component": "@/pages/SentinelGuide/AiSentinel/index"}, {"name": "AI向导", "path": "/ai-guide", "component": "@/pages/SentinelGuide/AiGuide/index"}, {"name": "生成报告", "path": "/generate-report", "component": "@/pages/DataReport/GenerateReport/index"}, {"name": "数据图谱", "path": "/data-graph", "component": "@/pages/DataReport/DataGraph/index"}, {"name": "报告模板", "path": "/report-template", "component": "@/pages/DataReport/ReportTemplate/index"}, {"name": "文件管理", "path": "/file-management", "component": "@/pages/FileCenter/FileManagement/index"}, {"name": "批量操作", "path": "/batch-operations", "component": "@/pages/FileCenter/BatchOperations/index"}, {"name": "个人设置", "path": "/personal-settings", "component": "@/pages/Settings/UserSettings/index"}, {"name": "应用设置", "path": "/app-settings", "component": "@/pages/Settings/AppSettings/index"}]}], "hash": true, "targets": {"chrome": 80}}, "routes": {"1": {"path": "/", "file": "@/layouts/index.tsx", "parentId": "@@/global-layout", "id": "1", "absPath": "/", "__content": "import { Outlet, useNavigate, useLocation } from \"umi\";\nimport { ConfigProvider, Layout, Menu, Typography } from \"antd\";\nimport {\n  HomeOutlined,\n  BookOutlined,\n  TeamOutlined,\n  SearchOutlined,\n  ShopOutlined,\n  EyeOutlined,\n  CompassOutlined,\n  BarChartOutlined,\n  FileTextOutlined,\n  DatabaseOutlined,\n  FolderOutlined,\n  FileOutlined,\n  SettingOutlined,\n  UserOutlined,\n  AppstoreOutlined,\n} from \"@ant-design/icons\";\nimport { useEffect, useState, createContext } from \"react\";\n\nconst { Sider, Content } = Layout;\nconst { Title } = Typography;\n\n// 添加样式\nconst customStyles = `\n  .custom-menu-scrollbar::-webkit-scrollbar {\n    display: none;\n  }\n`;\n\n// 创建布局上下文，让子组件知道是否有布局菜单\nexport const LayoutContext = createContext({\n  hasLayoutSidebar: false,\n  isQiankun: false,\n});\n\nexport default (props) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const [isQiankun, setIsQiankun] = useState(false);\n\n  useEffect(() => {\n    // 检查是否在qiankun环境中运行\n    setIsQiankun(!!window.__POWERED_BY_QIANKUN__);\n\n    // 添加样式到head\n    const style = document.createElement(\"style\");\n    style.textContent = customStyles;\n    document.head.appendChild(style);\n\n    // 处理重复侧边栏的 JavaScript 解决方案\n    const handleDuplicateSidebars = () => {\n      // 更精确地选择侧边栏 - 只选择主要的布局侧边栏\n      const mainSidebars = document.querySelectorAll(\".main-sidebar\");\n\n      // 如果找到了 main-sidebar，只处理这些\n      if (mainSidebars.length > 1) {\n        mainSidebars.forEach((sidebar, index) => {\n          if (index > 0) {\n            (sidebar as HTMLElement).style.display = \"none\";\n          }\n        });\n      } else {\n        // 如果没有找到足够的 main-sidebar，尝试其他选择器\n        const allSidebars = document.querySelectorAll(\n          '[class*=\"upms-layout-sider\"]'\n        );\n\n        if (allSidebars.length > 1) {\n          allSidebars.forEach((sidebar, index) => {\n            if (index > 0) {\n              (sidebar as HTMLElement).style.display = \"none\";\n            }\n          });\n        }\n      }\n    };\n\n    // 延迟执行，确保DOM已经渲染\n    const timer = setTimeout(handleDuplicateSidebars, 100);\n\n    // 也在DOM变化时检查\n    const observer = new MutationObserver(handleDuplicateSidebars);\n    observer.observe(document.body, { childList: true, subtree: true });\n\n    return () => {\n      document.head.removeChild(style);\n      clearTimeout(timer);\n      observer.disconnect();\n    };\n  }, []);\n\n  const menuItems = [\n    {\n      key: \"/home\",\n      icon: <HomeOutlined />,\n      label: \"首页\",\n    },\n    {\n      key: \"knowledge-management\",\n      icon: <BookOutlined />,\n      label: \"知识管理\",\n      children: [\n        {\n          key: \"/personal-kb\",\n          icon: <UserOutlined />,\n          label: \"个人知识库\",\n        },\n        {\n          key: \"/enterprise-kb\",\n          icon: <TeamOutlined />,\n          label: \"企业知识库\",\n        },\n        {\n          key: \"/full-domain-search\",\n          icon: <SearchOutlined />,\n          label: \"全模域搜索\",\n        },\n        {\n          key: \"/kb-plaza\",\n          icon: <ShopOutlined />,\n          label: \"知识库广场\",\n        },\n      ],\n    },\n    {\n      key: \"sentinel-guide\",\n      icon: <CompassOutlined />,\n      label: \"哨兵向导\",\n      children: [\n        {\n          key: \"/ai-sentinel\",\n          icon: <EyeOutlined />,\n          label: \"AI哨兵\",\n        },\n        {\n          key: \"/ai-guide\",\n          icon: <CompassOutlined />,\n          label: \"AI向导\",\n        },\n      ],\n    },\n    {\n      key: \"data-report\",\n      icon: <BarChartOutlined />,\n      label: \"数据报告\",\n      children: [\n        {\n          key: \"/generate-report\",\n          icon: <FileTextOutlined />,\n          label: \"生成报告\",\n        },\n        {\n          key: \"/data-graph\",\n          icon: <DatabaseOutlined />,\n          label: \"数据图谱\",\n        },\n        {\n          key: \"/report-template\",\n          icon: <BookOutlined />,\n          label: \"报告模板\",\n        },\n      ],\n    },\n    {\n      key: \"file-center\",\n      icon: <FolderOutlined />,\n      label: \"文件中心\",\n      children: [\n        {\n          key: \"/file-management\",\n          icon: <FileOutlined />,\n          label: \"文件管理\",\n        },\n        {\n          key: \"/batch-operations\",\n          icon: <AppstoreOutlined />,\n          label: \"批量操作\",\n        },\n      ],\n    },\n    {\n      key: \"settings\",\n      icon: <SettingOutlined />,\n      label: \"设置\",\n      children: [\n        {\n          key: \"/personal-settings\",\n          icon: <UserOutlined />,\n          label: \"个人设置\",\n        },\n        {\n          key: \"/app-settings\",\n          icon: <AppstoreOutlined />,\n          label: \"应用设置\",\n        },\n      ],\n    },\n  ];\n\n  const handleMenuClick = ({ key }) => {\n    navigate(key);\n  };\n\n  // 如果在qiankun环境中，维持原状\n  if (isQiankun) {\n    return (\n      <ConfigProvider\n        prefixCls=\"upms\"\n        theme={{\n          token: {\n            colorPrimary: \"#a08ee8\",\n          },\n        }}\n      >\n        <LayoutContext.Provider\n          value={{ hasLayoutSidebar: false, isQiankun: true }}\n        >\n          <Outlet />\n        </LayoutContext.Provider>\n      </ConfigProvider>\n    );\n  }\n\n  // 非qiankun环境，显示带侧边栏的布局\n  return (\n    <ConfigProvider\n      prefixCls=\"upms\"\n      theme={{\n        token: {\n          colorPrimary: \"#a08ee8\",\n        },\n      }}\n    >\n      <LayoutContext.Provider\n        value={{ hasLayoutSidebar: true, isQiankun: false }}\n      >\n        <Layout style={{ minHeight: \"100vh\" }}>\n          <Sider\n            width={240}\n            theme=\"light\"\n            style={{ borderRight: \"1px solid #f0f0f0\" }}\n            id=\"main-sidebar\"\n            className=\"main-sidebar\"\n          >\n            <div style={{ padding: \"16px\", borderBottom: \"1px solid #f0f0f0\" }}>\n              <Title level={4} style={{ margin: 0, color: \"#a08ee8\" }}>\n                知识库系统\n              </Title>\n            </div>\n            <Menu\n              mode=\"inline\"\n              selectedKeys={[location.pathname]}\n              items={menuItems}\n              onClick={handleMenuClick}\n              style={{\n                border: \"none\",\n                height: \"calc(100vh - 73px)\",\n                overflowY: \"auto\",\n                scrollbarWidth: \"none\",\n                msOverflowStyle: \"none\",\n              }}\n              className=\"custom-menu-scrollbar\"\n            />\n          </Sider>\n          <Layout>\n            <Content>\n              <Outlet />\n            </Content>\n          </Layout>\n        </Layout>\n      </LayoutContext.Provider>\n    </ConfigProvider>\n  );\n};\n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/layouts/index.tsx"}, "2": {"path": "/", "redirect": "/home", "parentId": "1", "id": "2", "absPath": "/"}, "3": {"name": "首页", "path": "/home", "file": "@/pages/Home/index.tsx", "parentId": "1", "id": "3", "absPath": "/home", "__content": "import React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, Progress, Avatar, List, Badge, Tag, Table, Typography, Space, Button } from 'antd';\nimport { \n  UserOutlined, \n  TeamOutlined, \n  SettingOutlined, \n  AppstoreOutlined,\n  SafetyOutlined,\n  ClusterOutlined,\n  EyeOutlined,\n  HistoryOutlined,\n  RiseOutlined,\n  FallOutlined\n} from '@ant-design/icons';\nimport { Line, Pie, Column } from '@ant-design/charts';\n\nconst { Title, Text } = Typography;\n\nconst PermissionDashboard = () => {\n  // 模拟数据\n  const [dashboardData, setDashboardData] = useState({\n    totalUsers: 1248,\n    activeUsers: 892,\n    totalRoles: 28,\n    totalPermissions: 156,\n    totalApps: 12,\n    onlineUsers: 324\n  });\n\n  return (\n    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>\n      <div style={{ marginBottom: '24px' }}>\n        <Title level={2}>知识库系统概览</Title>\n        {/* <Text type=\"secondary\">实时监控系统状态和用户活动</Text> */}\n      </div>\n\n      {/* 统计卡片 */}\n      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"总用户数\"\n              value={dashboardData.totalUsers}\n              prefix={<UserOutlined style={{ color: '#1890ff' }} />}\n              suffix={\n                <div style={{ fontSize: '12px', color: '#52c41a' }}>\n                  <RiseOutlined /> +12%\n                </div>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"活跃用户\"\n              value={dashboardData.activeUsers}\n              prefix={<TeamOutlined style={{ color: '#52c41a' }} />}\n              suffix={\n                <div style={{ fontSize: '12px', color: '#52c41a' }}>\n                  <RiseOutlined /> +8%\n                </div>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"在线用户\"\n              value={dashboardData.onlineUsers}\n              prefix={<SafetyOutlined style={{ color: '#fa8c16' }} />}\n              suffix={\n                <div style={{ fontSize: '12px', color: '#fa8c16' }}>\n                  <FallOutlined /> -3%\n                </div>\n              }\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} lg={6}>\n          <Card>\n            <Statistic\n              title=\"系统应用\"\n              value={dashboardData.totalApps}\n              prefix={<AppstoreOutlined style={{ color: '#722ed1' }} />}\n              suffix={\n                <div style={{ fontSize: '12px', color: '#52c41a' }}>\n                  <RiseOutlined /> +2\n                </div>\n              }\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default PermissionDashboard;", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/Home/index.tsx"}, "4": {"name": "非基座运行", "path": "/uncontainer", "file": "@/pages/uncontainer.tsx", "parentId": "1", "id": "4", "absPath": "/uncontainer", "__content": "import React from 'react';\n\nexport default function Uncontainer() {\n  return (\n    <div>\n      请在基座打开子应用\n    </div>\n  );\n}\n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/uncontainer.tsx"}, "5": {"name": "个人知识库", "path": "/personal-kb", "file": "@/pages/KnowledgeManagement/PersonalKb/index.tsx", "parentId": "1", "id": "5", "absPath": "/personal-kb", "__content": "import { KnowledgeProvider } from \"@/components/Knowledge/KnowledgeContext\";\nimport KnowledgeSidebarColumn from \"@/components/Knowledge/KnowledgeSidebarColumn\";\nimport KnowledgeDocListColumn from \"@/components/Knowledge/KnowledgeDocListColumn\";\nimport KnowledgeMainContentColumn from \"@/components/Knowledge/KnowledgeMainContentColumn\";\n\nexport default function PersonalKbPage() {\n  return (\n    <KnowledgeProvider>\n      <div style={{ \n        display: 'flex', \n        height: '100vh', \n        width: '100%', \n        padding: '1rem', \n        gap: '1.5rem',\n        boxSizing: 'border-box',\n        overflow: 'hidden'\n      }}>\n        <KnowledgeSidebarColumn />\n        <KnowledgeDocListColumn />\n        <KnowledgeMainContentColumn />\n      </div>\n    </KnowledgeProvider>\n  );\n} \n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/KnowledgeManagement/PersonalKb/index.tsx"}, "6": {"name": "企业知识库", "path": "/enterprise-kb", "file": "@/pages/KnowledgeManagement/EnterpriseKb/index.tsx", "parentId": "1", "id": "6", "absPath": "/enterprise-kb", "__content": "import { KnowledgeProvider } from \"@/components/Knowledge/KnowledgeContext\";\nimport KnowledgeSidebarColumn from \"@/components/Knowledge/KnowledgeSidebarColumn\";\nimport KnowledgeDocListColumn from \"@/components/Knowledge/KnowledgeDocListColumn\";\nimport RightCard from \"./rightCard\";\n\nexport default function PersonalKbPage() {\n  return (\n    <KnowledgeProvider>\n      <div style={{ \n        display: 'flex', \n        height: '100vh', \n        width: '100%', \n        padding: '1rem', \n        gap: '1.5rem',\n        boxSizing: 'border-box',\n        overflow: 'hidden'\n      }}>\n        <KnowledgeSidebarColumn />\n        <KnowledgeDocListColumn />\n        <RightCard />\n      </div>\n    </KnowledgeProvider>\n  );\n} \n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/KnowledgeManagement/EnterpriseKb/index.tsx"}, "7": {"name": "全模域搜索", "path": "/full-domain-search", "file": "@/pages/KnowledgeManagement/FullDomainSearch/index.tsx", "parentId": "1", "id": "7", "absPath": "/full-domain-search", "__content": "import { KnowledgeProvider } from \"@/components/Knowledge/KnowledgeContext\";\nimport KnowledgeSidebarColumn from \"@/components/Knowledge/KnowledgeSidebarColumn\";\nimport KnowledgeDocListColumn from \"@/components/Knowledge/KnowledgeDocListColumn\";\nimport RightCard from \"./rightCard\";\n\nexport default function PersonalKbPage() {\n  return (\n    <KnowledgeProvider>\n      <div style={{ \n        display: 'flex', \n        height: '100vh', \n        width: '100%', \n        padding: '1rem', \n        gap: '1.5rem',\n        boxSizing: 'border-box',\n        overflow: 'hidden'\n      }}>\n        <KnowledgeSidebarColumn />\n        <KnowledgeDocListColumn />\n        <RightCard />\n      </div>\n    </KnowledgeProvider>\n  );\n} \n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/KnowledgeManagement/FullDomainSearch/index.tsx"}, "8": {"name": "知识库广场", "path": "/kb-plaza", "file": "@/pages/KnowledgeManagement/KbPlaza/index.tsx", "parentId": "1", "id": "8", "absPath": "/kb-plaza", "__content": "import { KnowledgeProvider } from \"@/components/Knowledge/KnowledgeContext\";\nimport KnowledgeSidebarColumn from \"@/components/Knowledge/KnowledgeSidebarColumn\";\nimport KnowledgeDocListColumn from \"@/components/Knowledge/KnowledgeDocListColumn\";\nimport RightCard from \"./rightCard\";\n\nexport default function PersonalKbPage() {\n  return (\n    <KnowledgeProvider>\n      <div style={{ \n        display: 'flex', \n        height: '100vh', \n        width: '100%', \n        padding: '1rem', \n        gap: '1.5rem',\n        boxSizing: 'border-box',\n        overflow: 'hidden'\n      }}>\n        <KnowledgeSidebarColumn />\n        <KnowledgeDocListColumn />\n        <RightCard />\n      </div>\n    </KnowledgeProvider>\n  );\n} \n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/KnowledgeManagement/KbPlaza/index.tsx"}, "9": {"name": "AI哨兵", "path": "/ai-sentinel", "file": "@/pages/SentinelGuide/AiSentinel/index.tsx", "parentId": "1", "id": "9", "absPath": "/ai-sentinel", "__content": "import WorkflowExample from \"@/components/workflow/example\";\n\nexport default function AiSentinelPage() {\n  return (\n      <WorkflowExample />\n  );\n} \n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/SentinelGuide/AiSentinel/index.tsx"}, "10": {"name": "AI向导", "path": "/ai-guide", "file": "@/pages/SentinelGuide/AiGuide/index.tsx", "parentId": "1", "id": "10", "absPath": "/ai-guide", "__content": "\"use client\"\nimport FlowListExample from \"@/components/flowlist/flow-list-example\";\n\n\nexport default function AiGuidePage() {\n  return <div><FlowListExample /></div>;\n} ", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/SentinelGuide/AiGuide/index.tsx"}, "11": {"name": "生成报告", "path": "/generate-report", "file": "@/pages/DataReport/GenerateReport/index.tsx", "parentId": "1", "id": "11", "absPath": "/generate-report", "__content": "export default function GenerateReportPage() {\n  return <div className=\"p-8\">生成报告页面（待实现）</div>;\n} ", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/DataReport/GenerateReport/index.tsx"}, "12": {"name": "数据图谱", "path": "/data-graph", "file": "@/pages/DataReport/DataGraph/index.tsx", "parentId": "1", "id": "12", "absPath": "/data-graph", "__content": "import React from 'react';\nimport { Card, Button, Typography } from 'antd';\nimport { ImageIcon, FileText } from 'lucide-react';\n\nconst { Title, Text } = Typography;\n\nexport default function DataGraphPage() {\n  return (\n    <div style={{ padding: '32px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>\n      <Title level={3} style={{ marginBottom: '24px' }}>数据图谱</Title>\n      <Card>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>\n          <Title level={4} style={{ margin: 0 }}>PersonCL</Title>\n          <Button \n            size=\"small\"\n            style={{ \n              backgroundColor: '#aaa1ce', \n              borderColor: '#aaa1ce',\n              color: 'white'\n            }}\n          >\n            插入知识库\n          </Button>\n        </div>\n        <div style={{\n          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',\n          borderRadius: '8px',\n          padding: '32px',\n          minHeight: '384px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          position: 'relative'\n        }}>\n          <div style={{ position: 'relative', width: '100%', height: '320px' }}>\n            {/* 文件节点 */}\n            <div style={{\n              position: 'absolute',\n              top: '48px',\n              left: '64px',\n              background: '#e3f2fd',\n              border: '2px solid #2196f3',\n              borderRadius: '12px',\n              padding: '12px',\n              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',\n              opacity: 0.85,\n              transition: 'transform 0.2s',\n              cursor: 'pointer'\n            }}>\n              <ImageIcon style={{ width: '32px', height: '32px', color: '#1976d2', display: 'block', margin: '0 auto 8px' }} />\n              <Text style={{ fontSize: '12px', fontWeight: 500, textAlign: 'center', display: 'block' }}>\n                saturation(1).png\n              </Text>\n            </div>\n\n            <div style={{\n              position: 'absolute',\n              top: '48px',\n              right: '64px',\n              background: '#e3f2fd',\n              border: '2px solid #2196f3',\n              borderRadius: '12px',\n              padding: '12px',\n              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',\n              opacity: 0.85,\n              transition: 'transform 0.2s',\n              cursor: 'pointer'\n            }}>\n              <ImageIcon style={{ width: '32px', height: '32px', color: '#1976d2', display: 'block', margin: '0 auto 8px' }} />\n              <Text style={{ fontSize: '12px', fontWeight: 500, textAlign: 'center', display: 'block' }}>\n                20250620-105529.jpg\n              </Text>\n            </div>\n\n            <div style={{\n              position: 'absolute',\n              bottom: '48px',\n              left: '64px',\n              background: '#e8f5e8',\n              border: '2px solid #4caf50',\n              borderRadius: '12px',\n              padding: '12px',\n              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',\n              opacity: 0.85,\n              transition: 'transform 0.2s',\n              cursor: 'pointer'\n            }}>\n              <FileText style={{ width: '32px', height: '32px', color: '#388e3c', display: 'block', margin: '0 auto 8px' }} />\n              <Text style={{ fontSize: '12px', fontWeight: 500, textAlign: 'center', display: 'block' }}>\n                飞行手册.pdf\n              </Text>\n            </div>\n\n            <div style={{\n              position: 'absolute',\n              bottom: '48px',\n              right: '64px',\n              background: '#f3e5f5',\n              border: '2px solid #9c27b0',\n              borderRadius: '12px',\n              padding: '12px',\n              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',\n              opacity: 0.85,\n              transition: 'transform 0.2s',\n              cursor: 'pointer'\n            }}>\n              <FileText style={{ width: '32px', height: '32px', color: '#7b1fa2', display: 'block', margin: '0 auto 8px' }} />\n              <Text style={{ fontSize: '12px', fontWeight: 500, textAlign: 'center', display: 'block' }}>\n                技术规范文档.docx\n              </Text>\n            </div>\n\n            {/* SVG连接线 */}\n            <svg style={{ position: 'absolute', inset: 0, width: '100%', height: '100%', pointerEvents: 'none' }}>\n              <defs>\n                <marker id=\"arrowhead-blue\" markerWidth=\"8\" markerHeight=\"6\" refX=\"7\" refY=\"3\" orient=\"auto\">\n                  <polygon points=\"0 0, 8 3, 0 6\" fill=\"#2196f3\" />\n                </marker>\n                <marker id=\"arrowhead-green\" markerWidth=\"8\" markerHeight=\"6\" refX=\"7\" refY=\"3\" orient=\"auto\">\n                  <polygon points=\"0 0, 8 3, 0 6\" fill=\"#4caf50\" />\n                </marker>\n                <marker id=\"arrowhead-purple\" markerWidth=\"8\" markerHeight=\"6\" refX=\"7\" refY=\"3\" orient=\"auto\">\n                  <polygon points=\"0 0, 8 3, 0 6\" fill=\"#9c27b0\" />\n                </marker>\n              </defs>\n              <path d=\"M 140 50 Q 200 30 260 50\" stroke=\"#2196f3\" strokeWidth=\"2.5\" fill=\"none\" markerEnd=\"url(#arrowhead-blue)\" />\n              <path d=\"M 140 270 Q 200 290 260 270\" stroke=\"#4caf50\" strokeWidth=\"2.5\" fill=\"none\" markerEnd=\"url(#arrowhead-green)\" />\n              <path d=\"M 80 100 Q 60 160 80 230\" stroke=\"#9c27b0\" strokeWidth=\"2\" fill=\"none\" strokeDasharray=\"4,4\" markerEnd=\"url(#arrowhead-purple)\" opacity=\"0.7\" />\n              <path d=\"M 320 100 Q 340 160 320 230\" stroke=\"#9c27b0\" strokeWidth=\"2\" fill=\"none\" strokeDasharray=\"4,4\" markerEnd=\"url(#arrowhead-purple)\" opacity=\"0.7\" />\n            </svg>\n\n            {/* 标签 */}\n            <div style={{\n              position: 'absolute',\n              top: '24px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              background: 'rgba(255,255,255,0.9)',\n              backdropFilter: 'blur(4px)',\n              padding: '6px 12px',\n              borderRadius: '16px',\n              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n              border: '1px solid #e3f2fd'\n            }}>\n              <Text style={{ fontSize: '12px', fontWeight: 500, color: '#1976d2' }}>\n                图像资源关联\n              </Text>\n            </div>\n\n            <div style={{\n              position: 'absolute',\n              bottom: '24px',\n              left: '50%',\n              transform: 'translateX(-50%)',\n              background: 'rgba(255,255,255,0.9)',\n              backdropFilter: 'blur(4px)',\n              padding: '6px 12px',\n              borderRadius: '16px',\n              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n              border: '1px solid #e8f5e8'\n            }}>\n              <Text style={{ fontSize: '12px', fontWeight: 500, color: '#388e3c' }}>\n                文档资源关联\n              </Text>\n            </div>\n\n            {/* 中心节点 */}\n            <div style={{\n              position: 'absolute',\n              top: '50%',\n              left: '50%',\n              transform: 'translate(-50%, -50%)',\n              background: 'rgba(255,255,255,0.95)',\n              backdropFilter: 'blur(4px)',\n              borderRadius: '8px',\n              padding: '16px',\n              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n              border: '1px solid #e0e0e0',\n              textAlign: 'center'\n            }}>\n              <Title level={5} style={{ margin: '0 0 4px 0', color: '#333' }}>PersonCL</Title>\n              <Text style={{ fontSize: '12px', color: '#666', display: 'block', marginBottom: '8px' }}>\n                知识图谱\n              </Text>\n              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px', fontSize: '12px', color: '#999' }}>\n                <span style={{ width: '8px', height: '8px', backgroundColor: '#2196f3', borderRadius: '50%' }}></span>\n                <span>4个文件</span>\n                <span style={{ width: '8px', height: '8px', backgroundColor: '#4caf50', borderRadius: '50%' }}></span>\n                <span>6个关联</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </Card>\n    </div>\n  );\n} \n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/DataReport/DataGraph/index.tsx"}, "13": {"name": "报告模板", "path": "/report-template", "file": "@/pages/DataReport/ReportTemplate/index.tsx", "parentId": "1", "id": "13", "absPath": "/report-template", "__content": "export default function ReportTemplatePage() {\n  return <div className=\"p-8\">报告模板页面（待实现）</div>;\n} ", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/DataReport/ReportTemplate/index.tsx"}, "14": {"name": "文件管理", "path": "/file-management", "file": "@/pages/FileCenter/FileManagement/index.tsx", "parentId": "1", "id": "14", "absPath": "/file-management", "__content": "\"use client\"\n\nimport { useState } from \"react\"\nimport { Card, But<PERSON>, Tree, Checkbox, Typography } from \"antd\"\nimport { DownOutlined, RightOutlined, FolderOpenOutlined, FileTextOutlined, FileImageOutlined, CheckOutlined } from \"@ant-design/icons\"\n\nconst { Title, Text } = Typography\n\nconst fileItems = [\n  { name: \"saturation(1).png\", type: \"image\", color: \"#1890ff\" },\n  { name: \"20250620-105529.jpg\", type: \"image\", color: \"#1890ff\" },\n  { name: \"飞行手册.pdf\", type: \"file\", color: \"#52c41a\" },\n  { name: \"技术规范文档.docx\", type: \"file\", color: \"#722ed1\" },\n]\n\nexport default function FileManagementPage() {\n  const [selectedTreeItem, setSelectedTreeItem] = useState<string>(\"\")\n  const [expandedTreeItems, setExpandedTreeItems] = useState<string[]>([\"personal-kb\", \"PersonCL\", \"enterprise-data\"])\n  const [isSelectionMode, setIsSelectionMode] = useState(false)\n  const [selectedFiles, setSelectedFiles] = useState<string[]>([])\n\n  const toggleTreeExpanded = (item: string) => {\n    setExpandedTreeItems((prev) => (prev.includes(item) ? prev.filter((i) => i !== item) : [...prev, item]))\n  }\n  \n  const toggleFileSelection = (fileName: string) => {\n    setSelectedFiles((prev) => (prev.includes(fileName) ? prev.filter((f) => f !== fileName) : [...prev, fileName]))\n  }\n\n  const renderFileIcon = (file: any) => {\n    return file.type === \"image\" ? (\n      <FileImageOutlined style={{ color: file.color, fontSize: '16px' }} />\n    ) : (\n      <FileTextOutlined style={{ color: file.color, fontSize: '16px' }} />\n    )\n  }\n\n  return (\n    <div style={{ padding: '32px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>\n      <Title level={3} style={{ marginBottom: '24px' }}>文件中心</Title>\n      <Card>\n        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>\n          <Title level={4} style={{ margin: 0 }}>文件管理</Title>\n          <Button\n            size=\"small\"\n            style={{ \n              backgroundColor: '#aaa1ce', \n              borderColor: '#aaa1ce',\n              color: 'white'\n            }}\n            onClick={() => setIsSelectionMode(!isSelectionMode)}\n          >\n            {isSelectionMode ? \"取消选择\" : \"生成报告\"}\n          </Button>\n        </div>\n        \n        <div style={{ marginBottom: '16px' }}>\n          {/* 个人知识库 */}\n          <div style={{ marginBottom: '8px' }}>\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                cursor: 'pointer',\n                padding: '8px',\n                borderRadius: '6px',\n                backgroundColor: selectedTreeItem === \"personal-kb\" ? '#e6f7ff' : 'transparent',\n                transition: 'background-color 0.2s'\n              }}\n              onClick={() => {\n                toggleTreeExpanded(\"personal-kb\")\n                setSelectedTreeItem(\"personal-kb\")\n              }}\n              onMouseEnter={(e) => {\n                if (selectedTreeItem !== \"personal-kb\") {\n                  e.currentTarget.style.backgroundColor = '#f5f5f5'\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (selectedTreeItem !== \"personal-kb\") {\n                  e.currentTarget.style.backgroundColor = 'transparent'\n                }\n              }}\n            >\n              {expandedTreeItems.includes(\"personal-kb\") ? (\n                <DownOutlined style={{ fontSize: '12px', color: '#666' }} />\n              ) : (\n                <RightOutlined style={{ fontSize: '12px', color: '#666' }} />\n              )}\n              <FolderOpenOutlined style={{ fontSize: '16px', color: '#1890ff' }} />\n              <Text strong style={{ fontSize: '14px' }}>个人知识库</Text>\n            </div>\n            \n            {expandedTreeItems.includes(\"personal-kb\") && (\n              <div style={{ marginLeft: '24px', marginTop: '8px' }}>\n                <div\n                  style={{\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '8px',\n                    cursor: 'pointer',\n                    padding: '8px',\n                    borderRadius: '6px',\n                    backgroundColor: selectedTreeItem === \"PersonCL\" ? '#e6f7ff' : 'transparent',\n                    transition: 'background-color 0.2s'\n                  }}\n                  onClick={() => {\n                    toggleTreeExpanded(\"PersonCL\")\n                    setSelectedTreeItem(\"PersonCL\")\n                  }}\n                  onMouseEnter={(e) => {\n                    if (selectedTreeItem !== \"PersonCL\") {\n                      e.currentTarget.style.backgroundColor = '#f5f5f5'\n                    }\n                  }}\n                  onMouseLeave={(e) => {\n                    if (selectedTreeItem !== \"PersonCL\") {\n                      e.currentTarget.style.backgroundColor = 'transparent'\n                    }\n                  }}\n                >\n                  {expandedTreeItems.includes(\"PersonCL\") ? (\n                    <DownOutlined style={{ fontSize: '12px', color: '#666' }} />\n                  ) : (\n                    <RightOutlined style={{ fontSize: '12px', color: '#666' }} />\n                  )}\n                  <FolderOpenOutlined style={{ fontSize: '16px', color: '#722ed1' }} />\n                  <Text strong style={{ fontSize: '14px' }}>PersonCL</Text>\n                </div>\n                \n                {expandedTreeItems.includes(\"PersonCL\") && (\n                  <div style={{ marginLeft: '24px', marginTop: '8px' }}>\n                    {fileItems.map((file, index) => (\n                      <div\n                        key={index}\n                        style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '12px',\n                          padding: '12px',\n                          backgroundColor: '#fff',\n                          borderRadius: '6px',\n                          border: '1px solid #d9d9d9',\n                          cursor: 'move',\n                          marginBottom: '8px',\n                          transition: 'all 0.2s'\n                        }}\n                        draggable=\"true\"\n                        onDragStart={(e) => {\n                          e.dataTransfer.setData(\"text/plain\", file.name)\n                          e.dataTransfer.effectAllowed = \"move\"\n                        }}\n                        onMouseEnter={(e) => {\n                          e.currentTarget.style.backgroundColor = '#fafafa'\n                        }}\n                        onMouseLeave={(e) => {\n                          e.currentTarget.style.backgroundColor = '#fff'\n                        }}\n                      >\n                        {renderFileIcon(file)}\n                        <div style={{ flex: 1 }}>\n                          <Text strong style={{ fontSize: '14px', display: 'block' }}>{file.name}</Text>\n                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginTop: '4px' }}>\n                            <span style={{ \n                              fontSize: '12px', \n                              backgroundColor: '#f0f0f0', \n                              borderRadius: '2px', \n                              padding: '2px 4px',\n                              color: '#666'\n                            }}>\n                              FILE\n                            </span>\n                            <Text style={{ fontSize: '12px', color: '#999' }}>2025/6/24</Text>\n                            <Text style={{ fontSize: '12px', color: '#52c41a' }}>已完成</Text>\n                          </div>\n                        </div>\n                        {isSelectionMode && (\n                          <Checkbox\n                            checked={selectedFiles.includes(file.name)}\n                            onChange={(e) => {\n                              e.stopPropagation()\n                              toggleFileSelection(file.name)\n                            }}\n                          />\n                        )}\n                      </div>\n                    ))}\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n          \n          {/* 企业数据 */}\n          <div style={{ marginBottom: '8px' }}>\n            <div\n              style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '8px',\n                cursor: 'pointer',\n                padding: '8px',\n                borderRadius: '6px',\n                backgroundColor: selectedTreeItem === \"enterprise-data\" ? '#e6f7ff' : 'transparent',\n                transition: 'background-color 0.2s'\n              }}\n              onClick={() => {\n                toggleTreeExpanded(\"enterprise-data\")\n                setSelectedTreeItem(\"enterprise-data\")\n              }}\n              onMouseEnter={(e) => {\n                if (selectedTreeItem !== \"enterprise-data\") {\n                  e.currentTarget.style.backgroundColor = '#f5f5f5'\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (selectedTreeItem !== \"enterprise-data\") {\n                  e.currentTarget.style.backgroundColor = 'transparent'\n                }\n              }}\n            >\n              {expandedTreeItems.includes(\"enterprise-data\") ? (\n                <DownOutlined style={{ fontSize: '12px', color: '#666' }} />\n              ) : (\n                <RightOutlined style={{ fontSize: '12px', color: '#666' }} />\n              )}\n              <FolderOpenOutlined style={{ fontSize: '16px', color: '#fa8c16' }} />\n              <Text strong style={{ fontSize: '14px' }}>企业数据</Text>\n            </div>\n            \n            {expandedTreeItems.includes(\"enterprise-data\") && (\n              <div style={{ marginLeft: '24px', marginTop: '8px' }}>\n                {[\"xxx-5月考勤记录\", \"xxx-项目实施方案\", \"公司Q1绩效考核\"].map((fileName, index) => (\n                  <div\n                    key={index}\n                    style={{\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '8px',\n                      cursor: 'pointer',\n                      padding: '8px',\n                      borderRadius: '6px',\n                      backgroundColor: selectedTreeItem === fileName ? '#e6f7ff' : 'transparent',\n                      transition: 'background-color 0.2s'\n                    }}\n                    onClick={() => setSelectedTreeItem(fileName)}\n                    onMouseEnter={(e) => {\n                      if (selectedTreeItem !== fileName) {\n                        e.currentTarget.style.backgroundColor = '#f5f5f5'\n                      }\n                    }}\n                    onMouseLeave={(e) => {\n                      if (selectedTreeItem !== fileName) {\n                        e.currentTarget.style.backgroundColor = 'transparent'\n                      }\n                    }}\n                  >\n                    <FileTextOutlined style={{ \n                      fontSize: '16px', \n                      color: index === 0 ? '#1890ff' : index === 1 ? '#52c41a' : '#722ed1' \n                    }} />\n                    <Text style={{ fontSize: '14px', flex: 1 }}>{fileName}</Text>\n                    {isSelectionMode && (\n                      <Checkbox\n                        checked={selectedFiles.includes(fileName)}\n                        onChange={(e) => {\n                          e.stopPropagation()\n                          toggleFileSelection(fileName)\n                        }}\n                      />\n                    )}\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n        \n        {/* 拖拽区域 */}\n        <div\n          style={{\n            border: '2px dashed #d9d9d9',\n            borderRadius: '8px',\n            padding: '32px',\n            textAlign: 'center',\n            color: '#999',\n            backgroundColor: '#fafafa',\n            transition: 'all 0.2s',\n            marginTop: '24px'\n          }}\n          onDragOver={(e) => {\n            e.preventDefault()\n            e.dataTransfer.dropEffect = \"move\"\n            e.currentTarget.style.borderColor = '#1890ff'\n            e.currentTarget.style.backgroundColor = '#f6ffed'\n          }}\n          onDragLeave={(e) => {\n            e.currentTarget.style.borderColor = '#d9d9d9'\n            e.currentTarget.style.backgroundColor = '#fafafa'\n          }}\n          onDrop={(e) => {\n            e.preventDefault()\n            const fileName = e.dataTransfer.getData(\"text/plain\")\n            console.log(\"文件移动:\", fileName)\n            e.currentTarget.style.borderColor = '#d9d9d9'\n            e.currentTarget.style.backgroundColor = '#fafafa'\n          }}\n        >\n          <div style={{ fontSize: '32px', marginBottom: '8px' }}>📁</div>\n          <Text style={{ display: 'block', marginBottom: '4px' }}>拖拽文件到此处进行移动</Text>\n          <Text style={{ fontSize: '12px', color: '#bfbfbf' }}>支持拖拽重新组织文件结构</Text>\n        </div>\n      </Card>\n    </div>\n  )\n} \n", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/FileCenter/FileManagement/index.tsx"}, "15": {"name": "批量操作", "path": "/batch-operations", "file": "@/pages/FileCenter/BatchOperations/index.tsx", "parentId": "1", "id": "15", "absPath": "/batch-operations", "__content": "export default function BatchOperationsPage() {\n  return <div className=\"p-8\">批量操作页面（待实现）</div>;\n} ", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/FileCenter/BatchOperations/index.tsx"}, "16": {"name": "个人设置", "path": "/personal-settings", "file": "@/pages/Settings/UserSettings/index.tsx", "parentId": "1", "id": "16", "absPath": "/personal-settings", "__content": "export default function PersonalSettingsPage() {\n  return <div className=\"p-8\">个人设置页面（待实现）</div>;\n} ", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/Settings/UserSettings/index.tsx"}, "17": {"name": "应用设置", "path": "/app-settings", "file": "@/pages/Settings/AppSettings/index.tsx", "parentId": "1", "id": "17", "absPath": "/app-settings", "__content": "export default function AppSettingsPage() {\n  return <div className=\"p-8\">应用设置页面（待实现）</div>;\n} ", "__isJSFile": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/pages/Settings/AppSettings/index.tsx"}, "@@/global-layout": {"id": "@@/global-layout", "path": "/", "file": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/layouts/index.tsx", "absPath": "/", "isLayout": true, "__absFile": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/layouts/index.tsx"}}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "yarn", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "@umijs/max", "cliName": "max"}, "bundleStatus": {"done": false}, "mfsuBundleStatus": {"done": false}, "react": {"version": "18.1.0", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/react"}, "react-dom": {"version": "18.1.0", "path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/node_modules/react-dom"}, "appJS": {"path": "/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/app.ts", "exports": ["qiankun", "request"]}, "locale": "zh-CN", "globalCSS": [], "globalJS": [], "overridesCSS": [], "bundler": "webpack", "git": {"originUrl": "*************:s11e-DAO/bsin-paas-os.git"}, "framework": "react", "typescript": {"tslibVersion": "2.8.1"}, "faviconFiles": [], "port": 8555, "host": "0.0.0.0", "ip": "************"}