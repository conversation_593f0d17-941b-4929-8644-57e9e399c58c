// @ts-nocheck
// This file is generated by Umi automatically
// DO NOT CHANGE IT MANUALLY!
import * as Plugin_0 from '/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/app.ts';
import * as Plugin_1 from '@@/core/helmet.ts';
import * as Plugin_2 from '/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/.umi/plugin-model/runtime.tsx';
import * as Plugin_3 from '/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/.umi/plugin-qiankun-master/masterRuntimePlugin.tsx';
import * as Plugin_4 from '/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/.umi/plugin-qiankun-slave/slaveRuntimePlugin.ts';
import { PluginManager } from 'umi';

function __defaultExport (obj) {
  if (obj.default) {
    return typeof obj.default === 'function' ? obj.default() :  obj.default
  }
  return obj;
}
export function getPlugins() {
  return [
    {
      apply: __defaultExport(Plugin_0),
      path: process.env.NODE_ENV === 'production' ? void 0 : '/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/app.ts',
    },
    {
      apply: Plugin_1,
      path: process.env.NODE_ENV === 'production' ? void 0 : '@@/core/helmet.ts',
    },
    {
      apply: Plugin_2,
      path: process.env.NODE_ENV === 'production' ? void 0 : '/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/.umi/plugin-model/runtime.tsx',
    },
    {
      apply: Plugin_3,
      path: process.env.NODE_ENV === 'production' ? void 0 : '/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/.umi/plugin-qiankun-master/masterRuntimePlugin.tsx',
    },
    {
      apply: Plugin_4,
      path: process.env.NODE_ENV === 'production' ? void 0 : '/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/.umi/plugin-qiankun-slave/slaveRuntimePlugin.ts',
    },
  ];
}

export function getValidKeys() {
  return ['patchRoutes','patchClientRoutes','modifyContextOpts','modifyClientRenderOpts','rootContainer','innerProvider','i18nProvider','accessProvider','dataflowProvider','outerProvider','render','onRouteChange','qiankun','useQiankunStateForSlave','request',];
}

let pluginManager = null;

export function createPluginManager() {
  pluginManager = PluginManager.create({
    plugins: getPlugins(),
    validKeys: getValidKeys(),
  });


  return pluginManager;
}

export function getPluginManager() {
  return pluginManager;
}
