// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
import React from 'react';

export async function getRoutes() {
  const routes = {"1":{"path":"/","parentId":"@@/global-layout","id":"1"},"2":{"path":"/","redirect":"/home","parentId":"1","id":"2"},"3":{"name":"首页","path":"/home","parentId":"1","id":"3"},"4":{"name":"非基座运行","path":"/uncontainer","parentId":"1","id":"4"},"5":{"name":"个人知识库","path":"/personal-kb","parentId":"1","id":"5"},"6":{"name":"企业知识库","path":"/enterprise-kb","parentId":"1","id":"6"},"7":{"name":"全模域搜索","path":"/full-domain-search","parentId":"1","id":"7"},"8":{"name":"知识库广场","path":"/kb-plaza","parentId":"1","id":"8"},"9":{"name":"AI哨兵","path":"/ai-sentinel","parentId":"1","id":"9"},"10":{"name":"AI向导","path":"/ai-guide","parentId":"1","id":"10"},"11":{"name":"生成报告","path":"/generate-report","parentId":"1","id":"11"},"12":{"name":"数据图谱","path":"/data-graph","parentId":"1","id":"12"},"13":{"name":"报告模板","path":"/report-template","parentId":"1","id":"13"},"14":{"name":"文件管理","path":"/file-management","parentId":"1","id":"14"},"15":{"name":"批量操作","path":"/batch-operations","parentId":"1","id":"15"},"16":{"name":"个人设置","path":"/personal-settings","parentId":"1","id":"16"},"17":{"name":"应用设置","path":"/app-settings","parentId":"1","id":"17"},"@@/global-layout":{"id":"@@/global-layout","path":"/","isLayout":true}} as const;
  return {
    routes,
    routeComponents: {
'1': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'@/layouts/index.tsx')),
'2': React.lazy(() => import('./EmptyRoute')),
'3': React.lazy(() => import(/* webpackChunkName: "p__Home__index" */'@/pages/Home/index.tsx')),
'4': React.lazy(() => import(/* webpackChunkName: "p__uncontainer" */'@/pages/uncontainer.tsx')),
'5': React.lazy(() => import(/* webpackChunkName: "p__KnowledgeManagement__PersonalKb__index" */'@/pages/KnowledgeManagement/PersonalKb/index.tsx')),
'6': React.lazy(() => import(/* webpackChunkName: "p__KnowledgeManagement__EnterpriseKb__index" */'@/pages/KnowledgeManagement/EnterpriseKb/index.tsx')),
'7': React.lazy(() => import(/* webpackChunkName: "p__KnowledgeManagement__FullDomainSearch__index" */'@/pages/KnowledgeManagement/FullDomainSearch/index.tsx')),
'8': React.lazy(() => import(/* webpackChunkName: "p__KnowledgeManagement__KbPlaza__index" */'@/pages/KnowledgeManagement/KbPlaza/index.tsx')),
'9': React.lazy(() => import(/* webpackChunkName: "p__SentinelGuide__AiSentinel__index" */'@/pages/SentinelGuide/AiSentinel/index.tsx')),
'10': React.lazy(() => import(/* webpackChunkName: "p__SentinelGuide__AiGuide__index" */'@/pages/SentinelGuide/AiGuide/index.tsx')),
'11': React.lazy(() => import(/* webpackChunkName: "p__DataReport__GenerateReport__index" */'@/pages/DataReport/GenerateReport/index.tsx')),
'12': React.lazy(() => import(/* webpackChunkName: "p__DataReport__DataGraph__index" */'@/pages/DataReport/DataGraph/index.tsx')),
'13': React.lazy(() => import(/* webpackChunkName: "p__DataReport__ReportTemplate__index" */'@/pages/DataReport/ReportTemplate/index.tsx')),
'14': React.lazy(() => import(/* webpackChunkName: "p__FileCenter__FileManagement__index" */'@/pages/FileCenter/FileManagement/index.tsx')),
'15': React.lazy(() => import(/* webpackChunkName: "p__FileCenter__BatchOperations__index" */'@/pages/FileCenter/BatchOperations/index.tsx')),
'16': React.lazy(() => import(/* webpackChunkName: "p__Settings__UserSettings__index" */'@/pages/Settings/UserSettings/index.tsx')),
'17': React.lazy(() => import(/* webpackChunkName: "p__Settings__AppSettings__index" */'@/pages/Settings/AppSettings/index.tsx')),
'@@/global-layout': React.lazy(() => import(/* webpackChunkName: "layouts__index" */'/Users/<USER>/IntelliJ IDEA/projects/open-source/gitee/bsin-paas-os/bsin-ui-apps/hitox-ui-kownledge/src/layouts/index.tsx')),
},
  };
}
