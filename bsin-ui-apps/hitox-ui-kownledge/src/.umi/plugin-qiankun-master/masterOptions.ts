// @ts-nocheck
// This file is generated by <PERSON><PERSON> automatically
// DO NOT CHANGE IT MANUALLY!
let options = {"masterHistoryType":"hash","base":"/hitox-ui-knowledge/","apps":[{"name":"bsin-ui-upms","entry":"http://127.0.0.1:8001"}],"routes":[{"path":"/bsin-ui-upms","microApp":"bsin-ui-upms"}]};
export const getMasterOptions = () => options;
export const setMasterOptions = (newOpts) => options = ({ ...options, ...newOpts });
