import React, { createContext, useContext, useState, Dispatch, SetStateAction } from "react";

// 只保留实际用到的字段
export interface KnowledgeBase {
  id: number;
  name: string;
  external_id: string; // 确保有 external_id 字段
  document_count: number; // 添加文档数量字段
}

export interface Document {
  id: number;
  external_id: string;
  name: string;
  document_type: string;
  updated_at: string;
  status: string;
  file_size?: number;
}

interface KnowledgeContextType {
  selectedKb: KnowledgeBase | null;
  setSelectedKb: Dispatch<SetStateAction<KnowledgeBase | null>>;
  selectedDoc: Document | null;
  setSelectedDoc: Dispatch<SetStateAction<Document | null>>;
  refreshDocuments?: () => void;
  setRefreshDocuments?: Dispatch<SetStateAction<(() => void) | undefined>>;
  refreshKnowledgeBases?: () => void;
  setRefreshKnowledgeBases?: Dispatch<SetStateAction<(() => void) | undefined>>;
}

const KnowledgeContext = createContext<KnowledgeContextType | undefined>(undefined);

export const KnowledgeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [selectedKb, setSelectedKb] = useState<KnowledgeBase | null>(null);
  const [selectedDoc, setSelectedDoc] = useState<Document | null>(null);
  const [refreshDocuments, setRefreshDocuments] = useState<(() => void) | undefined>();
  const [refreshKnowledgeBases, setRefreshKnowledgeBases] = useState<(() => void) | undefined>();
  return (
    <KnowledgeContext.Provider value={{ selectedKb, setSelectedKb, selectedDoc, setSelectedDoc, refreshDocuments, setRefreshDocuments, refreshKnowledgeBases, setRefreshKnowledgeBases }}>
      {children}
    </KnowledgeContext.Provider>
  );
};

export const useKnowledge = () => {
  const context = useContext(KnowledgeContext);
  if (!context) throw new Error("useKnowledge 必须在 KnowledgeProvider 内使用");
  return context;
}; 