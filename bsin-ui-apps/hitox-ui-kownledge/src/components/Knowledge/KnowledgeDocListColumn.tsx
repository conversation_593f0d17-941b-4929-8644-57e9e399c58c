import { Card, Button, Modal, Input, Upload, List, Popconfirm, Spin, Empty, theme } from "antd";
import { UploadOutlined, DeleteOutlined, LoadingOutlined, FileTextOutlined } from "@ant-design/icons";
import React, { Dispatch, SetStateAction, useRef, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { useKnowledge } from "./KnowledgeContext";
import { oploadFile, getDocuments, getDocument, delDocument} from "@/services/knowledge-service";

interface KnowledgeBase {
  id: number;
  name: string;
  description: string;
  document_count: number;
  created_at: string;
  updated_at: string;
  external_id: string;
  is_public: boolean;
  // 可选字段
  icon?: string | null;
  provider_id?: number;
  config?: object;
  owner_id?: number;
  is_active?: boolean;
  provider?: any;
  tags?: any[];
  category?: string;
}

interface Document {
  id: number;
  external_id: string;
  name: string;
  document_type: string;
  updated_at: string;
  status: string;
  file_size?: number;
}

interface KnowledgeDocListColumnProps {
  selectedDoc: Document | null;
  setSelectedDoc: Dispatch<SetStateAction<Document | null>>;
}

export default function KnowledgeDocListColumn() {
  const { token } = theme.useToken();
  
  // 在组件开始处添加样式
  const documentListStyles = `
    .document-list-item:hover {
      background-color: ${token.colorPrimaryBg} !important;
      border-color: ${token.colorPrimaryBorder} !important;
      box-shadow: 0 2px 8px ${token.colorPrimary}15;
      transform: translateY(-1px);
    }
    
    .document-list-item:hover .delete-button {
      opacity: 1 !important;
    }
  `;

  // 在组件开始处添加样式到head
  if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.textContent = documentListStyles;
    if (!document.head.querySelector('style[data-document-list]')) {
      styleElement.setAttribute('data-document-list', 'true');
      document.head.appendChild(styleElement);
    }
  }

  const { selectedKb, selectedDoc, setSelectedDoc, setRefreshDocuments, refreshKnowledgeBases } = useKnowledge();

  // 确保qiankun环境下样式正确应用
  const containerStyle = {
    width: '80%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column' as const,
    overflow: 'hidden'
  };

  // 本地文档状态
  const [documents, setDocuments] = React.useState<Document[]>([]);
  const [documentsLoading, setDocumentsLoading] = React.useState(false);
  const [documentsError, setDocumentsError] = React.useState<string | null>(null);
  const pollingTimers = useRef<Record<number, NodeJS.Timeout>>({});

  // 本地 UI 状态
  const [isAddContentModalOpen, setIsAddContentModalOpen] = React.useState(false);
  const [fileToUpload, setFileToUpload] = React.useState<File | null>(null);
  const [isUploading, setIsUploading] = React.useState(false);
  const [docToDelete, setDocToDelete] = React.useState<Document | null>(null);

  // 文件选择
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFileToUpload(e.target.files[0]);
    }
  };

  // 拖拽
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      setFileToUpload(e.dataTransfer.files[0]);
      e.dataTransfer.clearData();
    }
  };

  // 上传
  const handleFileUpload = async () => {
    if (!fileToUpload || !selectedKb) {
      toast.error("请选择要上传的文件。");
      return;
    }
    setIsUploading(true);
    toast.loading("正在上传文件...");
    const formData = new FormData();
    formData.append('file', fileToUpload);
    try {
      await oploadFile(selectedKb.id, formData);
      toast.dismiss();
      toast.success(`文件 "${fileToUpload.name}" 上传请求已发送，正在处理...`);
      setIsAddContentModalOpen(false);
      setFileToUpload(null);
      // 上传成功后拉取最新文档列表
      await fetchDocumentsAndPoll(selectedKb.id);
      // 通知 KnowledgeSidebarColumn 刷新知识库列表
      if (typeof refreshKnowledgeBases === 'function') {
        refreshKnowledgeBases();
      }
    } catch (error) {
      toast.dismiss();
      toast.error("文件上传失败，请重试。");
      console.error("文件上传失败:", error);
    } finally {
      setIsUploading(false);
    }
  };

  // 检查文档状态并轮询 - 使用 useCallback 避免无限循环
  const checkDocumentStatus = useCallback(async (doc: Document) => {
    if (!selectedKb) return;
    try {
      const response = await getDocument(selectedKb.id, doc.id);
      const updatedDoc = response;
      setSelectedDoc((prev) => (prev && prev.id === updatedDoc.id ? updatedDoc : prev));
      if (doc.status === 'PROCESSING' && updatedDoc.status !== 'PROCESSING') {
        toast.success(`文档 "${updatedDoc.name}" 处理完成！`);
        if (pollingTimers.current[updatedDoc.id]) {
          clearTimeout(pollingTimers.current[updatedDoc.id]);
          delete pollingTimers.current[updatedDoc.id];
        }
      }
      if (updatedDoc.status === 'PROCESSING') {
        pollingTimers.current[updatedDoc.id] = setTimeout(() => {
          checkDocumentStatus(updatedDoc);
        }, 5000);
      }
    } catch (error) {
      if (pollingTimers.current[doc.id]) {
        clearTimeout(pollingTimers.current[doc.id]);
        delete pollingTimers.current[doc.id];
      }
    }
  }, [selectedKb, setSelectedDoc]);

  // 拉取文档并轮询PROCESSING状态 - 使用 useCallback 避免无限循环
  const fetchDocumentsAndPoll = useCallback(async (kbId: number) => {
    try {
      setDocumentsLoading(true);
      setDocumentsError(null);
      const response = await getDocuments(kbId);
      const fetchedDocs = response;
      setDocuments(fetchedDocs);
      fetchedDocs.forEach((doc: Document) => {
        if (doc.status === 'PROCESSING' && !pollingTimers.current[doc.id]) {
          pollingTimers.current[doc.id] = setTimeout(() => {
            checkDocumentStatus(doc);
          }, 3000);
        }
      });
    } catch (err) {
      setDocumentsError("加载文档列表失败。");
      toast.error("加载文档列表失败。");
    } finally {
      setDocumentsLoading(false);
    }
  }, [checkDocumentStatus]);

  // 删除文档
  const handleDeleteDocument = async () => {
    if (!docToDelete || !selectedKb) return;
    try {
      toast.loading("正在删除文档...");
      await delDocument(selectedKb.id, docToDelete.id);
      await fetchDocumentsAndPoll(selectedKb.id);
      toast.dismiss();
      toast.success(`文档 "${docToDelete.name}" 删除成功！`);
    } catch (error) {
      toast.dismiss();
      toast.error("删除文档失败，请重试。");
      console.error("删除文档失败:", error);
    } finally {
      setDocToDelete(null);
    }
  };

  // 新增 handleDeleteDocumentByDoc 方法，专为卡片弹窗删除
  const handleDeleteDocumentByDoc = async (doc: Document) => {
    if (!doc || !selectedKb) return;
    try {
      toast.loading("正在删除文档...");
      await delDocument(selectedKb.id, doc.id);
      await fetchDocumentsAndPoll(selectedKb.id);
      // 通知 KnowledgeSidebarColumn 刷新知识库列表
      if (typeof refreshKnowledgeBases === 'function') {
        refreshKnowledgeBases();
      }
      toast.dismiss();
      toast.success(`文档 "${doc.name}" 删除成功！`);
    } catch (error) {
      toast.dismiss();
      toast.error("删除文档失败，请重试。");
      console.error("删除文档失败:", error);
    }
  };

  // 监听 selectedKb 变化自动拉取文档
  useEffect(() => {
    if (!selectedKb) return;
    fetchDocumentsAndPoll(selectedKb.id);
    return () => {
      Object.values(pollingTimers.current).forEach(clearTimeout);
      pollingTimers.current = {};
    };
  }, [selectedKb, fetchDocumentsAndPoll]);

  // 注册刷新方法到 context
  useEffect(() => {
    if (setRefreshDocuments) {
      setRefreshDocuments(() => {
        return () => {
          if (selectedKb) {
            fetchDocumentsAndPoll(selectedKb.id);
          }
        };
      });
    }
    
    // 清理函数
    return () => {
      if (setRefreshDocuments) {
        setRefreshDocuments(undefined);
      }
    };
  }, [selectedKb, setRefreshDocuments, fetchDocumentsAndPoll]);

  return (
    <div style={{ width: '20%', minWidth: '200px', height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      <Card 
        style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
        styles={{ body: { padding: 16, flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
          <h2 style={{ fontSize: 18, fontWeight: 'bold', color: '#333', margin: 0, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {selectedKb ? selectedKb.name : '请选择知识库'}
          </h2>
          <Input placeholder="搜索" style={{ width: 120, height: 32 }} size="small" />
        </div>

        {/* 三个主操作按钮 */}
        <div style={{ display: 'flex', gap: 8, marginBottom: 16 }}>
          <Modal
            title={`添加内容到 ${selectedKb?.name}`}
            open={isAddContentModalOpen}
            onCancel={() => setIsAddContentModalOpen(false)}
            footer={[
              <Button key="cancel" onClick={() => setIsAddContentModalOpen(false)}>
                取消
              </Button>,
              <Button 
                key="upload" 
                type="primary" 
                onClick={handleFileUpload} 
                disabled={!fileToUpload || isUploading}
                loading={isUploading}
              >
                确认上传
              </Button>
            ]}
          >
            <Upload.Dragger
              beforeUpload={(file) => {
                setFileToUpload(file);
                return false;
              }}
              showUploadList={false}
              style={{ marginBottom: 16 }}
            >
              <p style={{ fontSize: 48, color: '#999' }}>
                <UploadOutlined />
              </p>
              <p style={{ fontSize: 14, color: '#666' }}>
                {fileToUpload ? fileToUpload.name : '拖拽文件到此处或点击上传'}
              </p>
            </Upload.Dragger>
          </Modal>
          
          <Button 
            type="primary" 
            style={{ flex: 1 }}
            disabled={!selectedKb}
            onClick={() => setIsAddContentModalOpen(true)}
          >
            + 添加内容
          </Button>
          
          <Button
            type="primary" 
            style={{ flex: 1 }}
          >
            → 知识总结
          </Button>
        </div>
        
        <Button
          type="primary" 
          style={{ width: '100%', marginBottom: 16}}
        >
          合并入企业知识库
        </Button>

        <div style={{ flex: 1, overflow: 'hidden', paddingTop: 8 }}>
          {documentsLoading ? (
            <div style={{ textAlign: 'center', padding: 32 }}>
              <Spin size="large" />
              <p style={{ marginTop: 16, color: '#666' }}>文档加载中...</p>
            </div>
          ) : documentsError ? (
            <div style={{ textAlign: 'center', padding: 32, color: '#ff4d4f' }}>
              {documentsError}
            </div>
          ) : documents.length > 0 ? (
            <List
              dataSource={documents}
              style={{ height: '100%', overflowY: 'auto', paddingTop: 4 }}
              renderItem={(doc) => (
                <List.Item
                  style={{
                    padding: 12,
                    cursor: 'pointer',
                    borderRadius: 6,
                    marginBottom: 8,
                    backgroundColor: selectedDoc?.id === doc.id ? token.colorPrimaryBg : token.colorBgContainer,
                    border: selectedDoc?.id === doc.id ? `1px solid ${token.colorPrimary}` : `1px solid ${token.colorBorder}`,
                    transition: 'all 0.3s ease',
                    position: 'relative'
                  }}
                  className="document-list-item"
                  onClick={() => setSelectedDoc(doc)}
                >
                  <div style={{ width: '100%' }}>
                    {/* 上行：文件名称 + 删除按钮 */}
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      marginBottom: 8
                    }}>
                      <div style={{ display: 'flex', alignItems: 'center', flex: 1, minWidth: 0 }}>
                        <FileTextOutlined style={{ fontSize: 16, color: token.colorPrimary, marginRight: 8, flexShrink: 0 }} />
                        <span style={{ 
                          fontSize: 14, 
                          fontWeight: 500, 
                          overflow: 'hidden', 
                          textOverflow: 'ellipsis', 
                          whiteSpace: 'nowrap',
                          color: token.colorText
                        }}>
                          {doc.name}
                        </span>
                      </div>
                      
                      {/* 删除按钮 - 悬浮时显示 */}
                      <Button
                        type="text"
                        size="small"
                        icon={<DeleteOutlined />}
                        danger
                        style={{ 
                          opacity: 0,
                          transition: 'opacity 0.3s ease',
                          width: 24,
                          height: 24,
                          minWidth: 24
                        }}
                        className="delete-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          setDocToDelete(doc);
                        }}
                      />
                    </div>
                    
                    {/* 下行：文档类型 + 更新时间 + 状态 */}
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      fontSize: 12,
                      color: token.colorTextSecondary
                    }}>
                      <span style={{ textTransform: 'uppercase', fontWeight: 500 }}>
                        {doc.document_type || 'FILE'}
                      </span>
                      
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        {doc.status === 'PROCESSING' && (
                          <span style={{ color: token.colorWarning, display: 'flex', alignItems: 'center' }}>
                            <LoadingOutlined style={{ marginRight: 4 }} />
                            处理中...
                          </span>
                        )}
                        {doc.status === 'COMPLETED' && <span style={{ color: token.colorSuccess }}>已完成</span>}
                        {doc.status === 'FAILED' && <span style={{ color: token.colorError }}>失败</span>}
                        <span>{new Date(doc.updated_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                  </div>
                </List.Item>
              )}
            />
          ) : (
            <Empty 
              description="暂无文档" 
              style={{ marginTop: 64 }}
            />
          )}
        </div>
      </Card>

      {/* Delete Confirmation Modal */}
      <Modal
        title="删除文档"
        open={!!docToDelete}
        onCancel={() => setDocToDelete(null)}
        footer={[
          <Button key="cancel" onClick={() => setDocToDelete(null)}>
            取消
          </Button>,
          <Button 
            key="delete" 
            type="primary" 
            danger
            onClick={() => {
              if (docToDelete) {
                handleDeleteDocumentByDoc(docToDelete);
                setDocToDelete(null);
              }
            }}
          >
            删除
          </Button>
        ]}
        centered
      >
        <p>确定要删除文档 <strong>"{docToDelete?.name}"</strong> 吗？</p>
        <p style={{ color: token.colorError, fontSize: '14px' }}>
          此操作无法撤销，将永久删除该文档。
        </p>
      </Modal>
    </div>
  );
}
