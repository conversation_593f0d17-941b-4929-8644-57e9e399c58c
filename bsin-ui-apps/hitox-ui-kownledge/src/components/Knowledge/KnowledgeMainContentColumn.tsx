import React, { useState, useRef, useEffect } from "react";
import { Card, Button, Input, Select, Spin, Empty, List, Avatar, Tooltip, notification } from "antd";
import { LoadingOutlined, SendOutlined, FileTextOutlined, MessageOutlined, BulbOutlined, UserOutlined, PictureOutlined, DownloadOutlined } from "@ant-design/icons";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { useKnowledge } from "./KnowledgeContext";
import jsPDF from 'jspdf';
import {  buildStaticResourceUrl,getDocumentServiceUrl } from "@/services/backend-config";
import { getCurrentUser, oploadFile, getPersonalKnowledgeBases, getDocuments, getDocument, getChatAssistant, postChatAssistant, putChatAssistant,postSession,postChat} from "@/services/knowledge-service";
import DOMPurify from 'dompurify';

const { Option } = Select;
const { TextArea } = Input;

// 1. 类型定义补充
interface ChatAssistant {
  id: string;
  name: string;
  description: string;
  avatar: string;
  llm: {
    model_name: string;
    temperature: number;
    top_p: number;
    presence_penalty: number;
    frequency_penalty: number;
  };
  prompt: {
    opener: string;
    prompt: string;
    empty_response: string;
    similarity_threshold: number;
    top_n: number;
  };
  dataset_ids: string[];
}

interface KnowledgeBase {
  id: number;
  name: string;
  description?: string;
  document_count: number;
  created_at?: string;
  updated_at?: string;
  external_id: string;
  is_public?: boolean;
  icon?: string | null;
  provider_id?: number;
  config?: object;
  owner_id?: number;
  is_active?: boolean;
  provider?: any;
  tags?: any[];
  category?: string;
}

interface Document {
  id: number;
  external_id: string;
  name: string;
  document_type: string;
  updated_at: string;
  status: string;
  file_size?: number;
}

// 新增：获取当前用户
const useCurrentUser = () => {
  const [currentUser, setCurrentUser] = useState<any>(null);
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        // 使用统一的 knowledge-service 方法
        const response = await getCurrentUser();
        setCurrentUser(response.data || response);
      } catch (err) {
        setCurrentUser(null);
      }
    };
    fetchCurrentUser();
  }, []);
  return currentUser;
};

// 新增：获取个人知识库列表和刷新方法
const usePersonalKbs = () => {
  const { selectedKb, setSelectedKb } = useKnowledge() as {
    selectedKb: KnowledgeBase | null;
    setSelectedKb: React.Dispatch<React.SetStateAction<KnowledgeBase | null>>;
  };
  const [personalKbs, setPersonalKbs] = useState<KnowledgeBase[]>([]);
  const fetchKnowledgeBases = async () => {
    try {
      const data = await getPersonalKnowledgeBases();
      setPersonalKbs(data as KnowledgeBase[]);
      // if ((response.data as KnowledgeBase[]).length > 0 && !selectedKb) {
      //   setSelectedKb((response.data as KnowledgeBase[])[0]);
      // }
    } catch (err) {
      // ignore
    }
  };
  useEffect(() => { fetchKnowledgeBases(); }, []);
  return { personalKbs, setPersonalKbs, fetchKnowledgeBases };
};

// 新增：拉取文档列表和轮询
const useFetchDocumentsAndPoll = () => {
  const { selectedKb, refreshDocuments } = useKnowledge();
  const pollingTimers = useRef<Record<number, NodeJS.Timeout>>({});
  
  // 复制 checkDocumentStatus 功能
  const checkDocumentStatus = async (doc: Document) => {
    if (!selectedKb) return;
    try {
      const response = await getDocument(selectedKb.id, doc.id);
      const updatedDoc = response;
      
      // 这里需要更新文档列表，但由于 KnowledgeMainContentColumn 不直接管理文档列表
      // 我们只处理状态变化通知，实际的文档列表更新由 KnowledgeDocListColumn 处理
      if (doc.status === 'PROCESSING' && updatedDoc.status !== 'PROCESSING') {
        notification.success({
          message: '文档处理完成',
          description: `文档 "${updatedDoc.name}" 处理完成！`
        });
        if (typeof refreshDocuments === 'function') {
          // refreshDocuments();
        }
        if (pollingTimers.current[updatedDoc.id]) {
          clearTimeout(pollingTimers.current[updatedDoc.id]);
          delete pollingTimers.current[updatedDoc.id];
        }
      }

      if (updatedDoc.status === 'PROCESSING') {
        pollingTimers.current[updatedDoc.id] = setTimeout(() => {
          checkDocumentStatus(updatedDoc);
        }, 500);
      }
    } catch (error) {
      console.error(`Failed to check status for doc ${doc.id}`, error);
      if (pollingTimers.current[doc.id]) {
        clearTimeout(pollingTimers.current[doc.id]);
        delete pollingTimers.current[doc.id];
      }
    }
  };

  const fetchDocumentsAndPoll = async (kbId: number) => {
    try {
      const response = await getDocuments(kbId);
      const fetchedDocs = response;
      
      fetchedDocs.forEach((doc: Document) => {
        if (doc.status === 'PROCESSING' && !pollingTimers.current[doc.id]) {
          pollingTimers.current[doc.id] = setTimeout(() => {
            checkDocumentStatus(doc);
          }, 3000);
        }
      });
    } catch (err) {
      console.error('Failed to fetch documents:', err);
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      Object.values(pollingTimers.current).forEach(clearTimeout);
      pollingTimers.current = {};
    };
  }, []);

  return { fetchDocumentsAndPoll, checkDocumentStatus, pollingTimers };
};

// 省略类型定义，实际使用时请根据主文件props传递类型

// ReferenceItem 组件
const ReferenceItem = ({ doc, allChunks }: { doc: any; allChunks: any[] }) => {
  const fileExtension = doc.document_name.split('.').pop()?.toLowerCase();
  const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(fileExtension);
  const relevantChunks = allChunks.filter(chunk => chunk.document_id === doc.document_id);
  const publicFileUrl = `${getDocumentServiceUrl()}/document/${doc.document_id}?ext=${fileExtension}&prefix=document`;

  const renderContentPreview = () => (
    <div style={{ 
      maxWidth: 384, 
      maxHeight: 192, 
      overflowY: 'auto', 
      padding: 8, 
      fontSize: 12, 
      backgroundColor: '#1f2937', 
      color: 'white', 
      borderRadius: 4 
    }}>
      {relevantChunks.map((chunk, i) => (
        <p key={i} style={{ 
          borderBottom: relevantChunks.length > 1 && i < relevantChunks.length - 1 ? '1px solid #4b5563' : 'none',
          paddingBottom: 4, 
          marginBottom: 4 
        }}>
          {chunk.content.length > 200 ? `${chunk.content.substring(0, 200)}...` : chunk.content}
        </p>
      ))}
      {relevantChunks.length === 0 && <p>没有可用的文本预览。</p>}
    </div>
  );

  return (
    <Tooltip 
      title={renderContentPreview()}
      placement="bottom"
      getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
    >
      <a
        href={publicFileUrl}
        target="_blank"
        rel="noopener noreferrer"
        style={{
          cursor: 'pointer',
          display: 'inline-flex',
          alignItems: 'center',
          fontSize: 12,
          padding: '4px 8px',
          borderRadius: 6,
          backgroundColor: '#F3F4F6',
          color: '#374151',
          textDecoration: 'none',
          transition: 'all 0.3s ease'
        }}
      >
        {isImage ? <PictureOutlined style={{ marginRight: 4 }} /> : <FileTextOutlined style={{ marginRight: 4 }} />}
        {doc.document_name}
      </a>
    </Tooltip>
  );
};

export default function KnowledgeMainContentColumn() {
  const { selectedDoc, setSelectedDoc, selectedKb, setSelectedKb, refreshKnowledgeBases } = useKnowledge();
  // 聊天相关本地状态
  const [messages, setMessages] = useState<any[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [currentChatTitle, setCurrentChatTitle] = useState<string>('');
  const [userInput, setUserInput] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [thinkingContent, setThinkingContent] = useState<string | null>(null);
  const [isSavingConversation, setIsSavingConversation] = useState(false);
  const [selectedLlmModel, setSelectedLlmModel] = useState("qwen3:32B");
  const [chatAssistant, setChatAssistant] = useState<ChatAssistant | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // 新增依赖
  const currentUser = useCurrentUser();
  const { personalKbs, setPersonalKbs, fetchKnowledgeBases } = usePersonalKbs() as {
    personalKbs: KnowledgeBase[];
    setPersonalKbs: React.Dispatch<React.SetStateAction<KnowledgeBase[]>>;
    fetchKnowledgeBases: () => Promise<void>;
  };
  const { fetchDocumentsAndPoll, checkDocumentStatus, pollingTimers } = useFetchDocumentsAndPoll();

  // 聊天助手初始化与自动创建
  useEffect(() => {
    const getOrCreateChatAssistant = async () => {
      if (!currentUser) return;
      try {
        notification.info({
          message: '加载中',
          description: '正在加载个人聊天助手...'
        });
        const response = await getChatAssistant();
        const assistant = response;
        setChatAssistant(assistant);
        if (assistant.llm?.model_name) {
          setSelectedLlmModel(assistant.llm.model_name.split('@')[0]);
        }
        notification.success({
          message: '加载成功',
          description: `个人聊天助手 "${assistant.name}" 加载成功！`
        });
      } catch (error: any) {
        if (error.response && error.response.status === 404) {
          // 未找到，自动创建
          const firstRealKb = personalKbs.find(kb => kb.name !== `${currentUser.username}${currentUser.id}`);
          if (!firstRealKb || !firstRealKb.external_id) {
            notification.error({
              message: '创建失败',
              description: '创建聊天助手失败：找不到可用的知识库进行关联。'
            });
            return;
          }
          if (!selectedKb) setSelectedKb(firstRealKb);
          notification.info({
            message: '创建助手',
            description: `未找到个人聊天助手，正在为您创建并关联到 "${firstRealKb.name}"...`
          });
          try {
            const createResponse = await postChatAssistant({ dataset_ids: [firstRealKb.external_id] });
            const newAssistant = createResponse;
            setChatAssistant(newAssistant);
            if (newAssistant.llm?.model_name) {
              setSelectedLlmModel(newAssistant.llm.model_name.split('@')[0]);
            }
            notification.success({
              message: '创建成功',
              description: '个人聊天助手创建成功！'
            });
          } catch (createError) {
            notification.error({
              message: '创建失败',
              description: '创建个人聊天助手失败。'
            });
          }
        } else {
          notification.error({
            message: '加载失败',
            description: '加载个人聊天助手失败。'
          });
        }
      }
    };
    getOrCreateChatAssistant();
  }, [currentUser, personalKbs]);

  // 自动关联知识库
  useEffect(() => {
    if (!chatAssistant || !selectedKb || typeof selectedKb.external_id === 'undefined') return;
    const desiredDatasetId = selectedKb.document_count > 0 ? selectedKb.external_id : undefined;
    const currentDatasetId = chatAssistant.dataset_ids?.[0];
    if (desiredDatasetId !== currentDatasetId) {
      const updateAssistant = async () => {
        const payloadDatasetIds = desiredDatasetId ? [desiredDatasetId] : [];
        try {
          notification.info({
            message: '更新中',
            description: `正在更新聊天助手关联到知识库 "${selectedKb.name}"...`
          });
          await putChatAssistant(chatAssistant.id,
            { dataset_ids: payloadDatasetIds }
          );
          setChatAssistant(prev => prev ? { ...prev, dataset_ids: payloadDatasetIds } : null);
          notification.success({
            message: '更新成功',
            description: '聊天助手关联更新成功！'
          });
        } catch (error) {
          notification.error({
            message: '更新失败',
            description: '更新聊天助手关联失败。'
          });
        }
      };
      updateAssistant();
    }
  }, [selectedKb, chatAssistant]);

  // 切换模型时自动更新聊天助手
  useEffect(() => {
    if (!chatAssistant || !selectedLlmModel) return;
    const currentModelName = chatAssistant.llm?.model_name.split('@')[0];
    if (currentModelName !== selectedLlmModel) {
      const updateModel = async () => {
        try {
          notification.info({
            message: '更新模型',
            description: `正在将模型更新为 ${selectedLlmModel}...`
          });
           await putChatAssistant(chatAssistant.id,
            { llm: { model_name: selectedLlmModel } }
          );
          setChatAssistant(prev => prev ? { ...prev, llm: { ...prev.llm, model_name: selectedLlmModel } } : null);
          notification.success({
            message: '更新成功',
            description: '模型更新成功！'
          });
        } catch (error) {
          notification.error({
            message: '更新失败',
            description: '模型更新失败。'
          });
          setSelectedLlmModel(currentModelName || 'qwen3:32B');
        }
      };
      updateModel();
    }
  }, [selectedLlmModel]);

  // 新会话
  const handleNewConversation = () => {
    abortControllerRef.current?.abort();
    setMessages([]);
    setCurrentSessionId(null);
    setCurrentChatTitle('');
    setUserInput('');
    setIsStreaming(false);
    setThinkingContent(null);
  };

  const processStreamData=(rawString: string)=> {
  // 1. 按 data: 分割字符串
  const dataBlocks = rawString.split(/\ndata:/).filter(Boolean);
  
  // 2. 提取所有有效的 JSON 对象
  const jsonData = [];
  
  for (const block of dataBlocks) {
    try {
      // 移除首尾空白字符和换行符
      const trimmedBlock = block.trim().replace(/^data:/, '');
      
      // 尝试解析 JSON
      const parsed = JSON.parse(trimmedBlock);
      
      // 仅处理包含有效数据的对象
      if (parsed?.data?.answer) {
        jsonData.push(parsed);
      }
    } catch (e) {
      console.warn('解析失败的数据块:', block);
    }
  }
  
  // 3. 提取最终答案（最后一个有效回答）
  const finalAnswer = jsonData.length > 0 
    ? jsonData[jsonData.length - 1].data.answer 
    : '';
  
  // 4. 处理答案中的特殊标记和 HTML
  const processedAnswer = finalAnswer
    .replace(/##\d+\$\$/g, '') // 移除特殊标记如 ##0$$
    .replace(/\n/g, '<br>');   // 将换行符转换为 HTML 换行
  
  // 5. 安全渲染 HTML 内容
  return {
    rawAnswer: finalAnswer,
    safeHtml: DOMPurify.sanitize(processedAnswer),
    references: jsonData.flatMap(data => 
      data.data.reference?.chunks || []
    )
  };
}

  // 完整 handleSendMessage
  const handleSendMessage = async () => {
    if (!userInput.trim() || !chatAssistant || isStreaming) return;

    const newMessages = [...messages, { role: 'user', content: userInput }];
    setMessages(newMessages);

    if (!currentChatTitle) {
      setCurrentChatTitle(userInput);
    }

    const question = userInput;
    setUserInput('');
    setIsStreaming(true);
    setThinkingContent(null);
    // setCitations([]); // 如有引用相关状态可补充

    abortControllerRef.current = new AbortController();
    const signal = abortControllerRef.current.signal;

    try {
      let sessionId = currentSessionId;

      if (!sessionId) {
        const sessionResponse = await postSession({ assistantId: chatAssistant.id });        
        if (!sessionResponse) {
          throw new Error(`Failed to create session. Status: ${sessionResponse.status}`);
        }
        sessionId = sessionResponse.id;
        setCurrentSessionId(sessionId);
      }

      setMessages(prev => [...prev, { role: 'assistant', content: '' }]);
      const response = await postChat({ question, assistantId: chatAssistant.id, sessionId ,signal});
      if (!response) {
        throw new Error(`Request failed`);
      }
      const result = processStreamData(response);
      setMessages((prev) => {
        const newMessages = [...prev];
        const lastMessage = newMessages[newMessages.length - 1];
        if (lastMessage && lastMessage.role === 'assistant') {
          if (result.rawAnswer) {
            const thinkRegex = /<think>[\s\S]*?<\/think>/;
            const referenceMarkerRegex = /##\d+\$\$/g;
            const cleanedAnswer = result.rawAnswer
            .replace(thinkRegex, '')
            .replace(referenceMarkerRegex, '')
            .trim();
            lastMessage.content = cleanedAnswer;
          }
          if (result.references) {
            lastMessage.references = result.references;
            // 如有 setCitations 可补充
            }
          }
          return newMessages;
        });
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        setMessages(prev => [...prev, { role: 'assistant', content: '对话已取消。' }]);
      } else {
        const errorMessage = error instanceof Error ? error.message : '抱歉，处理您的请求时出现未知错误。';
        setMessages(prev => {
          const lastMessage = prev.length > 0 && prev[prev.length - 1].role === 'assistant'
            ? prev[prev.length - 1]
            : { role: 'assistant', content: '' };
          const updatedLastMessage = { ...lastMessage, content: `错误: ${errorMessage}` };
          const newMessages = prev.length > 0 && prev[prev.length - 1].role === 'assistant'
            ? [...prev.slice(0, -1), updatedLastMessage]
            : [...prev, updatedLastMessage];
          return newMessages;
        });
      }
    } finally {
      setIsStreaming(false);
      setThinkingContent(null);
      abortControllerRef.current = null;
    }
  };

  // 完整 handleSaveConversation
  const handleSaveConversation = async () => {
    if (messages.length === 0) {
      notification.info({
        message: '提示',
        description: '没有对话内容可保存。'
      });
      return;
    }
    if (!currentUser) {
      notification.error({
        message: '错误',
        description: '无法获取用户信息，无法保存对话。'
      });
      return;
    }
    setIsSavingConversation(true);
    const notificationKey = 'saving-conversation';
    notification.info({
      key: notificationKey,
      message: '保存中',
      description: '正在保存对话记录...',
      duration: 0
    });
    try {
      // 1. 查找"对话历史"知识库
      const specialKbName = `${currentUser.username}${currentUser.id}`;
      const historyKb = personalKbs.find((kb: any) => kb.name === specialKbName);
      if (!historyKb) {
        notification.error({
          message: '错误',
          description: '未找到"对话历史"知识库。'
        });
        setIsSavingConversation(false);
        notification.destroy(notificationKey);
        return;
      }
      // 2. 格式化消息并生成 PDF
      const pdf = new jsPDF();
      // 移除自定义字体加载，使用默认字体
      pdf.addFont(buildStaticResourceUrl('NotoSansSC-Regular.ttf'), "NotoSansSC", "normal");
      pdf.setFont("NotoSansSC");
      // pdf.setFont("helvetica"); // 使用默认字体
      const timestamp = new Date().toLocaleString('zh-CN', { hour12: false }).replace(/\//g, '-');
      let y = 15;
      const pageHeight = pdf.internal.pageSize.height;
      const margin = 10;
      const maxWidth = pdf.internal.pageSize.width - margin * 2;
      const addText = (text: string, options = {}) => {
        const lines = pdf.splitTextToSize(text, maxWidth);
        for (const line of lines) {
          if (y > pageHeight - margin) {
            pdf.addPage();
            y = margin;
          }
          pdf.text(line, margin, y, options);
          y += 7;
        }
      };
      pdf.setFontSize(16);
      addText(`对话标题: ${currentChatTitle || '未命名对话'}`);
      y += 5;
      pdf.setFontSize(10);
      addText(`保存时间: ${timestamp}`);
      y += 10;
      pdf.line(margin, y, maxWidth + margin, y);
      y += 10;
      pdf.setFontSize(12);
      messages.forEach((msg: any) => {
        const role = msg.role === 'user' ? '用户' : '机器人';
        const contentToSave = (msg.content || '').replace(/<think>[\s\S]*?<\/think>/g, '').trim();
        addText(`${role}:`);
        y += 2;
        addText(contentToSave);
        y += 8;
        addText('---');
        y += 8;
      });
      // 3. 创建 File 对象
      const filename = `历史对话 ${timestamp}.pdf`;
      const pdfBlob = pdf.output('blob');
      const fileToUpload = new File([pdfBlob], filename, { type: 'application/pdf' });
      // 4. 上传文件
      const formData = new FormData();
      formData.append('file', fileToUpload);      
      await oploadFile(historyKb.id, formData);
      notification.destroy(notificationKey);
      notification.success({
        message: '保存成功',
        description: '对话记录已成功保存到"对话历史"知识库！'
      });
      // 5. 刷新数据
      setPersonalKbs((prev: any[]) =>
        prev.map((kb: any) =>
          kb.id === historyKb.id
            ? { ...kb, document_count: kb.document_count + 1 }
            : kb
        )
      );
      if (selectedKb?.id === historyKb.id) {
        fetchDocumentsAndPoll(historyKb.id);
      }
      fetchKnowledgeBases();
      // 6. 通知 KnowledgeSidebarColumn 刷新知识库列表
      if (typeof refreshKnowledgeBases === 'function') {
        refreshKnowledgeBases();
      }
    } catch (error) {
      notification.destroy(notificationKey);
      notification.error({
        message: '保存失败',
        description: '保存对话记录失败。'
      });
    } finally {
      setIsSavingConversation(false);
    }
  };

  return (
    <div style={{ flex: 1, minWidth: 0, overflow: 'hidden' }}>
      <Card style={{ height: '100%' }} styles={{ body: { padding: 16, display: 'flex', flexDirection: 'column', height: '100%' } }}>
        {selectedDoc ? (
            <div style={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', padding: 8, borderBottom: '1px solid #f0f0f0' }}>
                <Button
                  type="primary"
                  onClick={() => setSelectedDoc(null)}
                >
                  返回对话
                </Button>
              </div>
              <div style={{ flex: 1, overflow: 'hidden' }}>
                <iframe
                  src={`${getDocumentServiceUrl()}/document/${selectedDoc.external_id}?ext=${selectedDoc.name.split('.').pop()}&prefix=document`}
                  style={{ width: '100%', height: '100%', border: 0 }}
                  title={selectedDoc.name}
                />
              </div>
            </div>
          ) : (
            // 个人知识库对话区
            <>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  {/* <MessageOutlined style={{ fontSize: 20, color: '#1890ff' }} /> */}
                  <span style={{ fontSize: 20 }}>🤖</span>
                  <span style={{ fontSize: 16, fontWeight: 500 }}>模型选择：</span>
                  <Select
                    value={selectedLlmModel}
                    onChange={setSelectedLlmModel}
                    style={{ width: 150 }}
                    size="small"
                  >
                    <Option value="qwen3:32B">Qwen3 32B</Option>
                    <Option value="gemma3:27b">Gemma3 27B</Option>
                  </Select>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Button
                    type="primary"
                    onClick={handleNewConversation}
                    disabled={isStreaming}
                  >
                    新会话
                  </Button>
                  <Button
                    type="primary"
                    onClick={handleSaveConversation}
                    disabled={messages.length === 0 || isSavingConversation}
                    loading={isSavingConversation}
                  >
                    保存讨论内容
                  </Button>
                </div>
              </div>

              <div style={{ flex: 1, overflow: 'hidden', display: 'flex', flexDirection: 'column' }}>
                <div style={{ flex: 1, overflowY: 'auto', padding: '12px', marginBottom: 16 ,backgroundColor:'#f9fafb'}}>
                  {messages.length === 0 ? (
                    <Empty 
                      description={
                        <div style={{ textAlign: 'center' }}>
                          <div style={{ fontSize: 18, fontWeight: 'bold', color: '#333', marginBottom: 8 }}>
                            开始对话
                          </div>
                          <div style={{ fontSize: 14, color: '#666' }}>
                            向您的个人助理提问
                          </div>
                        </div>
                      }
                      style={{ marginTop: 100 }}
                    />
                  ) : (
                    <List
                      dataSource={messages}
                      renderItem={(message, index) => {
                        const isLastMessage = index === messages.length - 1;
                        const isStreamingAssistantMessage = isLastMessage && message.role === 'assistant' && isStreaming;
                        const messageReferences =
                          message.role === 'assistant' &&
                          message.references &&
                          message.references.length > 0
                            ? message.references
                            : null;
                        
                        return (
                          <div key={index} style={{ marginBottom: 24 }}>
                            {isStreamingAssistantMessage && thinkingContent && (
                              <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12, marginBottom: 16 }}>
                                <Avatar 
                                  icon={<BulbOutlined />} 
                                  style={{ backgroundColor: '#722ed1' }}
                                />
                                <div style={{ 
                                  flex: 1, 
                                  maxWidth: '60%', 
                                  padding: 12, 
                                  backgroundColor: '#f6ffed', 
                                  border: '1px solid #b7eb8f',
                                  borderRadius: 8,
                                  color: '#389e0d'
                                }}>
                                  <div style={{ fontSize: 12, color: '#722ed1', marginBottom: 4 }}>思考中...</div>
                                  <ReactMarkdown remarkPlugins={[remarkGfm]}>{thinkingContent}</ReactMarkdown>
                                </div>
                              </div>
                            )}
                            
                            <div style={{ 
                              display: 'flex', 
                              flexDirection: message.role === 'user' ? 'row-reverse' : 'row',
                              alignItems: 'flex-start',
                              gap: 12
                            }}>
                              <Avatar 
                                icon={message.role === 'user' ? <UserOutlined /> :<span style={{ fontSize: 20,border: 'none' }}>🤖</span>}
                              />
                              <div style={{
                                maxWidth: '60%',
                                paddingLeft: 12,
                                paddingRight: 12,
                                borderRadius: 8,
                                backgroundColor: message.role === 'user' ? '#e6f7ff' : '#ffffff',
                                border: message.role === 'user' ? '1px solid #91d5ff' : '1px solid #f0f0f0',
                                color: message.role === 'user' ? '#0050b3' : '#333'
                              }}>
                                {isStreamingAssistantMessage && !message.content ? (
                                  <Spin indicator={<LoadingOutlined style={{ fontSize: 16, color: '#1890ff' }} />} />
                                ) : (
                                  <div>
                                    <ReactMarkdown remarkPlugins={[remarkGfm]}>{message.content}</ReactMarkdown>
                                  </div>
                                )}
                                
                                {messageReferences && (
                                  <div style={{ marginTop: 12,marginBottom: 12, paddingTop: 8, borderTop: '1px solid #f0f0f0' }}>
                                    <div style={{ fontSize: 12, color: '#666', marginBottom: 8 }}>参考资料：</div>
                                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: 4 }}>
                                      {messageReferences.map((doc: any, docIndex: number) => (
                                        <ReferenceItem 
                                          key={docIndex} 
                                          doc={doc} 
                                          allChunks={messageReferences || []} 
                                        />
                                      ))}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      }}
                    />
                  )}
                </div>

                <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: 16 }}>
                  <div style={{ display: 'flex', gap: 8 }}>
                    <TextArea
                      value={userInput}
                      onChange={(e) => setUserInput(e.target.value)}
                      placeholder="输入您的问题..."
                      autoSize={{ minRows: 1, maxRows: 4 }}
                      onPressEnter={(e) => {
                        if (!e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage();
                        }
                      }}
                      style={{ flex: 1 }}
                    />
                    <Button
                      type="primary"
                      icon={<SendOutlined />}
                      onClick={handleSendMessage}
                      disabled={!userInput.trim() || isStreaming}
                      loading={isStreaming}
                    >
                      发送
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
      </Card>
    </div>
  );
} 
