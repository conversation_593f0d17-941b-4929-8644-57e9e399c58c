import { useEffect, useState, Dispatch, SetStateAction, useMemo } from "react";
import { toast } from "sonner";
import { Card, Input, Button, Modal, Form, Space, List, Tooltip, Popconfirm, theme } from "antd";
import { ShareAltOutlined, DeleteOutlined, PlusOutlined, LoadingOutlined } from "@ant-design/icons";
import { useKnowledge } from "./KnowledgeContext";
import { getPersonalKnowledgeBases, createKnowledgeBase, deleteKnowledgeBase, getCurrentUser } from "@/services/knowledge-service";

const { TextArea } = Input;

interface User {
  id: number;
  username: string;
  email: string;
  full_name: string;
  department: string | null;
  position: string | null;
  is_active: boolean;
  owner_id?: number;
  provider?: any;
  tags?: any[];
  category?: string;
}

interface KnowledgeBase {
  id: number;
  name: string;
  description: string;
  document_count: number;
  created_at: string;
  updated_at: string;
  external_id: string;
  is_public: boolean;
  icon?: string | null;
  provider_id?: number;
  config?: object;
  owner_id?: number;
  is_active?: boolean;
  provider?: any;
  tags?: any[];
  category?: string;
}

// 导出 displayedPersonalKbs 以便外部调用
export let externalDisplayedPersonalKbs: (() => KnowledgeBase[]) | null = null;

// 只保留本地状态和 context
export default function KnowledgeSidebarColumn() {
  const { token } = theme.useToken();
  
  // 添加样式
  const listItemStyles = `
    .knowledge-list-item:hover {
      background-color: ${token.colorPrimaryBg} !important;
      transform: translateX(2px);
      box-shadow: 0 2px 8px ${token.colorPrimary}15;
    }
    
    .knowledge-list-item:hover .document-count {
      opacity: 0;
    }
    
    .knowledge-list-item:hover .action-buttons {
      opacity: 1 !important;
    }
  `;

  // 在组件开始处添加样式
  if (typeof document !== 'undefined') {
    const styleElement = document.createElement('style');
    styleElement.textContent = listItemStyles;
    if (!document.head.querySelector('style[data-knowledge-list]')) {
      styleElement.setAttribute('data-knowledge-list', 'true');
      document.head.appendChild(styleElement);
    }
  }

  // 本地状态
  const [isCreateKbModalOpen, setIsCreateKbModalOpen] = useState(false);
  const [newKbName, setNewKbName] = useState("");
  const [newKbDescription, setNewKbDescription] = useState("");
  const [isCreatingKb, setIsCreatingKb] = useState(false);
  const [personalKbs, setPersonalKbs] = useState<KnowledgeBase[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [kbToDelete, setKbToDelete] = useState<KnowledgeBase | null>(null);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const { selectedKb, setSelectedKb, setRefreshKnowledgeBases } = useKnowledge();

  // 查询
  const fetchKnowledgeBases = async () => {
    try {
      setLoading(true);
      const response = await getPersonalKnowledgeBases();
      const data = response.data || response;
      let kbsArray: KnowledgeBase[];
      
      if (Array.isArray(data)) {
        kbsArray = data;
      } else if (data && typeof data === 'object') {
        // 如果是对象，遍历其属性值转换为数组
        kbsArray = Object.values(data).filter(item => 
          item && typeof item === 'object' && 'id' in item
        ) as KnowledgeBase[];
      } else {
        kbsArray = [];
      }
      
      setPersonalKbs(kbsArray);
      setError(null);
    } catch (err) {
      setError("加载个人知识库失败，请检查后端服务是否可用。");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };
  // 赋值给外部变量
  externalDisplayedPersonalKbs = () => displayedPersonalKbs;

  // 获取当前用户
  const fetchCurrentUser = async () => {
    try {
      const response = await getCurrentUser();
      setCurrentUser(response.data || response);
    } catch (err) {
      toast.error("加载用户信息失败。");
      console.error(err);
    }
  };

  // 显示的知识库列表 - 将特殊知识库重命名为"对话历史"
  const displayedPersonalKbs = useMemo(() => {
    if (!currentUser) return personalKbs;
    const specialKbName = `${currentUser.username}${currentUser.id}`;
    const specialKb = personalKbs.find(kb => kb.name === specialKbName);
    const otherKbs = personalKbs.filter(kb => kb.name !== specialKbName);

    if (specialKb) {
      return [{ ...specialKb, name: "对话历史" }, ...otherKbs];
    }

    return personalKbs;
  }, [personalKbs, currentUser]);

  useEffect(() => {
    fetchKnowledgeBases();
    fetchCurrentUser();
  }, []);

  // 确保特殊知识库存在 - 为当前用户创建对话历史知识库
  useEffect(() => {
    const ensureSpecialKbExists = async () => {
      if (currentUser && !loading) {
        const specialKbName = `${currentUser.username}${currentUser.id}`;
        const specialKbExists = personalKbs.some(kb => kb.name === specialKbName);

        if (!specialKbExists) {
          try {
            toast.info("正在为您创建个人对话历史知识库...");
            await createKnowledgeBase({
              name: specialKbName,
              description: `用于存储用户 ${currentUser.username} 的对话历史`,
             });
            await fetchKnowledgeBases(); // 重新获取列表
          } catch (error) {
            toast.error("创建对话历史知识库失败。");
            console.error("创建对话历史知识库失败:", error);
          }
        }
      }
    };

    ensureSpecialKbExists();
  }, [currentUser, loading, personalKbs]);

  // 自动选中知识库逻辑 - 优先选中第二个知识库，如果只有一个则选中第一个
  useEffect(() => {
    if (personalKbs.length > 0 && !selectedKb) {
      // 如果有多个知识库，选中第二个（索引为1）
      if (personalKbs.length > 1) {
        setSelectedKb(personalKbs[1]);
      } else {
        // 如果只有一个知识库，选中第一个
        setSelectedKb(personalKbs[0]);
      }
    }
  }, [personalKbs, selectedKb, setSelectedKb]);

  // 创建
  const handleCreateKnowledgeBase = async () => {
    if (!newKbName.trim()) {
      toast.error("知识库名称不能为空。");
      return;
    }
    setIsCreatingKb(true);
    try {
      await createKnowledgeBase({
        name: newKbName,
        description: newKbDescription,
      });
      toast.success(`知识库 "${newKbName}" 创建成功！`);
      setIsCreateKbModalOpen(false);
      setNewKbName("");
      setNewKbDescription("");
      await fetchKnowledgeBases();
    } catch (error) {
      toast.error("创建知识库失败，请重试。");
      console.error("创建知识库失败:", error);
    } finally {
      setIsCreatingKb(false);
    }
  };

  // 删除
  const handleDeleteKnowledgeBase = async (kb: KnowledgeBase) => {
    toast.loading(`正在删除知识库 "${kb.name}"...`);
    try {
      await deleteKnowledgeBase(kb.id);
      toast.dismiss();
      toast.success("知识库删除成功！");
      await fetchKnowledgeBases();
    } catch (error) {
      toast.dismiss();
      toast.error("删除知识库失败。");
      console.error("删除知识库失败:", error);
    }
  };

  // 注册刷新知识库列表方法到 context
  useEffect(() => {
    if (setRefreshKnowledgeBases) {
      setRefreshKnowledgeBases(() => {
        return () => fetchKnowledgeBases();
      });
    }
    
    // 清理函数
    return () => {
      if (setRefreshKnowledgeBases) {
        setRefreshKnowledgeBases(undefined);
      }
    };
  }, [setRefreshKnowledgeBases]);

  return (
    <div style={{ width: '20%', minWidth: '200px', height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
      <Card 
        style={{ flex: 1, height: '100%', display: 'flex', flexDirection: 'column', overflow: 'hidden' }}
        styles={{ body: { padding: 16, flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' } }}
      >
        <Input placeholder="全模域搜索" style={{ marginBottom: 16 }} />

        {/* Personal Knowledge Base Card */}
        <Card 
          title="个人知识库" 
          size="small" 
          style={{ marginBottom: 16, height: 250 }}
          styles={{ body: { padding: 12, height: 'calc(100% - 57px)', display: 'flex', flexDirection: 'column' } }}
        >
          <div style={{ flex: 1, overflowY: 'auto', marginBottom: 12 }}>
            {loading && <div style={{ fontSize: 12, color: '#999' }}>加载中...</div>}
            {error && <div style={{ fontSize: 12, color: '#ff4d4f' }}>{error}</div>}
            {!loading && !error && displayedPersonalKbs.length > 0 ? (
              <List
                size="small"
                dataSource={displayedPersonalKbs}
                renderItem={(kb) => (
                  <List.Item
                    style={{
                      padding: '8px 12px',
                      cursor: 'pointer',
                      borderRadius: 4,
                      marginBottom: 8,
                      backgroundColor: selectedKb?.id === kb.id ? token.colorPrimaryBg : 'transparent',
                      fontWeight: selectedKb?.id === kb.id ? 'bold' : 'normal',
                      color: selectedKb?.id === kb.id ? token.colorPrimary : token.colorPrimary,
                      transition: 'all 0.3s ease',
                      position: 'relative'
                    }}
                    className="knowledge-list-item"
                    onClick={() => setSelectedKb(kb)}
                  >
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      width: '100%'
                    }}>
                      <span style={{ 
                        fontSize: 14, 
                        overflow: 'hidden', 
                        textOverflow: 'ellipsis', 
                        whiteSpace: 'nowrap', 
                        flex: 1 
                      }}>
                        {kb.name}
                      </span>
                      
                      {/* 文档数量 - 默认显示 */}
                      <span 
                        style={{ 
                          fontSize: 12, 
                          color: token.colorTextSecondary, 
                          marginLeft: 8,
                          transition: 'opacity 0.3s ease'
                        }}
                        className="document-count"
                      >
                        {kb.document_count}
                      </span>
                      
                      {/* 操作按钮 - 悬浮时显示 */}
                      <div 
                        style={{
                          display: 'flex',
                          gap: 4,
                          opacity: 0,
                          transition: 'opacity 0.3s ease',
                          position: 'absolute',
                          right: 12,
                          top: '50%',
                          transform: 'translateY(-50%)'
                        }}
                        className="action-buttons"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Tooltip title="分享">
                          <Button 
                            type="text" 
                            size="small" 
                            icon={<ShareAltOutlined />}
                            style={{ width: 24, height: 24, minWidth: 24 }}
                          />
                        </Tooltip>
                        <Tooltip title="删除">
                          <Button 
                            type="text" 
                            size="small" 
                            icon={<DeleteOutlined />} 
                            danger
                            style={{ width: 24, height: 24, minWidth: 24 }}
                            onClick={(e) => {
                              e.stopPropagation();
                              setKbToDelete(kb);
                            }}
                          />
                        </Tooltip>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            ) : !loading && !error ? (
              <div style={{ fontSize: 12, color: '#ccc' }}>暂无个人知识库</div>
            ) : null}
          </div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setIsCreateKbModalOpen(true)}
          >
            个人知识库
          </Button>
        </Card>

        {/* Enterprise Knowledge Base Card */}
        <Card 
          title="企业知识库" 
          size="small" 
          style={{ marginBottom: 16, height: 250 }}
          styles={{ body: { padding: 12, height: 'calc(100% - 57px)', display: 'flex', flexDirection: 'column' } }}
        >
          <div style={{ flex: 1, overflowY: 'auto', marginBottom: 12 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4, fontSize: 12 }}>
                <span style={{ color: '#666' }}>低空经济政策培训</span>
              </div>
              <div style={{ padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4, fontSize: 12 }}>
                <span style={{ color: '#666' }}>无人机型号查询</span>
              </div>
              <div style={{ padding: 8, backgroundColor: '#f5f5f5', borderRadius: 4, fontSize: 12 }}>
                <span style={{ color: '#666' }}>无人机飞手考试培训</span>
              </div>
            </Space>
          </div>
          <Button 
            type="default" 
          >
            更多...
          </Button>
        </Card>

        {/* Knowledge Base Plaza Card */}
        <Card 
          title="知识库广场" 
          size="small" 
          style={{ flex: 1 }}
          styles={{ body: { padding: 12 } }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <div style={{ 
              padding: 8, 
              background: 'linear-gradient(to right, #f0f9ff, #faf5ff)', 
              borderRadius: 4, 
              fontSize: 12 
            }}>
              <span style={{ color: '#666', fontWeight: 500 }}>穿越机速成</span>
            </div>
            <div style={{ 
              padding: 8, 
              background: 'linear-gradient(to right, #f0fdf4, #f0f9ff)', 
              borderRadius: 4, 
              fontSize: 12 
            }}>
              <span style={{ color: '#666', fontWeight: 500 }}>飞控系统推荐</span>
            </div>
          </Space>
        </Card>
      </Card>

      {/* Create Knowledge Base Modal */}
      <Modal
        title="创建个人知识库"
        open={isCreateKbModalOpen}
        onCancel={() => setIsCreateKbModalOpen(false)}
        footer={[
          <Button key="cancel" onClick={() => setIsCreateKbModalOpen(false)}>
            取消
          </Button>,
          <Button 
            key="submit" 
            type="primary" 
            loading={isCreatingKb}
            onClick={handleCreateKnowledgeBase}
          >
            创建
          </Button>
        ]}
      >
        <Form layout="vertical">
          <Form.Item label="名称" required>
            <Input
              value={newKbName}
              onChange={(e) => setNewKbName(e.target.value)}
              placeholder="例如：项目文档"
            />
          </Form.Item>
          <Form.Item label="描述">
            <TextArea
              value={newKbDescription}
              onChange={(e) => setNewKbDescription(e.target.value)}
              placeholder="（可选）简要描述知识库的用途"
              rows={3}
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        title="删除知识库"
        open={!!kbToDelete}
        onCancel={() => setKbToDelete(null)}
        footer={[
          <Button key="cancel" onClick={() => setKbToDelete(null)}>
            取消
          </Button>,
          <Button 
            key="delete" 
            type="primary" 
            danger
            onClick={() => {
              if (kbToDelete) {
                handleDeleteKnowledgeBase(kbToDelete);
                setKbToDelete(null);
              }
            }}
          >
            删除
          </Button>
        ]}
        centered
      >
        <p>确定要删除知识库 <strong>"{kbToDelete?.name}"</strong> 吗？</p>
        <p style={{ color: '#ff4d4f', fontSize: '14px' }}>
          此操作无法撤销，将永久删除该知识库及其所有文档。
        </p>
      </Modal>
    </div>
  );
} 
