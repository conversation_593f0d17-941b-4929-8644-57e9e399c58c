# FlowList 工作流列表组件

FlowList 是一个用于展示和管理工作流的卡片式列表组件，提供了现代化的UI设计和丰富的交互功能。

## 功能特性

- 🎨 **现代化卡片设计** - 采用卡片式布局，视觉效果清晰美观
- 🔍 **智能搜索** - 支持按名称、描述和标签搜索工作流
- 🏷️ **状态筛选** - 可按活跃、草稿、已归档等状态筛选
- 📊 **多种排序** - 支持按更新时间、创建时间、名称排序
- ⚡ **快速操作** - 编辑、复制、删除等操作按钮
- 📱 **响应式设计** - 适配不同屏幕尺寸
- 🎯 **状态管理** - 显示工作流状态和节点数量
- 🖼️ **缩略图支持** - 支持工作流缩略图显示

## 组件结构

```
FlowList/
├── flow-list.tsx          # 主组件文件
├── flow-list.css          # 样式文件
├── flow-list-demo.tsx     # 演示文件
└── README.md             # 说明文档
```

## 使用方法

### 基础用法

```tsx
import FlowList, { FlowItem } from './components/flow-list';

const MyComponent = () => {
  const handleFlowSelect = (flow: FlowItem) => {
    console.log('选择工作流:', flow);
  };

  const handleFlowEdit = (flow: FlowItem) => {
    console.log('编辑工作流:', flow);
  };

  const handleFlowDelete = (flowId: string) => {
    console.log('删除工作流:', flowId);
  };

  const handleFlowDuplicate = (flow: FlowItem) => {
    console.log('复制工作流:', flow);
  };

  return (
    <FlowList
      onFlowSelect={handleFlowSelect}
      onFlowEdit={handleFlowEdit}
      onFlowDelete={handleFlowDelete}
      onFlowDuplicate={handleFlowDuplicate}
    />
  );
};
```

### 与工作流编辑器集成

```tsx
import React, { useState } from 'react';
import FlowList, { FlowItem } from './components/flow-list';
import Workflow from '../index';

const WorkflowApp = () => {
  const [currentView, setCurrentView] = useState<'list' | 'editor'>('list');
  const [selectedFlow, setSelectedFlow] = useState<FlowItem | null>(null);

  const handleFlowSelect = (flow: FlowItem) => {
    setSelectedFlow(flow);
    setCurrentView('editor');
  };

  if (currentView === 'editor') {
    return (
      <div>
        {/* 编辑器界面 */}
        <Workflow
          nodes={[]}
          edges={[]}
          onNodesChange={(changes) => console.log('节点变化:', changes)}
          onEdgesChange={(changes) => console.log('边变化:', changes)}
        />
      </div>
    );
  }

  return (
    <FlowList
      onFlowSelect={handleFlowSelect}
      onFlowEdit={handleFlowSelect}
      onFlowDelete={(flowId) => console.log('删除:', flowId)}
      onFlowDuplicate={(flow) => console.log('复制:', flow)}
    />
  );
};
```

## API 接口

### FlowItem 接口

```typescript
interface FlowItem {
  id: string;                    // 工作流唯一标识
  name: string;                  // 工作流名称
  description: string;           // 工作流描述
  createdAt: string;            // 创建时间
  updatedAt: string;            // 更新时间
  nodeCount: number;            // 节点数量
  status: 'active' | 'draft' | 'archived';  // 状态
  tags: string[];               // 标签数组
  thumbnail?: string;           // 缩略图URL（可选）
}
```

### FlowListProps 接口

```typescript
interface FlowListProps {
  onFlowSelect?: (flow: FlowItem) => void;    // 选择工作流回调
  onFlowEdit?: (flow: FlowItem) => void;      // 编辑工作流回调
  onFlowDelete?: (flowId: string) => void;    // 删除工作流回调
  onFlowDuplicate?: (flow: FlowItem) => void; // 复制工作流回调
}
```

## 样式定制

组件使用CSS模块化设计，主要样式类包括：

- `.flow-list-container` - 主容器
- `.flow-list-header` - 头部工具栏
- `.flow-grid` - 卡片网格
- `.flow-card` - 工作流卡片
- `.flow-thumbnail` - 缩略图区域
- `.flow-card-content` - 卡片内容
- `.flow-tags` - 标签区域
- `.flow-meta` - 元信息区域

可以通过覆盖这些CSS类来自定义样式。

## 示例数据

组件内置了示例数据，包括：

1. **智能客服工作流** - 处理客户咨询、投诉和建议
2. **订单处理流程** - 自动化订单接收、处理和配送
3. **用户注册验证** - 新用户注册、邮箱验证和账户激活
4. **数据备份恢复** - 定期数据备份和灾难恢复
5. **营销活动管理** - 营销活动创建、执行和效果分析

## 响应式设计

组件支持响应式设计：

- **桌面端** - 多列网格布局，完整功能
- **平板端** - 自适应列数，保持功能完整
- **移动端** - 单列布局，优化触摸交互

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 开发说明

### 本地开发

1. 确保已安装依赖：
```bash
npm install
```

2. 运行演示：
```bash
npm run dev
```

3. 查看组件效果：
```bash
# 查看基础演示
http://localhost:3000/flow-list-demo

# 查看完整集成演示
http://localhost:3000/flow-list-example
```

### 自定义开发

如需添加新功能或修改现有功能：

1. 修改 `flow-list.tsx` 中的组件逻辑
2. 更新 `flow-list.css` 中的样式
3. 在 `flow-list-demo.tsx` 中测试新功能
4. 更新此文档说明新功能

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进组件功能。

## 许可证

MIT License 