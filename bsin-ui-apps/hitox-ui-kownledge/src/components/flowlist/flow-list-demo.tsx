import React from 'react';
import FlowList, { FlowItem } from './flow-list';

const FlowListDemo: React.FC = () => {
  const handleFlowSelect = (flow: FlowItem) => {
    console.log('选择工作流:', flow);
    alert(`选择了工作流: ${flow.name}`);
  };

  const handleFlowEdit = (flow: FlowItem) => {
    console.log('编辑工作流:', flow);
    alert(`编辑工作流: ${flow.name}`);
  };

  const handleFlowDelete = (flowId: string) => {
    console.log('删除工作流:', flowId);
    if (confirm('确定要删除这个工作流吗？')) {
      alert(`删除工作流: ${flowId}`);
    }
  };

  const handleFlowDuplicate = (flow: FlowItem) => {
    console.log('复制工作流:', flow);
    alert(`复制工作流: ${flow.name}`);
  };

  return (
    <div style={{ height: '100vh', overflow: 'auto' }}>
      <FlowList
        onFlowSelect={handleFlowSelect}
        onFlowEdit={handleFlowEdit}
        onFlowDelete={handleFlowDelete}
        onFlowDuplicate={handleFlowDuplicate}
      />
    </div>
  );
};

export default FlowListDemo; 