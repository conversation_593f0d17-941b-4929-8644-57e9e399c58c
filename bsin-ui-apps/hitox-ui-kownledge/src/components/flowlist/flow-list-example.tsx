import React, { useState } from 'react';
import FlowList, { FlowItem } from './flow-list';
import Workflow from '../workflow';

const FlowListExample: React.FC = () => {
  const [currentView, setCurrentView] = useState<'list' | 'editor'>('list');
  const [selectedFlow, setSelectedFlow] = useState<FlowItem | null>(null);

  // mock 节点和边数据（每个主题唯一且结构各异）
  const mockFlowData: Record<string, { nodes: any[]; edges: any[] }> = {
    '1': {
      // 智能客服工作流
      nodes: [
        { id: 'start', type: 'custom-node', position: { x: 100, y: 200 }, data: { title: '用户咨询', type: 'start', desc: '接收用户问题' } },
        { id: 'question-classifier', type: 'custom-node', position: { x: 300, y: 200 }, data: { title: '问题分类', type: 'question-classifier', desc: '智能分类' } },
        { id: 'sentiment', type: 'custom-node', position: { x: 500, y: 120 }, data: { title: '情感分析', type: 'llm', desc: '分析情感' } },
        { id: 'priority', type: 'custom-node', position: { x: 500, y: 280 }, data: { title: '优先级评估', type: 'variable-assigner', desc: '评估优先级' } },
        { id: 'router', type: 'custom-node', position: { x: 700, y: 200 }, data: { title: '问题路由', type: 'if-else', desc: '分流' } },
        { id: 'tech', type: 'custom-node', position: { x: 900, y: 120 }, data: { title: '技术问题', type: 'knowledge-retrieval', desc: '技术知识检索' } },
        { id: 'product', type: 'custom-node', position: { x: 900, y: 200 }, data: { title: '产品咨询', type: 'knowledge-retrieval', desc: '产品知识检索' } },
        { id: 'complaint', type: 'custom-node', position: { x: 900, y: 280 }, data: { title: '投诉建议', type: 'parameter-extractor', desc: '投诉信息提取' } },
      ],
      edges: [
        { id: 'e1', source: 'start', target: 'question-classifier' },
        { id: 'e2', source: 'question-classifier', target: 'sentiment' },
        { id: 'e3', source: 'question-classifier', target: 'priority' },
        { id: 'e4', source: 'sentiment', target: 'router' },
        { id: 'e5', source: 'priority', target: 'router' },
        { id: 'e6', source: 'router', target: 'tech' },
        { id: 'e7', source: 'router', target: 'product' },
        { id: 'e8', source: 'router', target: 'complaint' },
      ],
    },
    '2': {
      // 订单处理流程
      nodes: [
        { id: 'order-receive', type: 'custom-node', position: { x: 100, y: 200 }, data: { title: '订单接收', type: 'start', desc: '接收订单' } },
        { id: 'order-validate', type: 'custom-node', position: { x: 300, y: 200 }, data: { title: '订单校验', type: 'llm', desc: '校验信息' } },
        { id: 'stock-check', type: 'custom-node', position: { x: 500, y: 120 }, data: { title: '库存检查', type: 'code', desc: '检查库存' } },
        { id: 'payment', type: 'custom-node', position: { x: 500, y: 280 }, data: { title: '支付处理', type: 'llm', desc: '处理支付' } },
        { id: 'delivery', type: 'custom-node', position: { x: 700, y: 200 }, data: { title: '发货', type: 'tool', desc: '安排发货' } },
        { id: 'complete', type: 'custom-node', position: { x: 900, y: 200 }, data: { title: '订单完成', type: 'end', desc: '订单流程结束' } },
      ],
      edges: [
        { id: 'e1', source: 'order-receive', target: 'order-validate' },
        { id: 'e2', source: 'order-validate', target: 'stock-check' },
        { id: 'e3', source: 'order-validate', target: 'payment' },
        { id: 'e4', source: 'stock-check', target: 'delivery' },
        { id: 'e5', source: 'payment', target: 'delivery' },
        { id: 'e6', source: 'delivery', target: 'complete' },
      ],
    },
    '3': {
      // 用户注册验证
      nodes: [
        { id: 'register', type: 'custom-node', position: { x: 100, y: 200 }, data: { title: '注册信息填写', type: 'start', desc: '填写注册信息' } },
        { id: 'email-verify', type: 'custom-node', position: { x: 300, y: 200 }, data: { title: '邮箱验证', type: 'llm', desc: '验证邮箱' } },
        { id: 'profile', type: 'custom-node', position: { x: 500, y: 120 }, data: { title: '资料补全', type: 'parameter-extractor', desc: '补全资料' } },
        { id: 'register-success', type: 'custom-node', position: { x: 700, y: 200 }, data: { title: '注册成功', type: 'end', desc: '注册完成' } },
      ],
      edges: [
        { id: 'e1', source: 'register', target: 'email-verify' },
        { id: 'e2', source: 'email-verify', target: 'profile' },
        { id: 'e3', source: 'profile', target: 'register-success' },
      ],
    },
    '4': {
      // 数据备份恢复
      nodes: [
        { id: 'plan', type: 'custom-node', position: { x: 100, y: 200 }, data: { title: '备份计划', type: 'start', desc: '制定备份计划' } },
        { id: 'snapshot', type: 'custom-node', position: { x: 300, y: 120 }, data: { title: '数据快照', type: 'llm', desc: '生成快照' } },
        { id: 'backup', type: 'custom-node', position: { x: 300, y: 280 }, data: { title: '备份存储', type: 'knowledge-retrieval', desc: '存储数据' } },
        { id: 'restore-check', type: 'custom-node', position: { x: 500, y: 120 }, data: { title: '恢复检测', type: 'if-else', desc: '检测可恢复性' } },
        { id: 'restore', type: 'custom-node', position: { x: 700, y: 200 }, data: { title: '恢复执行', type: 'end', desc: '执行恢复' } },
      ],
      edges: [
        { id: 'e1', source: 'plan', target: 'snapshot' },
        { id: 'plan2', source: 'plan', target: 'backup' },
        { id: 'e2', source: 'snapshot', target: 'restore-check' },
        { id: 'e3', source: 'backup', target: 'restore-check' },
        { id: 'e4', source: 'restore-check', target: 'restore' },
      ],
    },
    '5': {
      // 营销活动管理
      nodes: [
        { id: 'create', type: 'custom-node', position: { x: 100, y: 200 }, data: { title: '活动创建', type: 'start', desc: '新建活动' } },
        { id: 'target', type: 'custom-node', position: { x: 300, y: 120 }, data: { title: '目标用户筛选', type: 'llm', desc: '筛选用户' } },
        { id: 'content', type: 'custom-node', position: { x: 300, y: 280 }, data: { title: '内容生成', type: 'template-transform', desc: '生成内容' } },
        { id: 'push', type: 'custom-node', position: { x: 500, y: 200 }, data: { title: '活动推送', type: 'tool', desc: '推送活动' } },
        { id: 'analyze', type: 'custom-node', position: { x: 700, y: 200 }, data: { title: '效果分析', type: 'end', desc: '分析效果' } },
      ],
      edges: [
        { id: 'e1', source: 'create', target: 'target' },
        { id: 'e2', source: 'create', target: 'content' },
        { id: 'e3', source: 'target', target: 'push' },
        { id: 'e4', source: 'content', target: 'push' },
        { id: 'e5', source: 'push', target: 'analyze' },
      ],
    },
  };

  // 优化所有mockFlowData节点的间距
  Object.values(mockFlowData).forEach((flow, idx) => {
    flow.nodes.forEach((node, i) => {
      node.position.x = 100 + i * 250;
      // 分层布局：同一流程分支的节点y相同，不同分支错开
      if (flow.nodes.length <= 4) {
        node.position.y = 200 + i * 120;
      } else {
        // 主题1有多个分支，分支节点y分布
        if (node.id === 'sentiment' || node.id === 'tech') node.position.y = 120;
        else if (node.id === 'priority' || node.id === 'complaint') node.position.y = 280;
        else if (node.id === 'product') node.position.y = 200;
        else node.position.y = 200;
      }
    });
  });

  const [currentNodes, setCurrentNodes] = useState<any[]>([]);
  const [currentEdges, setCurrentEdges] = useState<any[]>([]);

  const handleFlowSelect = (flow: FlowItem) => {
    setSelectedFlow(flow);
    setCurrentView('editor');
  };

  const handleFlowEdit = (flow: FlowItem) => {
    setSelectedFlow(flow);
    setCurrentView('editor');
    const flowData = mockFlowData[flow.id];
    if (flowData) {
      setCurrentNodes(flowData.nodes);
      setCurrentEdges(flowData.edges);
    } else {
      setCurrentNodes([]);
      setCurrentEdges([]);
    }
  };

  const handleFlowDelete = (flowId: string) => {
    console.log('删除工作流:', flowId);
    if (confirm('确定要删除这个工作流吗？')) {
      alert(`删除工作流: ${flowId}`);
    }
  };

  const handleFlowDuplicate = (flow: FlowItem) => {
    console.log('复制工作流:', flow);
    alert(`复制工作流: ${flow.name}`);
  };

  const handleBackToList = () => {
    setCurrentView('list');
    setSelectedFlow(null);
  };

  const handleSaveFlow = () => {
    alert('工作流已保存');
  };

  if (currentView === 'editor') {
    return (
      <div className="flex-1" style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
        {/* 编辑器头部 */}
        <div style={{
          padding: '16px 24px',
          borderBottom: '1px solid #e5e7eb',
          background: 'white',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            <button
              onClick={handleBackToList}
              style={{
                padding: '8px 12px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                background: 'white',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="m15 18-6-6 6-6"/>
              </svg>
              返回列表
            </button>
            <h2 style={{ margin: 0, fontSize: '20px', fontWeight: '600' }}>
              {selectedFlow?.name || '工作流编辑器'}
            </h2>
          </div>
          
          <div style={{ display: 'flex', gap: '12px' }}>
            <button
              onClick={handleSaveFlow}
              style={{
                padding: '8px 16px',
                border: 'none',
                borderRadius: '6px',
                background: '#3b82f6',
                color: 'white',
                cursor: 'pointer',
                fontWeight: '500'
              }}
            >
              保存
            </button>
            <button
              style={{
                padding: '8px 16px',
                border: '1px solid #d1d5db',
                borderRadius: '6px',
                background: 'white',
                cursor: 'pointer'
              }}
            >
              预览
            </button>
          </div>
        </div>

        {/* 工作流编辑器 */}
        <div style={{ flex: 1, overflow: 'hidden' }}>
          <Workflow
            nodes={currentNodes}
            edges={currentEdges}
            readOnly={false}
            showToolbar={true}
            showNodePanel={true}
            onNodesChange={(changes) => console.log('节点变化:', changes)}
            onEdgesChange={(changes) => console.log('边变化:', changes)}
            onNodeSelect={(node) => console.log('选择节点:', node)}
            onNodeAdd={(node) => console.log('添加节点:', node)}
            onNodeDelete={(nodeId) => console.log('删除节点:', nodeId)}
            onEdgeAdd={(edge) => console.log('添加边:', edge)}
            onEdgeDelete={(edgeId) => console.log('删除边:', edgeId)}
          />
        </div>
      </div>
    );
  }

  return (
    <div  className="flex-1" style={{ height: '100vh', overflow: 'auto' }}>
      <FlowList
        onFlowSelect={handleFlowSelect}
        onFlowEdit={handleFlowEdit}
        onFlowDelete={handleFlowDelete}
        onFlowDuplicate={handleFlowDuplicate}
      />
    </div>
  );
};

export default FlowListExample; 