/* FlowList 容器 */
.flow-list-container {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 加载状态 */
.flow-list-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 头部工具栏 */
.flow-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  flex-wrap: wrap;
  gap: 16px;
}

.flow-list-title {
  display: flex;
  align-items: baseline;
  gap: 12px;
}

.flow-list-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #1e293b;
}

.flow-count {
  color: #64748b;
  font-size: 14px;
}

.flow-list-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 搜索框 */
.search-box {
  position: relative;
  min-width: 240px;
}

.search-box input {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.2s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

/* 筛选和排序选择器 */
.status-filter,
.sort-select {
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.status-filter:focus,
.sort-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 工作流网格 */
.flow-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* 工作流卡片 */
.flow-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
}

.flow-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #d1d5db;
}

/* 缩略图区域 */
.flow-thumbnail {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  background: #f8fafc;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}

.flow-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.10);
  transition: opacity 0.5s;
  opacity: 0;
  background: #f8fafc;
}
.flow-thumbnail img.loaded {
  opacity: 1;
}

.flow-thumbnail-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #e0e7ef 0%, #f1f5f9 100%);
}

.flow-thumbnail-placeholder svg {
  width: 60px;
  height: 60px;
  color: #b6c2d1;
}

/* 状态标签 */
.status-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 卡片操作按钮 */
.flow-card-actions {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.flow-card:hover .flow-card-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: white;
  color: #3b82f6;
  transform: scale(1.05);
}

.action-btn.delete:hover {
  color: #ef4444;
}

.action-btn svg {
  width: 16px;
  height: 16px;
}

/* 卡片内容 */
.flow-card-content {
  padding: 20px;
}

.flow-name {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  cursor: pointer;
  transition: color 0.2s ease;
}

.flow-name:hover {
  color: #3b82f6;
}

.flow-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #64748b;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 标签 */
.flow-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.tag {
  padding: 4px 8px;
  background: #f1f5f9;
  color: #475569;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* 元信息 */
.flow-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #94a3b8;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-item svg {
  width: 14px;
  height: 14px;
}

/* 空状态 */
.flow-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  text-align: center;
  color: #94a3b8;
}

.flow-list-empty svg {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  color: #cbd5e1;
}

.flow-list-empty h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #64748b;
}

.flow-list-empty p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .flow-list-container {
    padding: 16px;
  }
  
  .flow-list-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .flow-list-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .flow-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .flow-card-actions {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .flow-list-title h2 {
    font-size: 24px;
  }
  
  .flow-card-content {
    padding: 16px;
  }
  
  .flow-name {
    font-size: 16px;
  }
} 