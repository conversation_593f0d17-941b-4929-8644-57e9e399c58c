import React, { useState, useEffect } from 'react';
import { Input, Select, Card, Tag, Button, Spin, Empty, Row, Col, Badge, Tooltip } from 'antd';
import { SearchOutlined, EditOutlined, CopyOutlined, DeleteOutlined, ClockCircleOutlined, NodeIndexOutlined } from '@ant-design/icons';
import './flow-list.css';

const { Search } = Input;
const { Option } = Select;
const { Meta } = Card;

export interface FlowItem {
  id: string;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
  nodeCount: number;
  status: 'active' | 'draft' | 'archived';
  tags: string[];
  thumbnail?: string;
}

interface FlowListProps {
  onFlowSelect?: (flow: FlowItem) => void;
  onFlowEdit?: (flow: FlowItem) => void;
  onFlowDelete?: (flowId: string) => void;
  onFlowDuplicate?: (flow: FlowItem) => void;
}

const FlowList: React.FC<FlowListProps> = ({
  onFlowSelect,
  onFlowEdit,
  onFlowDelete,
  onFlowDuplicate
}) => {
  const [flows, setFlows] = useState<FlowItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'createdAt' | 'updatedAt'>('updatedAt');
  const [imgLoaded, setImgLoaded] = useState<{[id: string]: boolean}>({});

  // 多种SVG缩略图组件
  const svgThumbs = [
    // 方案1：三分支
    (
      <svg width="100%" height="100%" viewBox="0 0 160 68" fill="none" xmlns="http://www.w3.org/2000/svg" style={{display:'block', width:'100%', height:'100%'}}>
        <defs>
          <linearGradient id="bg1" x1="0" y1="0" x2="160" y2="68" gradientUnits="userSpaceOnUse">
            <stop stopColor="#e0e7ef"/>
            <stop offset="1" stopColor="#c2e9fb"/>
          </linearGradient>
          <linearGradient id="line1" x1="0" y1="34" x2="160" y2="34" gradientUnits="userSpaceOnUse">
            <stop stopColor="#4f8edc"/>
            <stop offset="1" stopColor="#b6c2d1"/>
          </linearGradient>
        </defs>
        <rect x="0" y="0" width="160" height="68" fill="url(#bg1)"/>
        <path d="M40 34 Q60 18 120 18" stroke="url(#line1)" strokeWidth="2.5" fill="none"/>
        <path d="M40 34 Q60 34 120 34" stroke="url(#line1)" strokeWidth="2.5" fill="none"/>
        <path d="M40 34 Q60 50 120 50" stroke="url(#line1)" strokeWidth="2.5" fill="none"/>
        <circle cx="40" cy="34" r="10" fill="#fff" stroke="#4f8edc" strokeWidth="2.5"/>
        <circle cx="120" cy="18" r="7" fill="#fff" stroke="#4f8edc" strokeWidth="2.5"/>
        <circle cx="120" cy="34" r="7" fill="#fff" stroke="#4f8edc" strokeWidth="2.5"/>
        <circle cx="120" cy="50" r="7" fill="#fff" stroke="#4f8edc" strokeWidth="2.5"/>
      </svg>
    ),
    // 方案2：两层分支
    (
      <svg width="100%" height="100%" viewBox="0 0 160 68" fill="none" xmlns="http://www.w3.org/2000/svg" style={{display:'block', width:'100%', height:'100%'}}>
        <defs>
          <linearGradient id="bg2" x1="0" y1="0" x2="160" y2="68" gradientUnits="userSpaceOnUse">
            <stop stopColor="#f8fafc"/>
            <stop offset="1" stopColor="#b6c2d1"/>
          </linearGradient>
          <linearGradient id="line2" x1="0" y1="34" x2="160" y2="34" gradientUnits="userSpaceOnUse">
            <stop stopColor="#60a5fa"/>
            <stop offset="1" stopColor="#818cf8"/>
          </linearGradient>
        </defs>
        <rect x="0" y="0" width="160" height="68" fill="url(#bg2)"/>
        <path d="M40 34 Q80 10 120 18" stroke="url(#line2)" strokeWidth="2.5" fill="none"/>
        <path d="M40 34 Q80 34 120 34" stroke="url(#line2)" strokeWidth="2.5" fill="none"/>
        <path d="M40 34 Q80 58 120 50" stroke="url(#line2)" strokeWidth="2.5" fill="none"/>
        <circle cx="40" cy="34" r="10" fill="#fff" stroke="#60a5fa" strokeWidth="2.5"/>
        <circle cx="80" cy="10" r="5" fill="#fff" stroke="#818cf8" strokeWidth="2"/>
        <circle cx="80" cy="58" r="5" fill="#fff" stroke="#818cf8" strokeWidth="2"/>
        <circle cx="120" cy="18" r="7" fill="#fff" stroke="#60a5fa" strokeWidth="2.5"/>
        <circle cx="120" cy="34" r="7" fill="#fff" stroke="#60a5fa" strokeWidth="2.5"/>
        <circle cx="120" cy="50" r="7" fill="#fff" stroke="#60a5fa" strokeWidth="2.5"/>
      </svg>
    ),
    // 方案3：单主线+分支
    (
      <svg width="100%" height="100%" viewBox="0 0 160 68" fill="none" xmlns="http://www.w3.org/2000/svg" style={{display:'block', width:'100%', height:'100%'}}>
        <defs>
          <linearGradient id="bg3" x1="0" y1="0" x2="160" y2="68" gradientUnits="userSpaceOnUse">
            <stop stopColor="#e0e7ef"/>
            <stop offset="1" stopColor="#f1f5f9"/>
          </linearGradient>
          <linearGradient id="line3" x1="0" y1="34" x2="160" y2="34" gradientUnits="userSpaceOnUse">
            <stop stopColor="#38bdf8"/>
            <stop offset="1" stopColor="#6366f1"/>
          </linearGradient>
        </defs>
        <rect x="0" y="0" width="160" height="68" fill="url(#bg3)"/>
        <path d="M40 34 Q80 34 120 34" stroke="url(#line3)" strokeWidth="2.5" fill="none"/>
        <path d="M80 34 Q100 18 120 18" stroke="url(#line3)" strokeWidth="2.5" fill="none"/>
        <path d="M80 34 Q100 50 120 50" stroke="url(#line3)" strokeWidth="2.5" fill="none"/>
        <circle cx="40" cy="34" r="10" fill="#fff" stroke="#38bdf8" strokeWidth="2.5"/>
        <circle cx="80" cy="34" r="6" fill="#fff" stroke="#6366f1" strokeWidth="2"/>
        <circle cx="120" cy="18" r="7" fill="#fff" stroke="#38bdf8" strokeWidth="2.5"/>
        <circle cx="120" cy="34" r="7" fill="#fff" stroke="#38bdf8" strokeWidth="2.5"/>
        <circle cx="120" cy="50" r="7" fill="#fff" stroke="#38bdf8" strokeWidth="2.5"/>
      </svg>
    ),
    // 方案4：多节点分布
    (
      <svg width="100%" height="100%" viewBox="0 0 160 68" fill="none" xmlns="http://www.w3.org/2000/svg" style={{display:'block', width:'100%', height:'100%'}}>
        <defs>
          <linearGradient id="bg4" x1="0" y1="0" x2="160" y2="68" gradientUnits="userSpaceOnUse">
            <stop stopColor="#f1f5f9"/>
            <stop offset="1" stopColor="#e0e7ef"/>
          </linearGradient>
          <linearGradient id="line4" x1="0" y1="34" x2="160" y2="34" gradientUnits="userSpaceOnUse">
            <stop stopColor="#818cf8"/>
            <stop offset="1" stopColor="#60a5fa"/>
          </linearGradient>
        </defs>
        <rect x="0" y="0" width="160" height="68" fill="url(#bg4)"/>
        <path d="M30 20 Q80 34 130 20" stroke="url(#line4)" strokeWidth="2.5" fill="none"/>
        <path d="M30 48 Q80 34 130 48" stroke="url(#line4)" strokeWidth="2.5" fill="none"/>
        <circle cx="30" cy="20" r="6" fill="#fff" stroke="#818cf8" strokeWidth="2"/>
        <circle cx="30" cy="48" r="6" fill="#fff" stroke="#818cf8" strokeWidth="2"/>
        <circle cx="80" cy="34" r="8" fill="#fff" stroke="#60a5fa" strokeWidth="2.5"/>
        <circle cx="130" cy="20" r="6" fill="#fff" stroke="#818cf8" strokeWidth="2"/>
        <circle cx="130" cy="48" r="6" fill="#fff" stroke="#818cf8" strokeWidth="2"/>
      </svg>
    ),
  ];

  // 模拟数据
  useEffect(() => {
    const mockFlows: FlowItem[] = [
      {
        id: '1',
        name: '智能客服工作流',
        description: '处理客户咨询、投诉和建议的智能客服系统',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-20T14:45:00Z',
        nodeCount: 25,
        status: 'active',
        tags: ['客服', 'AI', '自动化'],
        thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjUwIiBjeT0iNDAiIHI9IjE1IiBmaWxsPSIjNjM2M0Y2Ii8+CjxjaXJjbGUgY3g9IjE1MCIgY3k9IjQwIiByPSIxNSIgZmlsbD0iIzYzNjNGNiIvPgo8cGF0aCBkPSJNNDAgODBDNDAgNzAgNTAgNjAgNjAgNjBIMTQwQzE1MCA2MCAxNjAgNzAgMTYwIDgwVjEwMEg0MFY4MFoiIGZpbGw9IiM2MzYzRjYiLz4KPC9zdmc+'
      },
      {
        id: '2',
        name: '订单处理流程',
        description: '自动化订单接收、处理和配送管理流程',
        createdAt: '2024-01-10T09:15:00Z',
        updatedAt: '2024-01-18T16:20:00Z',
        nodeCount: 18,
        status: 'active',
        tags: ['订单', '物流', '自动化'],
        thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjMwIiB5PSIzMCIgd2lkdGg9IjE0MCIgaGVpZ2h0PSI5MCIgcng9IjgiIGZpbGw9IiM2MzYzRjYiLz4KPHJlY3QgeD0iNDAiIHk9IjQwIiB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEwIiByeD0iNSIgZmlsbD0iI0Y1RjVGNSIvPgo8cmVjdCB4PSI0MCIgeT0iNjAiIHdpZHRoPSI4MCIgaGVpZ2h0PSIxMCIgcng9IjUiIGZpbGw9IiNGNUY1RjUiLz4KPHJlY3QgeD0iNDAiIHk9IjgwIiB3aWR0aD0iNjAiIGhlaWdodD0iMTAiIHJ4PSI1IiBmaWxsPSIjRjVGNUY1Ii8+Cjwvc3ZnPg=='
      },
      {
        id: '3',
        name: '用户注册验证',
        description: '新用户注册、邮箱验证和账户激活流程',
        createdAt: '2024-01-05T11:00:00Z',
        updatedAt: '2024-01-12T13:30:00Z',
        nodeCount: 12,
        status: 'draft',
        tags: ['注册', '验证', '安全'],
        thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjUwIiByPSIzMCIgZmlsbD0iIzYzNjNGNiIvPgo8cGF0aCBkPSJNODAgNzBDODAgNjAgOTAgNTAgMTAwIDUwSDExMEMxMjAgNTAgMTMwIDYwIDEzMCA3MFY5MEg4MFY3MFoiIGZpbGw9IiM2MzYzRjYiLz4KPHJlY3QgeD0iNzAiIHk9IjEwMCIgd2lkdGg9IjYwIiBoZWlnaHQ9IjEwIiByeD0iNSIgZmlsbD0iIzYzNjNGNiIvPgo8L3N2Zz4='
      },
      {
        id: '4',
        name: '数据备份恢复',
        description: '定期数据备份和灾难恢复流程',
        createdAt: '2024-01-08T08:45:00Z',
        updatedAt: '2024-01-15T12:00:00Z',
        nodeCount: 8,
        status: 'archived',
        tags: ['备份', '恢复', '数据'],
        thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjQwIiB5PSIzMCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSI5MCIgcng9IjgiIGZpbGw9IiM2MzYzRjYiLz4KPHJlY3QgeD0iNTUiIHk9IjQ1IiB3aWR0aD0iOTAiIGhlaWdodD0iMTUiIHJ4PSI3IiBmaWxsPSIjRjVGNUY1Ii8+CjxyZWN0IHg9IjU1IiB5PSI3MCIgd2lkdGg9IjkwIiBoZWlnaHQ9IjE1IiByeD0iNyIgZmlsbD0iI0Y1RjVGNSIvPgo8L3N2Zz4='
      },
      {
        id: '5',
        name: '营销活动管理',
        description: '营销活动创建、执行和效果分析流程',
        createdAt: '2024-01-12T14:20:00Z',
        updatedAt: '2024-01-19T10:15:00Z',
        nodeCount: 22,
        status: 'active',
        tags: ['营销', '活动', '分析'],
        thumbnail: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDIwMCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMTUwIiBmaWxsPSIjRjVGNUY1Ii8+CjxjaXJjbGUgY3g9IjEwMCIgY3k9IjUwIiByPSIzMCIgZmlsbD0iIzYzNjNGNiIvPgo8cGF0aCBkPSJNODAgMzBDODAgMjAgOTAgMTAgMTAwIDEwSDExMEMxMjAgMTAgMTMwIDIwIDEzMCAzMFY3MEg4MFYzMFoiIGZpbGw9IiM2MzYzRjYiLz4KPHBhdGggZD0iTTgwIDgwQzgwIDcwIDkwIDYwIDEwMCA2MEgxMTBDMTIwIDYwIDEzMCA3MCAxMzAgODBWMTAwSDgwVjgwWiIgZmlsbD0iIzYzNjNGNiIvPgo8L3N2Zz4='
      }
    ];

    // 模拟加载延迟
    setTimeout(() => {
      setFlows(mockFlows);
      setLoading(false);
    }, 1000);
  }, []);

  // 过滤和排序逻辑
  const filteredAndSortedFlows = flows
    .filter(flow => {
      const matchesSearch = flow.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           flow.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           flow.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesStatus = filterStatus === 'all' || flow.status === filterStatus;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'createdAt':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'updatedAt':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        default:
          return 0;
      }
    });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return '#10B981';
      case 'draft':
        return '#F59E0B';
      case 'archived':
        return '#6B7280';
      default:
        return '#6B7280';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '活跃';
      case 'draft':
        return '草稿';
      case 'archived':
        return '已归档';
      default:
        return '未知';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleTitleClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止冒泡，不触发选择/跳转
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载工作流中...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: 24, background: '#f5f5f5', minHeight: '100vh' }}>
      {/* 头部工具栏 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <div>
            <h2 style={{ margin: 0, fontSize: 28, fontWeight: 700 }}>工作流列表</h2>
            <span style={{ color: '#666', fontSize: 14 }}>共 {filteredAndSortedFlows.length} 个工作流</span>
          </div>
          
          <div style={{ display: 'flex', gap: 12 }}>
            <Search
              placeholder="搜索工作流..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{ width: 240 }}
              prefix={<SearchOutlined />}
            />
            
            <Select
              value={filterStatus}
              onChange={setFilterStatus}
              style={{ width: 120 }}
            >
              <Option value="all">全部状态</Option>
              <Option value="active">活跃</Option>
              <Option value="draft">草稿</Option>
              <Option value="archived">已归档</Option>
            </Select>

            <Select
              value={sortBy}
              onChange={setSortBy}
              style={{ width: 120 }}
            >
              <Option value="updatedAt">最近更新</Option>
              <Option value="createdAt">创建时间</Option>
              <Option value="name">名称</Option>
            </Select>
          </div>
        </div>
      </div>

      {/* 工作流卡片网格 */}
      <Row gutter={[24, 24]}>
        {filteredAndSortedFlows.map((flow) => (
          <Col key={flow.id} xs={24} sm={24} md={8} lg={8} xl={8}>
            <Card
              hoverable
              style={{ 
                width: '100%', 
                height: 400,
                display: 'flex',
                flexDirection: 'column'
              }}
              styles={{ 
                body: {
                  flex: 1,
                  display: 'flex',
                  flexDirection: 'column',
                  padding: 16
                }
              }}
              cover={
                <div style={{ position: 'relative', height: 160, background: '#f8fafc', overflow: 'hidden' }}>
                  <div style={{ width: '100%', height: '100%' }}>
                    {svgThumbs[parseInt(flow.id.replace(/\D/g, ''), 10) % svgThumbs.length || 0]}
                  </div>
                  <Badge 
                    status={flow.status === 'active' ? 'success' : flow.status === 'draft' ? 'warning' : 'default'}
                    text={getStatusText(flow.status)}
                    style={{ position: 'absolute', top: 8, right: 8 }}
                  />
                </div>
              }
              actions={[
                <Tooltip title="编辑">
                  <Button 
                    type="text" 
                    icon={<EditOutlined />} 
                    onClick={() => onFlowEdit?.(flow)}
                  />
                </Tooltip>,
                <Tooltip title="复制">
                  <Button 
                    type="text" 
                    icon={<CopyOutlined />} 
                    onClick={() => onFlowDuplicate?.(flow)}
                  />
                </Tooltip>,
                <Tooltip title="删除">
                  <Button 
                    type="text" 
                    danger 
                    icon={<DeleteOutlined />} 
                    onClick={() => onFlowDelete?.(flow.id)}
                  />
                </Tooltip>
              ]}
              onClick={() => onFlowSelect?.(flow)}
            >
              <Meta
                title={<div style={{ fontSize: 16, fontWeight: 600 }}>{flow.name}</div>}
                description={
                  <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                    <p style={{ marginBottom: 12, color: '#666', flex: 1 }}>{flow.description}</p>
                    
                    <div style={{ marginBottom: 12 }}>
                      {flow.tags.map((tag, index) => (
                        <Tag key={index} color="blue" style={{ marginBottom: 4 }}>
                          {tag}
                        </Tag>
                      ))}
                    </div>

                    <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: 12, color: '#999' }}>
                      <span>
                        <ClockCircleOutlined style={{ marginRight: 4 }} />
                        {formatDate(flow.updatedAt)}
                      </span>
                      <span>
                        <NodeIndexOutlined style={{ marginRight: 4 }} />
                        {flow.nodeCount} 个节点
                      </span>
                    </div>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {/* 空状态 */}
      {filteredAndSortedFlows.length === 0 && (
        <Empty
          description="暂无工作流"
          style={{ marginTop: 60 }}
        >
          <Button type="primary">创建工作流</Button>
        </Empty>
      )}
    </div>
  );
};

export default FlowList; 
