# 工作流组件 (Workflow Component)

一个功能完整、可复用的React工作流编辑器组件，基于ReactFlow构建，支持节点拖拽、连接、配置等完整功能。

## 功能特性

### 🎯 核心功能
- **节点管理**: 支持添加、删除、配置节点
- **连接管理**: 支持节点间连接，支持连接线拖拽重新连接
- **交互操作**: 支持拖拽、缩放、选择等操作
- **配置面板**: 完整的节点配置界面，支持变量、模型参数等配置

### 🎨 UI特性
- **现代化设计**: 美观的UI设计，支持状态指示器
- **响应式布局**: 适配不同屏幕尺寸
- **动画效果**: 流畅的交互动画
- **主题支持**: 可自定义颜色主题

### 🔧 高级功能
- **节点选择器**: 可视化节点类型选择
- **右键菜单**: 丰富的上下文菜单
- **工具栏**: 完整的操作工具栏
- **状态管理**: 完整的节点运行状态显示

## 快速开始

### 安装依赖

```bash
npm install reactflow
```

### 基本使用

```tsx
import React, { useState } from 'react'
import Workflow from './components/workflow'
import type { Node, Edge } from 'reactflow'

const App = () => {
  const [nodes, setNodes] = useState<Node[]>([
    {
      id: '1',
      type: 'custom-node',
      position: { x: 100, y: 100 },
      data: {
        type: 'llm',
        title: 'LLM节点',
        desc: '大语言模型节点',
        variables: [],
        model: 'gpt-3.5-turbo',
        params: { temperature: 0.7 }
      }
    }
  ])

  const [edges, setEdges] = useState<Edge[]>([])

  return (
    <div style={{ width: '100vw', height: '100vh' }}>
      <Workflow
        nodes={nodes}
        edges={edges}
        onNodesChange={setNodes}
        onEdgesChange={setEdges}
        readOnly={false}
        showToolbar={true}
        showNodePanel={true}
      />
    </div>
  )
}

export default App
```

## API 文档

### WorkflowProps

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `nodes` | `Node[]` | `[]` | 节点数组 |
| `edges` | `Edge[]` | `[]` | 边数组 |
| `viewport` | `Viewport` | `{ x: 0, y: 0, zoom: 1 }` | 视口配置 |
| `readOnly` | `boolean` | `false` | 是否只读模式 |
| `showToolbar` | `boolean` | `true` | 是否显示工具栏 |
| `showNodePanel` | `boolean` | `true` | 是否显示节点面板 |
| `onNodesChange` | `(nodes: Node[]) => void` | - | 节点变化回调 |
| `onEdgesChange` | `(edges: Edge[]) => void` | - | 边变化回调 |
| `onNodeSelect` | `(node: Node) => void` | - | 节点选择回调 |
| `onNodeAdd` | `OnNodeAdd` | - | 节点添加回调 |
| `onNodeDelete` | `(nodeId: string) => void` | - | 节点删除回调 |

### 节点数据结构

```typescript
interface NodeData {
  type: BlockEnum           // 节点类型
  title: string            // 节点标题
  desc: string             // 节点描述
  variables: Variable[]     // 变量列表
  model?: string           // 模型名称 (LLM节点)
  params?: Record<string, any> // 模型参数
  _runningStatus?: NodeRunningStatus // 运行状态
}
```

### 支持的节点类型

- `start`: 开始节点
- `end`: 结束节点
- `llm`: 大语言模型
- `knowledge-retrieval`: 知识检索
- `question-classifier`: 问题分类
- `if-else`: 条件判断
- `code`: 代码执行
- `template-transform`: 模板转换
- `http-request`: HTTP请求
- `variable-assigner`: 变量分配
- `tool`: 工具节点
- `agent`: 智能体
- 更多...

## 使用指南

### 1. 添加节点

#### 方法一：工具栏按钮
点击工具栏中的"➕ 添加节点"按钮，会在画布中心添加一个默认的LLM节点。

#### 方法二：节点选择器
点击"📋 选择节点类型"按钮，弹出节点选择器，可以选择具体的节点类型。

#### 方法三：右键菜单
在画布空白处右键，选择"添加节点"。

#### 方法四：节点右侧添加
右键点击现有节点，选择"在右侧添加节点"，会自动添加并连接新节点。

### 2. 配置节点

#### 点击节点
直接点击节点会弹出配置面板，可以编辑：
- 基本信息（标题、描述）
- 模型配置（模型名称、参数）
- 变量配置（添加、删除、编辑变量）

#### 右键菜单
右键点击节点，选择"配置节点"。

### 3. 连接节点

#### 拖拽连接
从节点的输出连接点拖拽到目标节点的输入连接点。

#### 重新连接
拖拽连接线可以重新连接到其他节点。

### 4. 删除节点

#### 方法一：配置面板
在节点配置面板中点击"删除节点"按钮。

#### 方法二：右键菜单
右键点击节点，选择"删除节点"。

### 5. 工具栏操作

- **👆 指针**: 选择模式，可以点击选择节点
- **✋ 抓手**: 拖拽模式，可以拖拽画布
- **🔍+ 放大**: 放大视图
- **🔍- 缩小**: 缩小视图
- **📐 适应视图**: 自动调整视图以显示所有节点

## 自定义配置

### 自定义节点类型

```tsx
import CustomNode from './components/custom-node'

const customNodeTypes = {
  'my-custom-node': CustomNode
}

<Workflow
  nodes={nodes}
  edges={edges}
  customNodeTypes={customNodeTypes}
/>
```

### 自定义工具栏按钮

```tsx
const customToolbarButtons = [
  {
    id: 'custom-action',
    label: '自定义操作',
    icon: '🔧',
    action: () => console.log('Custom action')
  }
]

<Workflow
  nodes={nodes}
  edges={edges}
  toolbarButtons={customToolbarButtons}
/>
```

### 自定义右键菜单

```tsx
const customContextMenuItems = [
  {
    id: 'custom-menu',
    label: '自定义菜单',
    icon: '⚙️',
    action: () => console.log('Custom menu')
  }
]

<Workflow
  nodes={nodes}
  edges={edges}
  contextMenuItems={customContextMenuItems}
/>
```

## 样式定制

### CSS变量

可以通过CSS变量自定义主题：

```css
:root {
  --workflow-primary-color: #528BFF;
  --workflow-success-color: #10b981;
  --workflow-warning-color: #f59e0b;
  --workflow-error-color: #ef4444;
  --workflow-border-color: #e2e8f0;
  --workflow-background-color: #ffffff;
}
```

### 自定义样式

```css
/* 自定义节点样式 */
.custom-node {
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 自定义连接线样式 */
.custom-edge {
  stroke-width: 3;
  stroke-dasharray: 5,5;
}
```

## 事件处理

### 节点事件

```tsx
<Workflow
  nodes={nodes}
  edges={edges}
  onNodeSelect={(node) => {
    console.log('Node selected:', node)
  }}
  onNodeAdd={(newNode, oldNodes) => {
    console.log('Node added:', newNode)
  }}
  onNodeDelete={(nodeId) => {
    console.log('Node deleted:', nodeId)
  }}
/>
```

### 边事件

```tsx
<Workflow
  nodes={nodes}
  edges={edges}
  onEdgeAdd={(edge) => {
    console.log('Edge added:', edge)
  }}
  onEdgeDelete={(edgeId) => {
    console.log('Edge deleted:', edgeId)
  }}
/>
```

## 最佳实践

### 1. 性能优化

- 对于大量节点，考虑使用虚拟化
- 避免在渲染函数中进行复杂计算
- 使用 `useCallback` 和 `useMemo` 优化回调函数

### 2. 状态管理

- 将工作流状态提升到父组件
- 使用不可变数据更新状态
- 考虑使用状态管理库（Redux、Zustand等）

### 3. 错误处理

- 添加节点配置验证
- 处理连接错误
- 提供用户友好的错误提示

### 4. 用户体验

- 提供加载状态指示
- 添加操作确认对话框
- 支持撤销/重做功能

## 故障排除

### 常见问题

1. **节点不显示**
   - 检查节点类型是否正确注册
   - 确认节点位置是否在可视区域内

2. **连接失败**
   - 检查源节点和目标节点是否存在
   - 确认连接点类型是否匹配

3. **配置面板不显示**
   - 检查节点点击事件是否正确绑定
   - 确认配置面板组件是否正确导入

4. **样式问题**
   - 检查CSS文件是否正确导入
   - 确认样式优先级是否正确

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的节点和连接功能
- 提供配置面板和工具栏

### v1.1.0
- 添加节点选择器
- 优化UI设计和交互体验
- 增强配置面板功能

## 贡献指南

欢迎提交Issue和Pull Request来改进这个组件。

## 许可证

MIT License 