import React, { memo, useEffect, useRef } from 'react'
import type { ContextMenuItem } from '../types'

interface ContextMenuProps {
  x: number
  y: number
  items: ContextMenuItem[]
  onClose: () => void
  onItemClick: (item: ContextMenuItem) => void
}

const ContextMenu: React.FC<ContextMenuProps> = ({ x, y, items, onClose, onItemClick }) => {
  const menuRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    document.addEventListener('keydown', handleEscape)

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [onClose])

  const handleItemClick = (item: ContextMenuItem) => {
    if (!item.disabled) {
      onItemClick(item)
      onClose()
    }
  }

  return (
    <div
      ref={menuRef}
      className="context-menu"
      style={{
        left: x,
        top: y,
      }}
    >
      {items.map((item, index) => (
        <div key={item.id || index}>
          {item.divider ? (
            <div className="context-menu-divider" />
          ) : (
            <div
              className={`context-menu-item ${item.disabled ? 'disabled' : ''}`}
              onClick={() => handleItemClick(item)}
            >
              <span style={{ marginRight: '8px' }}>{item.icon}</span>
              <span>{item.label}</span>
            </div>
          )}
        </div>
      ))}
    </div>
  )
}

export default memo(ContextMenu) 