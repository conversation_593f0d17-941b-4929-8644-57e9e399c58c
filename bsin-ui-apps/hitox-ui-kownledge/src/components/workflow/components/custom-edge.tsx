import React, { memo, useMemo } from 'react'
import type { EdgeProps } from 'reactflow'
import { BaseEdge, EdgeLabelRenderer, Position, getBezierPath } from 'reactflow'
import type { Edge } from '../types'
import { NodeRunningStatus } from '../types'
import { DEFAULT_EDGE_CONFIG } from '../constants'

const CustomEdge: React.FC<EdgeProps> = ({
  id,
  data,
  source,
  sourceHandleId,
  target,
  targetHandleId,
  sourceX,
  sourceY,
  targetX,
  targetY,
  selected,
}: EdgeProps) => {
  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX: sourceX - 8,
    sourceY,
    sourcePosition: Position.Right,
    targetX: targetX + 8,
    targetY,
    targetPosition: Position.Left,
    curvature: 0.16,
  })

  const edgeData = data as Edge['data']
  const sourceRunningStatus = edgeData?._sourceRunningStatus
  const targetRunningStatus = edgeData?._targetRunningStatus

  const getEdgeColor = (status?: NodeRunningStatus) => {
    if (status === NodeRunningStatus.Running) return '#3b82f6'
    if (status === NodeRunningStatus.Succeeded) return '#10b981'
    if (status === NodeRunningStatus.Failed || status === NodeRunningStatus.Exception) return '#ef4444'
    if (status === NodeRunningStatus.Waiting) return '#f59e0b'
    return DEFAULT_EDGE_CONFIG.strokeColor
  }

  const stroke = useMemo(() => {
    if (selected) return getEdgeColor(NodeRunningStatus.Running)
    if (edgeData?._connectedNodeIsHovering) return getEdgeColor(NodeRunningStatus.Running)
    if (sourceRunningStatus && targetRunningStatus) {
      return getEdgeColor(sourceRunningStatus)
    }
    return getEdgeColor()
  }, [selected, edgeData?._connectedNodeIsHovering, sourceRunningStatus, targetRunningStatus])

  const strokeWidth = useMemo(() => {
    if (selected || edgeData?._connectedNodeIsHovering) {
      return DEFAULT_EDGE_CONFIG.hoverStrokeWidth
    }
    return DEFAULT_EDGE_CONFIG.strokeWidth
  }, [selected, edgeData?._connectedNodeIsHovering])

  return (
    <>
      <BaseEdge
        id={id}
        path={edgePath}
        style={{
          stroke,
          strokeWidth,
          opacity: edgeData?._waitingRun ? 0.7 : 1,
        }}
      />
      <EdgeLabelRenderer>
        <div
          className="edge-label"
          style={{
            position: 'absolute',
            transform: `translate(-50%, -50%) translate(${labelX}px, ${labelY}px)`,
            pointerEvents: 'all',
            opacity: edgeData?._waitingRun ? 0.7 : 1,
            display: edgeData?._hovering ? 'block' : 'none',
          }}
        >
          {/* 可以在这里添加边的标签或交互元素 */}
        </div>
      </EdgeLabelRenderer>
    </>
  )
}

export default memo(CustomEdge) 