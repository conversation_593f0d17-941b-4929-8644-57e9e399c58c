import React, { memo, useCallback } from 'react'
import type { NodeProps } from 'reactflow'
import { <PERSON><PERSON>, Position } from 'reactflow'
import { BlockEnum } from '../types'
import { NODE_TYPE_MAP, NODE_ICON_MAP, NODE_COLOR_MAP, NODE_STATUS_COLOR_MAP, DEFAULT_NODE_CONFIG } from '../constants'
import { NodeRunningStatus } from '../types'
import { log } from 'console'

interface CustomNodeProps extends NodeProps {
  data: any
  onAddNodeAfter?: (nodeId: string, event: React.MouseEvent) => void
}

const CustomNode: React.FC<CustomNodeProps> = ({ data, selected, id, onAddNodeAfter }) => {
  const nodeType = data.type as BlockEnum
  const nodeTitle = NODE_TYPE_MAP[nodeType] || nodeType
  const nodeIcon = NODE_ICON_MAP[nodeType] || '📦'
  const nodeColor = NODE_COLOR_MAP[nodeType] || '#6b7280'
  const runningStatus = data._runningStatus || NodeRunningStatus.NotStart
  const statusColor = NODE_STATUS_COLOR_MAP[runningStatus as keyof typeof NODE_STATUS_COLOR_MAP]

  const handleNodeClick = useCallback(() => {
    console.log('Node clicked:', id, data)
  }, [id, data])

  // 加号按钮点击
  const handleAddNodeClick = (e: React.MouseEvent) => {
    console.log('--- 步骤1: 加号按钮点击事件已触发 ---');
    e.stopPropagation();
    if (typeof onAddNodeAfter === 'function') {
      console.log('--- 步骤2: onAddNodeAfter 是一个函数，准备调用 ---');
      onAddNodeAfter(id, e);
    } else {
      console.error('--- 错误: onAddNodeAfter 不是一个函数! ---', onAddNodeAfter);
    }
  }

  const getNodeStyle = (): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      width: data.width || DEFAULT_NODE_CONFIG.width,
      height: data.height || DEFAULT_NODE_CONFIG.height,
      border: `${DEFAULT_NODE_CONFIG.borderWidth}px solid ${selected ? DEFAULT_NODE_CONFIG.selectedBorderColor : DEFAULT_NODE_CONFIG.borderColor}`,
      borderRadius: `${DEFAULT_NODE_CONFIG.borderRadius}px`,
      backgroundColor: DEFAULT_NODE_CONFIG.backgroundColor,
      boxShadow: selected ? DEFAULT_NODE_CONFIG.selectedShadow : '0 1px 3px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.2s ease',
      cursor: 'pointer',
      position: 'relative',
      overflow: 'visible', // 修复：允许加号按钮在节点外部显示
    }
    if (runningStatus !== NodeRunningStatus.NotStart) {
      baseStyle.borderColor = statusColor
      if (runningStatus === NodeRunningStatus.Running) {
        baseStyle.backgroundColor = '#fef3c7'
        baseStyle.boxShadow = '0 0 0 2px rgba(245, 158, 11, 0.2)'
      } else if (runningStatus === NodeRunningStatus.Succeeded) {
        baseStyle.backgroundColor = '#d1fae5'
        baseStyle.boxShadow = '0 0 0 2px rgba(16, 185, 129, 0.2)'
      } else if (runningStatus === NodeRunningStatus.Failed || runningStatus === NodeRunningStatus.Exception) {
        baseStyle.backgroundColor = '#fee2e2'
        baseStyle.boxShadow = '0 0 0 2px rgba(239, 68, 68, 0.2)'
      }
    }
    return baseStyle
  }

  const getStatusText = () => {
    switch (runningStatus) {
      case NodeRunningStatus.Running:
        return '运行中...'
      case NodeRunningStatus.Succeeded:
        return '成功'
      case NodeRunningStatus.Failed:
        return '失败'
      case NodeRunningStatus.Exception:
        return '异常'
      case NodeRunningStatus.Waiting:
        return '等待中'
      case NodeRunningStatus.Retry:
        return '重试中'
      default:
        return ''
    }
  }

  // 判断是否为结束节点
  const isEndNode = () => {
    // 结束节点类型：end（结束）和 answer（回答）
    return nodeType === 'end' || nodeType === 'answer'
  }

  return (
    <div
      className="custom-node"
      style={getNodeStyle()}
      onClick={handleNodeClick}
    >
      {/* 状态指示器 */}
      {runningStatus !== NodeRunningStatus.NotStart && (
        <div 
          className="node-status-indicator"
          style={{ 
            backgroundColor: statusColor,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px'
          }}
        />
      )}

      {/* 输入连接点 */}
      <Handle
        type="target"
        position={Position.Left}
        style={{
          background: nodeColor,
          border: '2px solid white',
          width: 10,
          height: 10,
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        }}
        className="node-handle"
      />

      {/* 节点内容 */}
      <div className="node-content">
        <div className="node-header">
          <div className="node-icon" style={{ color: nodeColor }}>
            {nodeIcon}
          </div>
          <div className="node-title">
            {data.title || nodeTitle}
          </div>
        </div>
        {data.desc && (
          <div className="node-description">
            {data.desc}
          </div>
        )}
        {runningStatus !== NodeRunningStatus.NotStart && (
          <div className="node-status" style={{ color: statusColor }}>
            {getStatusText()}
          </div>
        )}
        {data.variables && data.variables.length > 0 && (
          <div className="node-variables">
            <span className="variables-count">
              {data.variables.length} 个变量
            </span>
          </div>
        )}
      </div>

      {/* 输出连接点 */}
      <Handle
        type="source"
        position={Position.Right}
        style={{
          background: nodeColor,
          border: '2px solid white',
          width: 10,
          height: 10,
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        }}
        className="node-handle"
      />

      {/* 右侧加号按钮 - 结束节点不显示 */}
      {!isEndNode() && (
        <button
          className="node-add-btn nodrag" // 修复：添加 nodrag 类名，防止事件冲突
          style={{
            position: 'absolute',
            right: -8,
            top: '50%',
            transform: 'translateY(-50%)',
            width: 24,
            height: 24,
            borderRadius: '50%',
            background: 'linear-gradient(135deg, #2563eb 60%, #3b82f6 100%)',
            color: '#fff',
            border: '2px solid #fff',
            boxShadow: '0 2px 8px 0 rgba(59,130,246,0.18)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: 16,
            fontWeight: 700,
            cursor: 'pointer',
            zIndex: 10000,
            transition: 'box-shadow 0.2s, background 0.2s, border 0.2s',
            pointerEvents: 'auto',
          }}
          title="添加节点"
          onPointerDownCapture={handleAddNodeClick}
        >
          <span style={{lineHeight: 1, fontSize: 16, fontWeight: 700}}>+</span>
        </button>
      )}

      {/* 选中状态指示器 */}
      {selected && (
        <div 
          className="node-selection-indicator"
          style={{
            position: 'absolute',
            top: -2,
            left: -2,
            right: -2,
            bottom: -2,
            border: '2px solid #528BFF',
            borderRadius: '10px',
            pointerEvents: 'none',
            zIndex: -1,
          }}
        />
      )}
    </div>
  )
}

export default memo(CustomNode) 