.node-config-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 1001;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  width: 400px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.config-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #111827;
  font-size: 16px;
}

.config-icon {
  font-size: 18px;
}

.config-close {
  background: none;
  border: none;
  font-size: 18px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s;
}

.config-close:hover {
  background: #e5e7eb;
  color: #374151;
}

.config-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.config-section {
  margin-bottom: 24px;
}

.config-section h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.config-field {
  margin-bottom: 16px;
}

.config-field label {
  display: block;
  margin-bottom: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.config-field input,
.config-field textarea,
.config-field select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.config-field input:focus,
.config-field textarea:focus,
.config-field select:focus {
  outline: none;
  border-color: #528BFF;
  box-shadow: 0 0 0 3px rgba(82, 139, 255, 0.1);
}

.config-field textarea {
  resize: vertical;
  min-height: 60px;
}

.config-type-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
}

.type-icon {
  font-size: 16px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  border: none;
  padding: 0;
}

.add-variable-btn {
  background: #528BFF;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.add-variable-btn:hover {
  background: #3b82f6;
}

.empty-variables {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
  background: #f9fafb;
  border-radius: 6px;
}

.variables-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.variable-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  background: #f9fafb;
}

.variable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.variable-index {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.remove-variable-btn {
  background: none;
  border: none;
  font-size: 14px;
  color: #ef4444;
  cursor: pointer;
  padding: 2px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.remove-variable-btn:hover {
  background: #fee2e2;
}

.variable-fields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.variable-field {
  grid-column: span 1;
}

.variable-field.checkbox {
  grid-column: span 2;
  display: flex;
  align-items: center;
  gap: 8px;
}

.variable-field.checkbox label {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0;
  cursor: pointer;
  font-size: 12px;
}

.variable-field.checkbox input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.config-actions {
  display: flex;
  gap: 8px;
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

.config-btn {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.config-btn-primary {
  background: #528BFF;
  color: white;
  border-color: #528BFF;
}

.config-btn-primary:hover {
  background: #3b82f6;
  border-color: #3b82f6;
}

.config-btn-danger {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.config-btn-danger:hover {
  background: #dc2626;
  border-color: #dc2626;
}

.config-btn-secondary {
  background: white;
  color: #374151;
}

.config-btn-secondary:hover {
  background: #f3f4f6;
}

.config-content::-webkit-scrollbar {
  width: 6px;
}

.config-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.config-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.config-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
} 