import React, { useState, useCallback, useEffect, useRef } from 'react'
import type { Node } from 'reactflow'
import { BlockEnum } from '../types'
import { NODE_TYPE_MAP, NODE_ICON_MAP, NODE_COLOR_MAP } from '../constants'
import './node-config-panel.css'

interface NodeConfigPanelProps {
  node: Node | null
  onSave: (updatedNode: Node) => void
  onDelete: (nodeId: string) => void
  onClose: () => void
}

interface Variable {
  name: string
  type: string
  description: string
  required: boolean
}

const NodeConfigPanel: React.FC<NodeConfigPanelProps> = ({
  node,
  onSave,
  onDelete,
  onClose
}) => {
  const [config, setConfig] = useState(node?.data || {})
  const [variables, setVariables] = useState<Variable[]>(
    node?.data.variables?.map((v: any) => ({
      name: v.name || v.variable || '',
      type: v.type || 'string',
      description: v.description || v.des || '',
      required: v.required || false
    })) || []
  )
  
  const panelRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭弹框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查点击是否在弹框外部
      if (panelRef.current && !panelRef.current.contains(event.target as Element)) {
        // 检查点击是否在ReactFlow画布区域内
        const target = event.target as Element
        const isInReactFlow = target.closest('.react-flow__pane') || 
                             target.closest('.react-flow__viewport') ||
                             target.closest('.react-flow__renderer')
        
        // 只有在ReactFlow画布区域内点击才关闭弹框
        if (isInReactFlow) {
          onClose()
        }
      }
    }

    // 添加事件监听器
    document.addEventListener('mousedown', handleClickOutside)
    
    // 清理事件监听器
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [onClose])

  // ESC键关闭弹框
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [onClose])

  if (!node) return null

  const nodeType = config.type as BlockEnum
  const nodeTitle = NODE_TYPE_MAP[nodeType] || nodeType
  const nodeIcon = NODE_ICON_MAP[nodeType] || '📦'
  const nodeColor = NODE_COLOR_MAP[nodeType] || '#6b7280'

  const handleSave = useCallback(() => {
    const updatedNode = {
      ...node,
      data: {
        ...config,
        variables: variables.map(v => ({
          variable: v.name,
          type: v.type,
          des: v.description,
          required: v.required
        }))
      }
    }
    onSave(updatedNode)
  }, [node, config, variables, onSave])

  const handleDelete = useCallback(() => {
    onDelete(node.id)
  }, [node.id, onDelete])

  const addVariable = useCallback(() => {
    setVariables((prev: Variable[]) => [...prev, {
      name: '',
      type: 'string',
      description: '',
      required: false
    }])
  }, [])

  const updateVariable = useCallback((index: number, field: keyof Variable, value: any) => {
    setVariables((prev: Variable[]) => prev.map((v: Variable, i: number) => 
      i === index ? { ...v, [field]: value } : v
    ))
  }, [])

  const removeVariable = useCallback((index: number) => {
    setVariables((prev: Variable[]) => prev.filter((_, i) => i !== index))
  }, [])

  const updateConfig = useCallback((field: string, value: any) => {
    setConfig((prev: any) => ({ ...prev, [field]: value }))
  }, [])

  return (
    <div className="node-config-panel" ref={panelRef}>
      <div className="config-header">
        <div className="config-title">
          <span className="config-icon" style={{ color: nodeColor }}>
            {nodeIcon}
          </span>
          <span>节点配置</span>
        </div>
        <button className="config-close" onClick={onClose}>✕</button>
      </div>

      <div className="config-content">
        {/* 基本信息 */}
        <div className="config-section">
          <h4>基本信息</h4>
          <div className="config-field">
            <label>节点类型</label>
            <div className="config-type-display">
              <span className="type-icon" style={{ color: nodeColor }}>
                {nodeIcon}
              </span>
              <span>{nodeTitle}</span>
            </div>
          </div>
          
          <div className="config-field">
            <label>标题</label>
            <input
              type="text"
              value={config.title || ''}
              onChange={(e) => updateConfig('title', e.target.value)}
              placeholder="输入节点标题"
            />
          </div>

          <div className="config-field">
            <label>描述</label>
            <textarea
              value={config.desc || ''}
              onChange={(e) => updateConfig('desc', e.target.value)}
              placeholder="输入节点描述"
              rows={3}
            />
          </div>
        </div>

        {/* 模型配置 */}
        {nodeType === BlockEnum.LLM && (
          <div className="config-section">
            <h4>模型配置</h4>
            <div className="config-field">
              <label>模型名称</label>
              <input
                type="text"
                value={config.model || ''}
                onChange={(e) => updateConfig('model', e.target.value)}
                placeholder="输入模型名称"
              />
            </div>
            
            <div className="config-field">
              <label>模型参数 (JSON)</label>
              <textarea
                value={JSON.stringify(config.params || {}, null, 2)}
                onChange={(e) => {
                  try {
                    const params = JSON.parse(e.target.value)
                    updateConfig('params', params)
                  } catch {
                    // 忽略无效JSON
                  }
                }}
                placeholder="输入模型参数"
                rows={4}
              />
            </div>
          </div>
        )}

        {/* 变量配置 */}
        <div className="config-section">
          <div className="section-header">
            <h4>变量配置</h4>
            <button className="add-variable-btn" onClick={addVariable}>
              ➕ 添加变量
            </button>
          </div>
          
          {variables.length === 0 ? (
            <div className="empty-variables">
              暂无变量，点击上方按钮添加
            </div>
          ) : (
            <div className="variables-list">
              {variables.map((variable, index) => (
                <div key={index} className="variable-item">
                  <div className="variable-header">
                    <span className="variable-index">变量 {index + 1}</span>
                    <button 
                      className="remove-variable-btn"
                      onClick={() => removeVariable(index)}
                    >
                      🗑️
                    </button>
                  </div>
                  
                  <div className="variable-fields">
                    <div className="variable-field">
                      <label>名称</label>
                      <input
                        type="text"
                        value={variable.name}
                        onChange={(e) => updateVariable(index, 'name', e.target.value)}
                        placeholder="变量名称"
                      />
                    </div>
                    
                    <div className="variable-field">
                      <label>类型</label>
                      <select
                        value={variable.type}
                        onChange={(e) => updateVariable(index, 'type', e.target.value)}
                      >
                        <option value="string">字符串</option>
                        <option value="number">数字</option>
                        <option value="boolean">布尔值</option>
                        <option value="object">对象</option>
                        <option value="array">数组</option>
                      </select>
                    </div>
                    
                    <div className="variable-field">
                      <label>描述</label>
                      <input
                        type="text"
                        value={variable.description}
                        onChange={(e) => updateVariable(index, 'description', e.target.value)}
                        placeholder="变量描述"
                      />
                    </div>
                    
                    <div className="variable-field checkbox">
                      <label>
                        <input
                          type="checkbox"
                          checked={variable.required}
                          onChange={(e) => updateVariable(index, 'required', e.target.checked)}
                        />
                        必填
                      </label>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="config-actions">
        <button className="config-btn config-btn-primary" onClick={handleSave}>
          保存配置
        </button>
        <button className="config-btn config-btn-danger" onClick={handleDelete}>
          删除节点
        </button>
        <button className="config-btn config-btn-secondary" onClick={onClose}>
          取消
        </button>
      </div>
    </div>
  )
}

export default NodeConfigPanel 