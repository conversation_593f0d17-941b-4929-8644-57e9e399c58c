import React, { memo } from 'react'
import type { Node, NodePanelProps } from '../types'
import { NODE_TYPE_MAP, NODE_ICON_MAP } from '../constants'

interface NodePanelComponentProps {
  node: Node | null
  onClose: () => void
}

const NodePanel: React.FC<NodePanelComponentProps> = ({ node, onClose }) => {
  if (!node) return null

  const nodeType = node.data.type
  const nodeTitle = NODE_TYPE_MAP[nodeType] || nodeType
  const nodeIcon = NODE_ICON_MAP[nodeType] || '📦'

  return (
    <div className="node-panel">
      <div className="panel-header">
        <div className="panel-title">
          <span style={{ marginRight: '8px' }}>{nodeIcon}</span>
          {nodeTitle}
        </div>
        <div className="panel-close" onClick={onClose}>
          ✕
        </div>
      </div>
      
      <div style={{ marginBottom: '12px' }}>
        <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
          节点ID
        </div>
        <div style={{ fontSize: '12px', color: '#6b7280', fontFamily: 'monospace' }}>
          {node.id}
        </div>
      </div>

      {node.data.title && (
        <div style={{ marginBottom: '12px' }}>
          <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
            标题
          </div>
          <div style={{ fontSize: '12px', color: '#374151' }}>
            {node.data.title}
          </div>
        </div>
      )}

      {node.data.desc && (
        <div style={{ marginBottom: '12px' }}>
          <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
            描述
          </div>
          <div style={{ fontSize: '12px', color: '#374151' }}>
            {node.data.desc}
          </div>
        </div>
      )}

      <div style={{ marginBottom: '12px' }}>
        <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
          位置
        </div>
        <div style={{ fontSize: '12px', color: '#6b7280' }}>
          X: {Math.round(node.position.x)}, Y: {Math.round(node.position.y)}
        </div>
      </div>

      {node.data._runningStatus && (
        <div style={{ marginBottom: '12px' }}>
          <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: '4px' }}>
            运行状态
          </div>
          <div style={{ fontSize: '12px', color: '#374151' }}>
            {node.data._runningStatus}
          </div>
        </div>
      )}

      {/* 可以在这里添加更多节点属性的显示 */}
    </div>
  )
}

export default memo(NodePanel) 