.node-selector-popover {
  position: fixed;
  background: white;
  border-radius: 8px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
  width: 220px;
  max-height: 525px;
  overflow: hidden;
  z-index: 10001; /* 确保在最上层 */
  display: flex;
  flex-direction: column;
  animation: popover-fade-in 0.2s ease-out;
}

@keyframes popover-fade-in {
  from {
    opacity: 0;
    transform: translateY(-5px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.node-selector-search {
  padding: 10px;
  border-bottom: 1px solid #e5e7eb;
}

.node-selector-search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.node-selector-search-input:focus {
  outline: none;
  border-color: #528BFF;
  box-shadow: 0 0 0 3px rgba(82, 139, 255, 0.1);
}

.node-selector-list {
  flex: 1;
  overflow-y: auto;
  padding: 6px;
}

.node-selector-empty {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
}

.node-selector-list-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 10px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.node-selector-list-item:hover {
  background-color: #f3f4f6;
}

.node-selector-item-icon {
  font-size: 20px;
  flex-shrink: 0;
  width: 24px;
  text-align: center;
}

.node-selector-item-label {
  font-size: 13px;
  font-weight: 500;
  color: #374151;
}

/* 滚动条样式 */
.node-selector-list::-webkit-scrollbar {
  width: 5px;
}

.node-selector-list::-webkit-scrollbar-track {
  background: transparent;
}

.node-selector-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.node-selector-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
} 