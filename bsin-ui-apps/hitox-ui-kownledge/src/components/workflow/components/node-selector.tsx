import React, { useState, useEffect, useRef } from 'react'
import type { BlockEnum } from '../types'
import { NODE_TYPE_MAP, NODE_ICON_MAP, NODE_COLOR_MAP } from '../constants'
import './node-selector.css'

interface NodeSelectorProps {
  isOpen: boolean
  onClose: () => void
  onSelectNode: (nodeType: BlockEnum) => void
  position?: { x: number; y: number }
}

const NodeSelector: React.FC<NodeSelectorProps> = ({
  isOpen,
  onClose,
  onSelectNode,
  position = { x: 0, y: 0 }
}) => {
  const [searchTerm, setSearchTerm] = useState('')
  const selectorRef = useRef<HTMLDivElement>(null)

  // 点击外部关闭
  useEffect(() => {
    if (!isOpen) {
      return
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (selectorRef.current && !selectorRef.current.contains(event.target as Node)) {
        console.log('--- 点击外部区域，关闭弹框 ---')
        onClose()
      }
    }

    // 使用 requestAnimationFrame 确保在下一帧添加监听器，避免捕获到触发弹框打开的点击事件
    const frameId = requestAnimationFrame(() => {
      document.addEventListener('mousedown', handleClickOutside)
    })

    return () => {
      cancelAnimationFrame(frameId)
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  // 获取所有节点类型
  // BlockEnum 只能作为类型，不能作为值使用。我们从 NODE_TYPE_MAP 的 key 获取所有可用的节点类型
  const nodeTypes = Object.keys(NODE_TYPE_MAP) as BlockEnum[]
  
  // 过滤节点类型
  const filteredNodeTypes = nodeTypes.filter(nodeType => {
    const nodeName = NODE_TYPE_MAP[nodeType] || nodeType
    return nodeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
           nodeType.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const handleNodeSelect = (nodeType: BlockEnum) => {
    onSelectNode(nodeType)
    onClose()
  }

  return (
    <div
      ref={selectorRef}
      className="node-selector-popover"
      style={{
        left: position.x,
        top: position.y
      }}
    >
      <div className="node-selector-search">
        <input
          type="text"
          placeholder="搜索节点..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="node-selector-search-input"
        />
      </div>

      <div className="node-selector-list">
        {filteredNodeTypes.length === 0 ? (
          <div className="node-selector-empty">
            无匹配节点
          </div>
        ) : (
          filteredNodeTypes.map((nodeType) => {
            const nodeName = NODE_TYPE_MAP[nodeType] || nodeType
            const nodeIcon = NODE_ICON_MAP[nodeType] || '📦'
            
            return (
              <div
                key={nodeType}
                className="node-selector-list-item"
                onClick={() => handleNodeSelect(nodeType)}
              >
                <div className="node-selector-item-icon" style={{ color: NODE_COLOR_MAP[nodeType] }}>
                  {nodeIcon}
                </div>
                <div className="node-selector-item-label">
                  {nodeName}
                </div>
              </div>
            )
          })
        )}
      </div>
    </div>
  )
}

export default NodeSelector 