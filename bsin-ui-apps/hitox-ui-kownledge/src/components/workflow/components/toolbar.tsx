import React, { memo } from 'react'
import type { ToolbarButton } from '../types'

interface ToolbarProps {
  buttons: ToolbarButton[]
  onButtonClick: (button: ToolbarButton) => void
}

const Toolbar: React.FC<ToolbarProps> = ({ buttons, onButtonClick }) => {
  const handleButtonClick = (button: ToolbarButton) => {
    if (!button.disabled) {
      // 首先执行按钮自己的action
      if (button.action) {
        button.action()
      }
      // 然后执行父组件的onButtonClick
      onButtonClick(button)
    }
  }

  return (
    <div className="workflow-toolbar">
      {buttons.map((button) => (
        <button
          key={button.id}
          className={`toolbar-button ${button.active ? 'active' : ''} ${button.disabled ? 'disabled' : ''}`}
          onClick={() => handleButtonClick(button)}
          disabled={button.disabled}
          title={button.label}
        >
          <span style={{ marginRight: '4px' }}>{button.icon}</span>
          <span>{button.label}</span>
        </button>
      ))}
    </div>
  )
}

export default memo(Toolbar) 