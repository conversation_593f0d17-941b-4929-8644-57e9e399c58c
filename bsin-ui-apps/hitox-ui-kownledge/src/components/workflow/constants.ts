// 自定义节点类型常量
export const CUSTOM_NODE = 'custom-node'
export const CUSTOM_NOTE_NODE = 'custom-note-node'
export const CUSTOM_ITERATION_START_NODE = 'custom-iteration-start-node'
export const CUSTOM_LOOP_START_NODE = 'custom-loop-start-node'

// 自定义边类型常量
export const CUSTOM_EDGE = 'custom-edge'

// Z-index 常量
export const ITERATION_CHILDREN_Z_INDEX = 1000
export const ITERATION_NODE_Z_INDEX = 1001
export const LOOP_CHILDREN_Z_INDEX = 2000
export const LOOP_NODE_Z_INDEX = 2001

// 事件常量
export const WORKFLOW_DATA_UPDATE = 'WORKFLOW_DATA_UPDATE'
export const DSL_EXPORT_CHECK = 'DSL_EXPORT_CHECK'

// 默认配置
export const DEFAULT_RETRY_INTERVAL = 1000
export const DEFAULT_RETRY_MAX = 3
export const NODE_WIDTH_X_OFFSET = 200
export const START_INITIAL_POSITION = { x: 100, y: 100 }

// 并行限制
export const PARALLEL_LIMIT = 10
export const PARALLEL_DEPTH_LIMIT = 3

// 支持输出变量的节点类型
export const SUPPORT_OUTPUT_VARS_NODE = [
  'start',
  'llm',
  'knowledge-retrieval',
  'code',
  'template-transform',
  'http-request',
  'variable-assigner',
  'parameter-extractor',
  'tool',
  'agent',
] as const

// 节点类型映射
export const NODE_TYPE_MAP = {
  start: '开始',
  end: '结束',
  answer: '回答',
  llm: '大语言模型',
  'knowledge-retrieval': '知识检索',
  'question-classifier': '问题分类',
  'if-else': '条件判断',
  code: '代码执行',
  'template-transform': '模板转换',
  'http-request': 'HTTP请求',
  'variable-assigner': '变量分配',
  'variable-aggregator': '变量聚合',
  tool: '工具',
  'parameter-extractor': '参数提取',
  iteration: '迭代',
  'document-extractor': '文档提取',
  'list-operator': '列表操作',
  'iteration-start': '迭代开始',
  assigner: '分配器',
  agent: '智能体',
  loop: '循环',
  'loop-start': '循环开始',
  note: '注释',
} as const

// 节点图标映射
export const NODE_ICON_MAP = {
  start: '🚀',
  end: '🏁',
  answer: '💬',
  llm: '🤖',
  'knowledge-retrieval': '📚',
  'question-classifier': '🏷️',
  'if-else': '❓',
  code: '💻',
  'template-transform': '📝',
  'http-request': '🌐',
  'variable-assigner': '📊',
  'variable-aggregator': '📈',
  tool: '🔧',
  'parameter-extractor': '🔍',
  iteration: '🔄',
  'document-extractor': '📄',
  'list-operator': '📋',
  'iteration-start': '🔄',
  assigner: '📊',
  agent: '🧠',
  loop: '🔄',
  'loop-start': '🔄',
  note: '📝',
} as const

// 节点颜色映射
export const NODE_COLOR_MAP = {
  start: '#10b981',
  end: '#ef4444',
  answer: '#3b82f6',
  llm: '#8b5cf6',
  'knowledge-retrieval': '#06b6d4',
  'question-classifier': '#f59e0b',
  'if-else': '#f97316',
  code: '#6366f1',
  'template-transform': '#84cc16',
  'http-request': '#ec4899',
  'variable-assigner': '#14b8a6',
  'variable-aggregator': '#f43f5e',
  tool: '#a855f7',
  'parameter-extractor': '#06b6d4',
  iteration: '#f59e0b',
  'document-extractor': '#10b981',
  'list-operator': '#3b82f6',
  'iteration-start': '#f59e0b',
  assigner: '#14b8a6',
  agent: '#8b5cf6',
  loop: '#f59e0b',
  'loop-start': '#f59e0b',
  note: '#6b7280',
} as const

// 默认节点配置
export const DEFAULT_NODE_CONFIG = {
  width: 200,
  height: 80,
  borderRadius: 8,
  borderWidth: 1,
  borderColor: '#e2e8f0',
  backgroundColor: '#ffffff',
  selectedBorderColor: '#528BFF',
  selectedBorderWidth: 2,
  selectedShadow: '0 0 0 2px rgba(82, 139, 255, 0.2)',
} as const

// 默认边配置
export const DEFAULT_EDGE_CONFIG = {
  strokeWidth: 2,
  strokeColor: '#94a3b8',
  selectedStrokeWidth: 3,
  selectedStrokeColor: '#528BFF',
  hoverStrokeWidth: 3,
  hoverStrokeColor: '#528BFF',
} as const

// 右键菜单默认项
export const DEFAULT_CONTEXT_MENU_ITEMS = [
  { id: 'copy', label: '复制', icon: '📋' },
  { id: 'paste', label: '粘贴', icon: '📋' },
  { id: 'delete', label: '删除', icon: '🗑️' },
  { divider: true },
  { id: 'select-all', label: '全选', icon: '☑️' },
  { id: 'clear-selection', label: '清除选择', icon: '❌' },
] as const

// 工具栏默认按钮
export const DEFAULT_TOOLBAR_BUTTONS = [
  { id: 'pointer', label: '指针', icon: '👆', active: true, action: () => {} },
  { id: 'hand', label: '抓手', icon: '✋', action: () => {} },
  { id: 'zoom-in', label: '放大', icon: '🔍+', action: () => {} },
  { id: 'zoom-out', label: '缩小', icon: '🔍-', action: () => {} },
  { id: 'fit-view', label: '适应视图', icon: '📐', action: () => {} },
] as const

// 快捷键配置
export const SHORTCUTS = {
  'ctrl+c': 'copy',
  'ctrl+v': 'paste',
  'ctrl+z': 'undo',
  'ctrl+y': 'redo',
  'ctrl+a': 'select-all',
  'delete': 'delete',
  'backspace': 'delete',
  'escape': 'clear-selection',
} as const

// 工作流状态颜色映射
export const WORKFLOW_STATUS_COLOR_MAP = {
  waiting: '#f59e0b',
  running: '#3b82f6',
  succeeded: '#10b981',
  failed: '#ef4444',
  stopped: '#6b7280',
} as const

// 节点状态颜色映射
export const NODE_STATUS_COLOR_MAP = {
  'not-start': '#6b7280',
  waiting: '#f59e0b',
  running: '#3b82f6',
  succeeded: '#10b981',
  failed: '#ef4444',
  exception: '#dc2626',
  retry: '#f59e0b',
} as const 