import React, { useState, useCallback } from 'react'
import type { Node, Edge } from 'reactflow'
import Workflow from './index'
import { BlockEnum } from './types'

const WorkflowExample: React.FC = () => {
  // 初始节点数据 - 智能客服工作流
  const [nodes, setNodes] = useState<Node[]>([
    // 开始节点
    {
      id: 'start',
      type: 'custom-node',
      position: { x: 100, y: 300 },
      data: {
        type: BlockEnum.Start,
        title: '用户咨询',
        desc: '接收用户问题',
        variables: [
          { variable: 'user_input', type: 'string', des: '用户输入', required: true }
        ],
        model: '',
        params: {},
      }
    },
    
    // 问题分类节点
    {
      id: 'question-classifier',
      type: 'custom-node',
      position: { x: 350, y: 300 },
      data: {
        type: BlockEnum.QuestionClassifier,
        title: '问题分类',
        desc: '对用户问题进行智能分类',
        variables: [
          { variable: 'question_type', type: 'string', des: '问题类型', required: false },
          { variable: 'confidence', type: 'number', des: '分类置信度', required: false }
        ],
        model: 'text-classification',
        params: { threshold: 0.8 },
      }
    },
    
    // 智能分析层 - 并行处理
    {
      id: 'sentiment-analyzer',
      type: 'custom-node',
      position: { x: 600, y: 150 },
      data: {
        type: BlockEnum.LLM,
        title: '情感分析',
        desc: '分析用户情感状态和紧急程度',
        variables: [
          { variable: 'user_text', type: 'string', des: '用户文本', required: true },
          { variable: 'sentiment_score', type: 'number', des: '情感分数', required: false },
          { variable: 'urgency_level', type: 'string', des: '紧急程度', required: false }
        ],
        model: 'sentiment-analysis',
        params: { 
          analysis_type: 'multi_dimensional',
          urgency_keywords: ['紧急', '急', '立即', '马上', '崩溃', '故障']
        },
      }
    },
    
    {
      id: 'user-profile-analyzer',
      type: 'custom-node',
      position: { x: 600, y: 300 },
      data: {
        type: BlockEnum.ParameterExtractor,
        title: '用户画像分析',
        desc: '从用户历史记录中提取用户画像信息',
        variables: [
          { variable: 'user_id', type: 'string', des: '用户ID', required: true },
          { variable: 'user_profile', type: 'object', des: '用户画像', required: false },
          { variable: 'preference_tags', type: 'array', des: '偏好标签', required: false }
        ],
        model: 'user-profile',
        params: { 
          extractors: [
            { name: 'user_level', source: 'account_info' },
            { name: 'preferred_language', source: 'interaction_history' },
            { name: 'technical_expertise', source: 'question_history' }
          ]
        },
      }
    },
    
    {
      id: 'language-detector',
      type: 'custom-node',
      position: { x: 600, y: 450 },
      data: {
        type: BlockEnum.Code,
        title: '语言检测',
        desc: '检测用户使用的语言类型',
        variables: [
          { variable: 'input_text', type: 'string', des: '输入文本', required: true },
          { variable: 'detected_language', type: 'string', des: '检测语言', required: false },
          { variable: 'confidence_score', type: 'number', des: '置信度', required: false }
        ],
        model: 'language-detection',
        params: { 
          supported_languages: ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'],
          fallback_language: 'zh-CN'
        },
      }
    },
    
    // 优先级评估节点
    {
      id: 'priority-evaluator',
      type: 'custom-node',
      position: { x: 850, y: 300 },
      data: {
        type: BlockEnum.VariableAssigner,
        title: '优先级评估',
        desc: '综合评估问题的处理优先级',
        variables: [
          { variable: 'question_data', type: 'object', des: '问题数据', required: true },
          { variable: 'sentiment_data', type: 'object', des: '情感数据', required: true },
          { variable: 'user_profile', type: 'object', des: '用户画像', required: true },
          { variable: 'priority_score', type: 'number', des: '优先级分数', required: false },
          { variable: 'priority_level', type: 'string', des: '优先级等级', required: false }
        ],
        model: 'priority-calculator',
        params: { 
          factors: [
            { name: 'sentiment_urgency', weight: 0.3 },
            { name: 'user_level', weight: 0.2 },
            { name: 'question_complexity', weight: 0.25 },
            { name: 'business_impact', weight: 0.25 }
          ],
          thresholds: {
            high: 0.8,
            medium: 0.5,
            low: 0.3
          }
        },
      }
    },
    
    // 条件判断节点 - 根据问题类型分流
    {
      id: 'if-else-router',
      type: 'custom-node',
      position: { x: 1100, y: 300 },
      data: {
        type: BlockEnum.IfElse,
        title: '问题路由',
        desc: '根据问题类型进行路由',
        variables: [
          { variable: 'condition', type: 'string', des: '判断条件', required: true }
        ],
        model: '',
        params: { 
          conditions: [
            { label: '技术问题', value: 'technical' },
            { label: '产品咨询', value: 'product' },
            { label: '投诉建议', value: 'complaint' },
            { label: '账户问题', value: 'account' },
            { label: '其他问题', value: 'other' }
          ]
        },
      }
    },
    
    // 精简后的处理分支 - 技术问题
    {
      id: 'knowledge-retrieval-tech',
      type: 'custom-node',
      position: { x: 1350, y: 150 },
      data: {
        type: BlockEnum.KnowledgeRetrieval,
        title: '技术知识检索',
        desc: '从技术文档库检索相关信息',
        variables: [
          { variable: 'search_query', type: 'string', des: '搜索查询', required: true },
          { variable: 'search_results', type: 'array', des: '检索结果', required: false }
        ],
        model: 'embedding-search',
        params: { 
          collection: 'technical_docs',
          top_k: 5,
          similarity_threshold: 0.7
        },
      }
    },
    
    {
      id: 'llm-tech',
      type: 'custom-node',
      position: { x: 1600, y: 150 },
      data: {
        type: BlockEnum.LLM,
        title: '技术问题处理',
        desc: '基于检索结果生成技术解答',
        variables: [
          { variable: 'context', type: 'string', des: '上下文信息', required: true },
          { variable: 'response', type: 'string', des: '生成回答', required: false }
        ],
        model: 'gpt-4',
        params: { 
          temperature: 0.3,
          max_tokens: 1500,
          system_prompt: '你是一个专业的技术支持专家'
        },
      }
    },
    
    // 产品咨询分支
    {
      id: 'knowledge-retrieval-product',
      type: 'custom-node',
      position: { x: 1350, y: 300 },
      data: {
        type: BlockEnum.KnowledgeRetrieval,
        title: '产品知识检索',
        desc: '从产品文档库检索相关信息',
        variables: [
          { variable: 'search_query', type: 'string', des: '搜索查询', required: true },
          { variable: 'search_results', type: 'array', des: '检索结果', required: false }
        ],
        model: 'embedding-search',
        params: { 
          collection: 'product_docs',
          top_k: 3,
          similarity_threshold: 0.6
        },
      }
    },
    
    {
      id: 'template-transform-product',
      type: 'custom-node',
      position: { x: 1600, y: 300 },
      data: {
        type: BlockEnum.TemplateTransform,
        title: '产品信息格式化',
        desc: '将产品信息格式化为标准回答模板',
        variables: [
          { variable: 'product_info', type: 'object', des: '产品信息', required: true },
          { variable: 'formatted_response', type: 'string', des: '格式化回答', required: false }
        ],
        model: 'jinja2',
        params: { 
          template: '产品名称：{{name}}\n功能特点：{{features}}\n价格：{{price}}\n联系方式：{{contact}}'
        },
      }
    },
    
    // 投诉建议分支
    {
      id: 'parameter-extractor-complaint',
      type: 'custom-node',
      position: { x: 1350, y: 450 },
      data: {
        type: BlockEnum.ParameterExtractor,
        title: '投诉信息提取',
        desc: '从投诉内容中提取关键信息',
        variables: [
          { variable: 'complaint_text', type: 'string', des: '投诉内容', required: true },
          { variable: 'extracted_info', type: 'object', des: '提取信息', required: false }
        ],
        model: 'ner-extractor',
        params: { 
          extractors: [
            { name: 'product_name', pattern: '产品[名称]*[:：]?([^，。\n]+)' },
            { name: 'issue_type', pattern: '(故障|问题|错误|bug|缺陷)' },
            { name: 'priority', pattern: '(紧急|重要|一般|低)' },
            { name: 'contact_info', pattern: '(电话|邮箱|微信)[:：]?([^，。\n]+)' }
          ]
        },
      }
    },
    
    {
      id: 'tool-complaint-handler',
      type: 'custom-node',
      position: { x: 1600, y: 450 },
      data: {
        type: BlockEnum.Tool,
        title: '投诉处理工具',
        desc: '使用专业工具处理投诉建议',
        variables: [
          { variable: 'complaint_data', type: 'object', des: '投诉数据', required: true },
          { variable: 'handled_result', type: 'object', des: '处理结果', required: false }
        ],
        model: 'complaint-tool',
        params: { 
          tools: [
            { name: 'sentiment_analysis', description: '情感分析' },
            { name: 'priority_classifier', description: '优先级分类' },
            { name: 'auto_response_generator', description: '自动回复生成' }
          ],
          response_template: '感谢您的反馈，我们会认真处理您的问题。'
        },
      }
    },
    
    // 账户问题分支
    {
      id: 'document-extractor-account',
      type: 'custom-node',
      position: { x: 1350, y: 600 },
      data: {
        type: BlockEnum.DocExtractor,
        title: '账户信息提取',
        desc: '从用户描述中提取账户相关信息',
        variables: [
          { variable: 'account_description', type: 'string', des: '账户描述', required: true },
          { variable: 'extracted_account_info', type: 'object', des: '账户信息', required: false }
        ],
        model: 'account-extractor',
        params: { 
          extractors: [
            { name: 'user_id', pattern: '(用户ID|账号|用户名)[:：]?([^，。\n]+)' },
            { name: 'account_type', pattern: '(个人|企业|VIP|普通)' },
            { name: 'issue_category', pattern: '(登录|密码|权限|充值|退款)' }
          ]
        },
      }
    },
    
    {
      id: 'variable-assigner-account',
      type: 'custom-node',
      position: { x: 1600, y: 600 },
      data: {
        type: BlockEnum.VariableAssigner,
        title: '账户问题分类',
        desc: '根据提取的信息分配处理策略',
        variables: [
          { variable: 'account_info', type: 'object', des: '账户信息', required: true },
          { variable: 'assigned_strategy', type: 'object', des: '分配策略', required: false }
        ],
        model: 'account-classifier',
        params: { 
          strategies: [
            { condition: 'issue_category == "登录"', action: 'redirect_to_login_support' },
            { condition: 'issue_category == "密码"', action: 'redirect_to_password_reset' },
            { condition: 'issue_category == "权限"', action: 'escalate_to_admin' },
            { condition: 'issue_category == "充值"', action: 'redirect_to_payment_support' },
            { condition: 'issue_category == "退款"', action: 'escalate_to_finance' }
          ]
        },
      }
    },
    
    // 其他问题分支
    {
      id: 'code-executor',
      type: 'custom-node',
      position: { x: 1350, y: 750 },
      data: {
        type: BlockEnum.Code,
        title: '自定义处理',
        desc: '执行自定义代码处理特殊问题',
        variables: [
          { variable: 'input_data', type: 'object', des: '输入数据', required: true },
          { variable: 'processed_result', type: 'object', des: '处理结果', required: false }
        ],
        model: 'python',
        params: { 
          code: `
def process_question(question):
    # 自定义处理逻辑
    if "投诉" in question:
        return {"type": "complaint", "priority": "high"}
    elif "建议" in question:
        return {"type": "suggestion", "priority": "medium"}
    else:
        return {"type": "general", "priority": "low"}
          `,
          timeout: 30
        },
      }
    },
    
    // 变量聚合节点 - 合并所有分支结果
    {
      id: 'variable-aggregator',
      type: 'custom-node',
      position: { x: 1850, y: 300 },
      data: {
        type: BlockEnum.VariableAggregator,
        title: '结果聚合',
        desc: '聚合所有分支的处理结果',
        variables: [
          { variable: 'tech_result', type: 'string', des: '技术问题结果', required: false },
          { variable: 'product_result', type: 'string', des: '产品咨询结果', required: false },
          { variable: 'complaint_result', type: 'object', des: '投诉处理结果', required: false },
          { variable: 'account_result', type: 'object', des: '账户处理结果', required: false },
          { variable: 'other_result', type: 'object', des: '其他问题结果', required: false },
          { variable: 'final_response', type: 'string', des: '最终回答', required: false }
        ],
        model: 'aggregator',
        params: { 
          strategy: 'merge',
          default_response: '感谢您的咨询，我们会尽快为您处理。'
        },
      }
    },
    
    // HTTP请求节点 - 记录咨询日志
    {
      id: 'http-logger',
      type: 'custom-node',
      position: { x: 2100, y: 300 },
      data: {
        type: BlockEnum.HttpRequest,
        title: '记录日志',
        desc: '将咨询记录发送到日志系统',
        variables: [
          { variable: 'log_data', type: 'object', des: '日志数据', required: true },
          { variable: 'log_status', type: 'string', des: '记录状态', required: false }
        ],
        model: 'http',
        params: { 
          url: 'https://api.example.com/logs',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          timeout: 5000
        },
      }
    },
    
    // 结束节点
    {
      id: 'end',
      type: 'custom-node',
      position: { x: 2350, y: 300 },
      data: {
        type: BlockEnum.End,
        title: '返回结果',
        desc: '向用户返回最终处理结果',
        variables: [
          { variable: 'final_answer', type: 'string', des: '最终答案', required: true }
        ],
        model: '',
        params: {},
      }
    }
  ])

  // 初始边数据 - 智能客服工作流连接
  const [edges, setEdges] = useState<Edge[]>([
    // 主流程：开始 -> 问题分类
    {
      id: 'e-start-classifier',
      source: 'start',
      target: 'question-classifier',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.Start,
        targetType: BlockEnum.QuestionClassifier,
      }
    },
    
    // 并行分析分支：问题分类 -> 三个分析节点
    {
      id: 'e-classifier-sentiment',
      source: 'question-classifier',
      target: 'sentiment-analyzer',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.QuestionClassifier,
        targetType: BlockEnum.LLM,
      }
    },
    
    {
      id: 'e-classifier-profile',
      source: 'question-classifier',
      target: 'user-profile-analyzer',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.QuestionClassifier,
        targetType: BlockEnum.ParameterExtractor,
      }
    },
    
    {
      id: 'e-classifier-language',
      source: 'question-classifier',
      target: 'language-detector',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.QuestionClassifier,
        targetType: BlockEnum.Code,
      }
    },
    
    // 聚合分析：所有分析结果 -> 优先级评估
    {
      id: 'e-sentiment-priority',
      source: 'sentiment-analyzer',
      target: 'priority-evaluator',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.LLM,
        targetType: BlockEnum.VariableAssigner,
      }
    },
    {
      id: 'e-profile-priority',
      source: 'user-profile-analyzer',
      target: 'priority-evaluator',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.ParameterExtractor,
        targetType: BlockEnum.VariableAssigner,
      }
    },
    {
      id: 'e-language-priority',
      source: 'language-detector',
      target: 'priority-evaluator',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.Code,
        targetType: BlockEnum.VariableAssigner,
      }
    },
    
    // 最终路由：优先级评估 -> 条件判断
    {
      id: 'e-priority-router',
      source: 'priority-evaluator',
      target: 'if-else-router',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.VariableAssigner,
        targetType: BlockEnum.IfElse,
      }
    },
    
    // 技术问题分支：条件判断 -> 知识检索 -> LLM处理
    {
      id: 'e-router-tech-retrieval',
      source: 'if-else-router',
      target: 'knowledge-retrieval-tech',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.IfElse,
        targetType: BlockEnum.KnowledgeRetrieval,
        condition: 'technical'
      }
    },
    {
      id: 'e-tech-retrieval-llm',
      source: 'knowledge-retrieval-tech',
      target: 'llm-tech',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.KnowledgeRetrieval,
        targetType: BlockEnum.LLM,
      }
    },
    
    // 产品咨询分支：条件判断 -> 知识检索 -> 模板转换
    {
      id: 'e-router-product-retrieval',
      source: 'if-else-router',
      target: 'knowledge-retrieval-product',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.IfElse,
        targetType: BlockEnum.KnowledgeRetrieval,
        condition: 'product'
      }
    },
    {
      id: 'e-product-retrieval-template',
      source: 'knowledge-retrieval-product',
      target: 'template-transform-product',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.KnowledgeRetrieval,
        targetType: BlockEnum.TemplateTransform,
      }
    },
    
    // 投诉建议分支：条件判断 -> 参数提取 -> 工具处理
    {
      id: 'e-router-complaint-extractor',
      source: 'if-else-router',
      target: 'parameter-extractor-complaint',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.IfElse,
        targetType: BlockEnum.ParameterExtractor,
        condition: 'complaint'
      }
    },
    {
      id: 'e-complaint-extractor-tool',
      source: 'parameter-extractor-complaint',
      target: 'tool-complaint-handler',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.ParameterExtractor,
        targetType: BlockEnum.Tool,
      }
    },
    
    // 账户问题分支：条件判断 -> 文档提取 -> 变量分配
    {
      id: 'e-router-account-extractor',
      source: 'if-else-router',
      target: 'document-extractor-account',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.IfElse,
        targetType: BlockEnum.DocExtractor,
        condition: 'account'
      }
    },
    {
      id: 'e-account-extractor-assigner',
      source: 'document-extractor-account',
      target: 'variable-assigner-account',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.DocExtractor,
        targetType: BlockEnum.VariableAssigner,
      }
    },
    
    // 其他问题分支：条件判断 -> 代码执行
    {
      id: 'e-router-code',
      source: 'if-else-router',
      target: 'code-executor',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.IfElse,
        targetType: BlockEnum.Code,
        condition: 'other'
      }
    },
    
    // 聚合流程：所有分支结果聚合
    {
      id: 'e-llm-aggregator',
      source: 'llm-tech',
      target: 'variable-aggregator',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.LLM,
        targetType: BlockEnum.VariableAggregator,
      }
    },
    {
      id: 'e-template-aggregator',
      source: 'template-transform-product',
      target: 'variable-aggregator',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.TemplateTransform,
        targetType: BlockEnum.VariableAggregator,
      }
    },
    {
      id: 'e-complaint-aggregator',
      source: 'tool-complaint-handler',
      target: 'variable-aggregator',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.Tool,
        targetType: BlockEnum.VariableAggregator,
      }
    },
    {
      id: 'e-account-aggregator',
      source: 'variable-assigner-account',
      target: 'variable-aggregator',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.VariableAssigner,
        targetType: BlockEnum.VariableAggregator,
      }
    },
    {
      id: 'e-code-aggregator',
      source: 'code-executor',
      target: 'variable-aggregator',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.Code,
        targetType: BlockEnum.VariableAggregator,
      }
    },
    
    // 最终流程：聚合 -> 日志记录 -> 结束
    {
      id: 'e-aggregator-logger',
      source: 'variable-aggregator',
      target: 'http-logger',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.VariableAggregator,
        targetType: BlockEnum.HttpRequest,
      }
    },
    {
      id: 'e-logger-end',
      source: 'http-logger',
      target: 'end',
      type: 'custom-edge',
      data: {
        sourceType: BlockEnum.HttpRequest,
        targetType: BlockEnum.End,
      }
    }
  ])

  // 事件处理函数
  const handleNodesChange = useCallback((newNodes: Node[]) => {
    setNodes(newNodes)
    console.log('Nodes changed:', newNodes)
  }, [])

  const handleEdgesChange = useCallback((newEdges: Edge[]) => {
    setEdges(newEdges)
    console.log('Edges changed:', newEdges)
  }, [])

  const handleNodeSelect = useCallback((node: Node) => {
    console.log('Node selected:', node)
  }, [])

  const handleNodeAdd = useCallback((newNode: any, oldNodes: any) => {
    console.log('Node added:', newNode, oldNodes)
  }, [])

  const handleNodeDelete = useCallback((nodeId: string) => {
    console.log('Node deleted:', nodeId)
  }, [])

  const handleEdgeAdd = useCallback((edge: Edge) => {
    console.log('Edge added:', edge)
  }, [])

  const handleEdgeDelete = useCallback((edgeId: string) => {
    console.log('Edge deleted:', edgeId)
  }, [])

  // 保存工作流
  const handleSaveWorkflow = useCallback(() => {
    const workflowData = {
      nodes,
      edges,
      timestamp: new Date().toISOString()
    }
    console.log('Saving workflow:', workflowData)
    // 这里可以调用API保存数据
    localStorage.setItem('workflow-data', JSON.stringify(workflowData))
    alert('工作流已保存！')
  }, [nodes, edges])

  // 加载工作流
  const handleLoadWorkflow = useCallback(() => {
    const savedData = localStorage.getItem('workflow-data')
    if (savedData) {
      try {
        const workflowData = JSON.parse(savedData)
        setNodes(workflowData.nodes)
        setEdges(workflowData.edges)
        console.log('Workflow loaded:', workflowData)
        alert('工作流已加载！')
      } catch (error) {
        console.error('Failed to load workflow:', error)
        alert('加载工作流失败！')
      }
    } else {
      alert('没有找到保存的工作流！')
    }
  }, [])

  // 清空工作流
  const handleClearWorkflow = useCallback(() => {
    if (confirm('确定要清空工作流吗？')) {
      setNodes([])
      setEdges([])
      console.log('Workflow cleared')
    }
  }, [])

  // 导出工作流
  const handleExportWorkflow = useCallback(() => {
    const workflowData = {
      nodes,
      edges,
      metadata: {
        version: '1.0.0',
        exportTime: new Date().toISOString(),
        nodeCount: nodes.length,
        edgeCount: edges.length
      }
    }
    
    const dataStr = JSON.stringify(workflowData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = `workflow-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    
    URL.revokeObjectURL(url)
    console.log('Workflow exported:', workflowData)
  }, [nodes, edges])

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* 顶部工具栏 */}
      <div style={{
        padding: '12px 20px',
        background: '#f8fafc',
        borderBottom: '1px solid #e2e8f0',
        display: 'flex',
        gap: '12px',
        alignItems: 'center'
      }}>
        <h2 style={{ margin: 0, color: '#1e293b' }}>工作流编辑器</h2>
        <div style={{ flex: 1 }} />
        <button
          onClick={handleSaveWorkflow}
          style={{
            padding: '8px 16px',
            background: '#10b981',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          💾 保存
        </button>
        <button
          onClick={handleLoadWorkflow}
          style={{
            padding: '8px 16px',
            background: '#3b82f6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          📂 加载
        </button>
        <button
          onClick={handleExportWorkflow}
          style={{
            padding: '8px 16px',
            background: '#8b5cf6',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          📤 导出
        </button>
        <button
          onClick={handleClearWorkflow}
          style={{
            padding: '8px 16px',
            background: '#ef4444',
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          🗑️ 清空
        </button>
      </div>

      {/* 工作流编辑器 */}
      <div style={{ flex: 1, position: 'relative' }}>
        <Workflow
          nodes={nodes}
          edges={edges}
          onNodesChange={handleNodesChange}
          onEdgesChange={handleEdgesChange}
          onNodeSelect={handleNodeSelect}
          onNodeAdd={handleNodeAdd}
          onNodeDelete={handleNodeDelete}
          onEdgeAdd={handleEdgeAdd}
          onEdgeDelete={handleEdgeDelete}
          readOnly={false}
          showToolbar={true}
          showNodePanel={true}
        />
      </div>

      {/* 底部状态栏 */}
      <div style={{
        padding: '8px 20px',
        background: '#f1f5f9',
        borderTop: '1px solid #e2e8f0',
        fontSize: '12px',
        color: '#64748b',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <span>
          智能客服工作流 | 节点数量: {nodes.length} | 连接数量: {edges.length} | 
          流程类型: 串行+并行 | 分支数: 5 | 分析层: 3 | 布局: 优化
        </span>
        <span>工作流编辑器 v1.3.0</span>
      </div>
    </div>
  )
}

export default WorkflowExample 
