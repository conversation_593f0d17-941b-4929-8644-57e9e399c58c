import { useCallback, useState, useRef } from 'react'
import type { Node, Edge, Connection, NodeChange, EdgeChange, Viewport } from 'reactflow'
import type { BlockEnum, ContextMenuItem, ToolbarButton } from '../types'
import { DEFAULT_CONTEXT_MENU_ITEMS, DEFAULT_TOOLBAR_BUTTONS } from '../constants'

export const useWorkflowInteractions = () => {
  const [selectedNode, setSelectedNode] = useState<Node | null>(null)
  const [contextMenu, setContextMenu] = useState<{
    x: number
    y: number
    items: ContextMenuItem[]
    node?: Node
  } | null>(null)
  const [controlMode, setControlMode] = useState<'pointer' | 'hand'>('pointer')
  const clipboardRef = useRef<{ nodes: Node[]; edges: Edge[] } | null>(null)

  // 节点交互
  const handleNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    setSelectedNode(node)
    console.log('Node clicked:', node)
  }, [])

  const handleNodeDragStart = useCallback((event: React.MouseEvent, node: Node) => {
    console.log('Node drag start:', node)
  }, [])

  const handleNodeDrag = useCallback((event: React.MouseEvent, node: Node) => {
    console.log('Node dragging:', node)
  }, [])

  const handleNodeDragStop = useCallback((event: React.MouseEvent, node: Node) => {
    console.log('Node drag stop:', node)
  }, [])

  const handleNodeContextMenu = useCallback((event: React.MouseEvent, node: Node) => {
    event.preventDefault()
    const items: ContextMenuItem[] = [
      { id: 'copy', label: '复制节点', icon: '📋', action: () => console.log('Copy node:', node.id) },
      { id: 'delete', label: '删除节点', icon: '🗑️', action: () => console.log('Delete node:', node.id) },
      { id: 'divider1', label: '', icon: '', action: () => {}, divider: true },
      { id: 'properties', label: '属性', icon: '⚙️', action: () => console.log('Node properties:', node.id) },
    ]
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      items,
      node,
    })
  }, [])

  // 边交互
  const handleEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    console.log('Edge clicked:', edge)
  }, [])

  const handleEdgeContextMenu = useCallback((event: React.MouseEvent, edge: Edge) => {
    event.preventDefault()
    const items: ContextMenuItem[] = [
      { id: 'delete', label: '删除边', icon: '🗑️', action: () => console.log('Delete edge:', edge.id) },
      { id: 'properties', label: '属性', icon: '⚙️', action: () => console.log('Edge properties:', edge.id) },
    ]
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      items,
    })
  }, [])

  // 画布交互
  const handlePaneClick = useCallback(() => {
    setSelectedNode(null)
  }, [])

  const handlePaneContextMenu = useCallback((event: React.MouseEvent) => {
    event.preventDefault()
    const items: ContextMenuItem[] = [
      { id: 'add-node', label: '添加节点', icon: '➕', action: () => console.log('Add node') },
      { id: 'paste', label: '粘贴', icon: '📋', action: () => console.log('Paste'), disabled: !clipboardRef.current },
      { id: 'divider1', label: '', icon: '', action: () => {}, divider: true },
      { id: 'select-all', label: '全选', icon: '☑️', action: () => console.log('Select all') },
      { id: 'clear-selection', label: '清除选择', icon: '❌', action: () => console.log('Clear selection') },
    ]
    setContextMenu({
      x: event.clientX,
      y: event.clientY,
      items,
    })
  }, [])

  // 连接交互
  const handleConnect = useCallback((connection: Connection) => {
    console.log('Connection created:', connection)
  }, [])

  const handleConnectStart = useCallback((event: React.MouseEvent, params: any) => {
    console.log('Connection start:', params)
  }, [])

  const handleConnectEnd = useCallback((event: React.MouseEvent) => {
    console.log('Connection end')
  }, [])

  // 右键菜单交互
  const handleContextMenuClose = useCallback(() => {
    setContextMenu(null)
  }, [])

  const handleContextMenuItemClick = useCallback((item: ContextMenuItem) => {
    item.action()
  }, [])

  // 工具栏交互
  const handleToolbarButtonClick = useCallback((button: ToolbarButton) => {
    switch (button.id) {
      case 'pointer':
        setControlMode('pointer')
        break
      case 'hand':
        setControlMode('hand')
        break
      case 'zoom-in':
        console.log('Zoom in')
        break
      case 'zoom-out':
        console.log('Zoom out')
        break
      case 'fit-view':
        console.log('Fit view')
        break
      default:
        console.log('Toolbar button clicked:', button.id)
    }
  }, [])

  // 复制粘贴
  const handleCopy = useCallback((nodes: Node[], edges: Edge[]) => {
    clipboardRef.current = { nodes, edges }
    console.log('Copied to clipboard:', { nodes, edges })
  }, [])

  const handlePaste = useCallback(() => {
    if (clipboardRef.current) {
      console.log('Pasting from clipboard:', clipboardRef.current)
      return clipboardRef.current
    }
    return null
  }, [])

  // 节点添加
  const handleAddNode = useCallback((nodeType: BlockEnum, position: { x: number; y: number }) => {
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: 'custom-node',
      position,
      data: {
        type: nodeType,
        title: `新${nodeType}节点`,
        desc: '',
      },
    }
    console.log('Adding new node:', newNode)
    return newNode
  }, [])

  return {
    // 状态
    selectedNode,
    contextMenu,
    controlMode,
    
    // 节点交互
    handleNodeClick,
    handleNodeDragStart,
    handleNodeDrag,
    handleNodeDragStop,
    handleNodeContextMenu,
    
    // 边交互
    handleEdgeClick,
    handleEdgeContextMenu,
    
    // 画布交互
    handlePaneClick,
    handlePaneContextMenu,
    
    // 连接交互
    handleConnect,
    handleConnectStart,
    handleConnectEnd,
    
    // 右键菜单交互
    handleContextMenuClose,
    handleContextMenuItemClick,
    
    // 工具栏交互
    handleToolbarButtonClick,
    
    // 复制粘贴
    handleCopy,
    handlePaste,
    
    // 节点操作
    handleAddNode,
    setSelectedNode,
  }
} 