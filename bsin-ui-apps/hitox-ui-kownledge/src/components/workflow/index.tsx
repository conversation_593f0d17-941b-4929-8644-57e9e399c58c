import type { FC } from 'react'
import { use<PERSON><PERSON>back, useMemo, useState } from 'react'
import React<PERSON>low, {
  Background,
  ReactFlowProvider,
  useNodesState,
  useEdgesState,
  applyNodeChanges,
  applyEdgeChanges,
  type <PERSON>deChang<PERSON>,
  type EdgeChange,
  SelectionMode,
  useReactFlow,
  useOnViewportChange,
} from 'reactflow'
import type { Node, Edge, Viewport, Connection } from 'reactflow'
import 'reactflow/dist/style.css'
import './style.css'

// 导入自定义组件
import CustomNode from './components/custom-node'
import CustomEdge from './components/custom-edge'
import ContextMenu from './components/context-menu'
import Toolbar from './components/toolbar'
import NodePanel from './components/node-panel'
import NodeSelector from './components/node-selector'
import NodeConfigPanel from './components/node-config-panel'

// 导入hooks和类型
import { useWorkflowInteractions } from './hooks/use-workflow-interactions'
import type { WorkflowProps, ContextMenuItem, Too<PERSON>bar<PERSON>utton } from './types'
import { BlockEnum } from './types'
import { CUSTOM_NODE, CUSTOM_EDGE, DEFAULT_TOOLBAR_BUTTONS } from './constants'

const WorkflowInner: FC<WorkflowProps & {
  initialNodes: Node[]
  initialEdges: Edge[]
  nodeTypes: any
  edgeTypes: any
  finalToolbarButtons: ToolbarButton[]
  finalContextMenuItems: ContextMenuItem[]
}> = ({
  initialNodes,
  initialEdges,
  viewport,
  onNodesChange,
  onEdgesChange,
  onNodeSelect,
  onNodeAdd,
  onNodeDelete,
  onEdgeAdd,
  onEdgeDelete,
  onContextMenu,
  onPaneContextMenu,
  readOnly = false,
  showToolbar = true,
  showNodePanel = true,
  nodeTypes,
  edgeTypes,
  finalToolbarButtons,
  finalContextMenuItems,
  className = '',
  style = {height: '100%'},
}) => {
  // 必须在Provider内部调用
  const [nodes, setNodes] = useNodesState(initialNodes)
  const [edges, setEdges] = useEdgesState(initialEdges)
  const [configNode, setConfigNode] = useState<Node | null>(null)
  const [showNodeSelector, setShowNodeSelector] = useState(false)
  const [nodeSelectorPosition, setNodeSelectorPosition] = useState({ x: 0, y: 0 })
  const [addAfterNodeId, setAddAfterNodeId] = useState<string | null>(null)
  const reactFlowInstance = useReactFlow()

  // 使用交互hooks
  const {
    selectedNode,
    contextMenu,
    controlMode,
    handleNodeClick,
    handleNodeDragStart,
    handleNodeDrag,
    handleNodeDragStop,
    handleNodeContextMenu,
    handleEdgeClick,
    handleEdgeContextMenu,
    handlePaneClick: originalHandlePaneClick,
    handlePaneContextMenu,
    handleConnect,
    handleConnectStart,
    handleConnectEnd,
    handleContextMenuClose,
    handleContextMenuItemClick,
    handleToolbarButtonClick,
    setSelectedNode,
  } = useWorkflowInteractions()

  // 重写画布点击处理，添加关闭节点选择器的逻辑
  const handlePaneClick = useCallback(() => {
    originalHandlePaneClick()
    // 点击画布时关闭节点选择器
    if (showNodeSelector) {
      setShowNodeSelector(false)
      setAddAfterNodeId(null)
    }
  }, [originalHandlePaneClick, showNodeSelector])

  // 添加新节点到画布中心
  const handleAddNodeToCanvas = useCallback(() => {
    const center = reactFlowInstance.project({ x: 400, y: 200 })
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: 'custom-node',
      position: center,
      data: {
        type: BlockEnum.LLM,
        title: '新节点',
        desc: '请配置',
        variables: [],
        model: '',
        params: {},
      },
    }
    setNodes(nds => [...nds, newNode])
  }, [reactFlowInstance, setNodes])

  // 从节点选择器添加节点
  const handleSelectNodeType = useCallback((nodeType: BlockEnum) => {
    const center = reactFlowInstance.project({ x: 400, y: 200 })
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: 'custom-node',
      position: center,
      data: {
        type: nodeType,
        title: `新${nodeType}节点`,
        desc: '请配置',
        variables: [],
        model: '',
        params: {},
      },
    }
    setNodes(nds => [...nds, newNode])
    setShowNodeSelector(false)
  }, [reactFlowInstance, setNodes])

  // 在节点右侧添加新节点
  const handleAddNodeAfter = useCallback((sourceNode: Node) => {
    const newPosition = {
      x: sourceNode.position.x + 250,
      y: sourceNode.position.y
    }
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: 'custom-node',
      position: newPosition,
      data: {
        type: BlockEnum.LLM,
        title: '新节点',
        desc: '请配置',
        variables: [],
        model: '',
        params: {},
      },
    }
    setNodes(nds => [...nds, newNode])
    
    // 自动连接节点
    const newEdge: Edge = {
      id: `edge-${sourceNode.id}-${newNode.id}`,
      source: sourceNode.id,
      target: newNode.id,
      type: CUSTOM_EDGE,
      data: {
        sourceType: sourceNode.data.type,
        targetType: newNode.data.type,
      },
    }
    setEdges(eds => [...eds, newEdge])
  }, [setNodes, setEdges])

  // 删除节点
  const handleDeleteNode = useCallback((nodeId: string) => {
    setNodes(nds => nds.filter(n => n.id !== nodeId))
    setEdges(eds => eds.filter(e => e.source !== nodeId && e.target !== nodeId))
    setConfigNode(null)
  }, [setNodes, setEdges])

  // 处理节点变化
  const handleNodesChange = useCallback((changes: NodeChange[]) => {
    setNodes((nds) => applyNodeChanges(changes, nds))
  }, [setNodes])

  // 处理边变化
  const handleEdgesChange = useCallback((changes: EdgeChange[]) => {
    setEdges((eds) => applyEdgeChanges(changes, eds))
  }, [setEdges])

  // 连接变更
  const handleConnectCallback = useCallback((connection: Connection) => {
    setEdges(eds => [
      ...eds,
      {
        id: `edge-${connection.source}-${connection.target}`,
        source: connection.source!,
        target: connection.target!,
        sourceHandle: connection.sourceHandle || null,
        targetHandle: connection.targetHandle || null,
        type: CUSTOM_EDGE,
        data: {
          sourceType: 'llm',
          targetType: 'llm',
        },
      },
    ])
  }, [setEdges])

  // 节点点击弹出配置
  const handleNodeClickCallback = useCallback((event: React.MouseEvent, node: Node) => {
    // 检查点击事件的目标元素是否为加号按钮
    // 如果是，则不执行任何操作，让加号按钮自己的事件处理函数生效
    if ((event.target as HTMLElement).closest('.node-add-btn')) {
      return
    }

    // 如果点击的不是加号按钮，则正常弹出配置面板
    setConfigNode(node)
  }, []) // setConfigNode 是稳定函数，无需作为依赖

  // 节点配置保存
  const handleSaveConfig = useCallback((updatedNode: Node) => {
    setNodes(nds => nds.map(n => n.id === updatedNode.id ? { ...n, data: updatedNode.data } : n))
    setConfigNode(null)
  }, [setNodes])

  // 显示节点选择器
  const handleShowNodeSelector = useCallback((event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect()
    setNodeSelectorPosition({
      x: rect.left + rect.width / 2 - 300,
      y: rect.top + rect.height + 8
    })
    setShowNodeSelector(true)
  }, [])

  // 工具栏按钮：添加实际功能
  const enhancedToolbarButtons = useMemo(() => {
    return [
      {
        id: 'pointer',
        label: '指针',
        icon: '👆',
        active: controlMode === 'pointer',
        action: () => {
          // 控制模式切换在 handleToolbarButtonClick 中处理
        },
      },
      {
        id: 'hand',
        label: '抓手',
        icon: '✋',
        active: controlMode === 'hand',
        action: () => {
          // 控制模式切换在 handleToolbarButtonClick 中处理
        },
      },
      {
        id: 'zoom-in',
        label: '放大',
        icon: '🔍+',
        action: () => {
          reactFlowInstance.zoomIn()
        },
      },
      {
        id: 'zoom-out',
        label: '缩小',
        icon: '🔍-',
        action: () => {
          reactFlowInstance.zoomOut()
        },
      },
      {
        id: 'fit-view',
        label: '适应视图',
        icon: '📐',
        action: () => {
          reactFlowInstance.fitView()
        },
      },
      {
        id: 'add-node',
        label: '添加节点',
        icon: '➕',
        action: handleAddNodeToCanvas,
      },
    ]
  }, [controlMode, reactFlowInstance, handleAddNodeToCanvas])

  // 节点右侧加号点击 - 切换显示/隐藏
  const handleAddNodeAfterBtn = useCallback((nodeId: string, event: React.MouseEvent) => {
    console.log('--- 步骤3: handleAddNodeAfterBtn 已成功被调用 ---');
    
    // 如果当前节点已经有弹框显示，则隐藏
    if (showNodeSelector && addAfterNodeId === nodeId) {
      console.log('--- 当前节点已有弹框，隐藏弹框 ---')
      setShowNodeSelector(false)
      setAddAfterNodeId(null)
      return
    }
    
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    // 计算弹框的理想位置：在按钮右侧，垂直居中
    const popupHeight = 525 // 弹框的最大高度
    const popupWidth = 220 // 弹框的宽度
    const buttonCenterY = rect.top + rect.height / 2
    const popupTop = buttonCenterY - popupHeight / 2
    
    // 边界检查，确保弹框不会超出视口
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    let popupX = rect.right + 12 // 默认在按钮右侧
    let popupY = Math.max(10, popupTop) // 确保不会超出视口顶部
    
    // 如果右侧空间不够，显示在按钮左侧
    if (popupX + popupWidth > viewportWidth - 20) {
      popupX = rect.left - popupWidth - 12
    }
    
    // 如果底部空间不够，向上调整
    if (popupY + popupHeight > viewportHeight - 20) {
      popupY = Math.max(10, viewportHeight - popupHeight - 20)
    }
    
    setNodeSelectorPosition({
      x: popupX,
      y: popupY,
    })
    setAddAfterNodeId(nodeId)
    setShowNodeSelector(true)
  }, [showNodeSelector, addAfterNodeId])

  // 选择节点类型后，在指定节点右侧添加新节点并自动连接
  const handleSelectNodeTypeAfter = useCallback((nodeType: BlockEnum) => {
    if (!addAfterNodeId) return
    const sourceNode = nodes.find(n => n.id === addAfterNodeId)
    if (!sourceNode) return
    const newPosition = {
      x: sourceNode.position.x + 250,
      y: sourceNode.position.y
    }
    const newNode: Node = {
      id: `node-${Date.now()}`,
      type: 'custom-node',
      position: newPosition,
      data: {
        type: nodeType,
        title: `新${nodeType}节点`,
        desc: '请配置',
        variables: [],
        model: '',
        params: {},
      },
    }
    setNodes(nds => [...nds, newNode])
    // 自动连接节点
    const newEdge: Edge = {
      id: `edge-${sourceNode.id}-${newNode.id}`,
      source: sourceNode.id,
      target: newNode.id,
      type: CUSTOM_EDGE,
      data: {
        sourceType: sourceNode.data.type,
        targetType: newNode.data.type,
      },
    }
    setEdges(eds => [...eds, newEdge])
    setShowNodeSelector(false)
    setAddAfterNodeId(null)
  }, [addAfterNodeId, nodes, setNodes, setEdges])

  // 处理视口变化
  useOnViewportChange({
    onEnd: () => {
      console.log('Viewport changed')
    },
  })

  // 增强的右键菜单项（移除“在右侧添加节点”）
  const enhancedContextMenuItems = useMemo(() => {
    if (contextMenu?.node) {
      const { node } = contextMenu;
      return [
        { id: 'config', label: '配置节点', icon: '⚙️', action: () => setConfigNode(node) },
        { id: 'copy', label: '复制节点', icon: '📋', action: () => console.log('Copy node:', node.id) },
        { id: 'delete', label: '删除节点', icon: '🗑️', action: () => handleDeleteNode(node.id) },
      ]
    }
    return [
      { id: 'add-node', label: '添加节点', icon: '➕', action: handleAddNodeToCanvas },
      { id: 'select-all', label: '全选', icon: '☑️', action: () => console.log('Select all') },
      { id: 'clear-selection', label: '清除选择', icon: '❌', action: () => console.log('Clear selection') },
    ]
  }, [contextMenu, handleDeleteNode, handleAddNodeToCanvas]) // 移除 setConfigNode

  // 节点类型 useMemo，避免闭包
  const customNodeTypes = useMemo(() => ({
    ...nodeTypes,
    [CUSTOM_NODE]: (props: any) => <CustomNode {...props} onAddNodeAfter={handleAddNodeAfterBtn} />,
  }), [handleAddNodeAfterBtn, nodeTypes]) // 依赖项正确

  return (
    <div 
      id="workflow-container"
      // className={`relative h-full w-full min-w-[960px] ${className}`}
      style={style}
    >
      {/* 工具栏 */}
      {showToolbar && (
        <Toolbar 
          buttons={enhancedToolbarButtons}
          onButtonClick={handleToolbarButtonClick}
        />
      )}

      {/* 节点面板 */}
      {showNodePanel && selectedNode && (
        <NodePanel 
          node={selectedNode}
          onClose={() => setSelectedNode(null)}
        />
      )}

      {/* 右键菜单 */}
      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          items={enhancedContextMenuItems}
          onClose={handleContextMenuClose}
          onItemClick={handleContextMenuItemClick}
        />
      )}

      {/* 节点配置面板 */}
      {configNode && (
        <NodeConfigPanel
          node={configNode}
          onSave={handleSaveConfig}
          onDelete={handleDeleteNode}
          onClose={() => setConfigNode(null)}
        />
      )}

      {/* 节点选择器弹框（节点右侧加号/右键菜单添加节点时弹出） */}
      <NodeSelector
        isOpen={showNodeSelector}
        onClose={() => {
          console.log('--- 关闭弹框 ---');
          setShowNodeSelector(false);
          setAddAfterNodeId(null);
        }}
        onSelectNode={addAfterNodeId ? handleSelectNodeTypeAfter : handleSelectNodeType}
        position={nodeSelectorPosition}
      />

      {/* ReactFlow 主组件 */}
      <ReactFlow
        nodeTypes={customNodeTypes}
        edgeTypes={edgeTypes}
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onNodeClick={handleNodeClickCallback}
        onNodeDragStart={handleNodeDragStart}
        onNodeDrag={handleNodeDrag}
        onNodeDragStop={handleNodeDragStop}
        onNodeContextMenu={handleNodeContextMenu}
        onEdgeClick={handleEdgeClick}
        onEdgeContextMenu={handleEdgeContextMenu}
        onPaneClick={handlePaneClick}
        onPaneContextMenu={handlePaneContextMenu}
        onConnect={handleConnectCallback}
        onConnectStart={(event, params) => handleConnectStart(event as any, params)}
        onConnectEnd={(event) => handleConnectEnd(event as any)}
        defaultViewport={viewport}
        fitView
        multiSelectionKeyCode={null}
        deleteKeyCode={null}
        nodesDraggable={!readOnly}
        nodesConnectable={!readOnly}
        nodesFocusable={!readOnly}
        edgesFocusable={!readOnly}
        panOnDrag={controlMode === 'hand' && !readOnly}
        zoomOnPinch={!readOnly}
        zoomOnScroll={!readOnly}
        zoomOnDoubleClick={!readOnly}
        selectionKeyCode={null}
        selectionMode={SelectionMode.Partial}
        selectionOnDrag={controlMode === 'pointer' && !readOnly}
        minZoom={0.25}
        maxZoom={2}
      >
        <Background
          gap={14}
          size={2}
          color="#e5e7eb"
          className="workflow-background"
        />
      </ReactFlow>
    </div>
  )
}

const Workflow: FC<WorkflowProps> = (props) => {
  // 合并自定义节点类型
  const nodeTypes = useMemo(() => ({
    [CUSTOM_NODE]: CustomNode,
    ...props.customNodeTypes,
  }), [props.customNodeTypes])

  // 合并自定义边类型
  const edgeTypes = useMemo(() => ({
    [CUSTOM_EDGE]: CustomEdge,
    ...props.customEdgeTypes,
  }), [props.customEdgeTypes])

  // 合并工具栏按钮
  const finalToolbarButtons = useMemo(() => {
    return props.toolbarButtons || [...DEFAULT_TOOLBAR_BUTTONS]
  }, [props.toolbarButtons])

  // 合并右键菜单项
  const finalContextMenuItems = useMemo(() => {
    return props.contextMenuItems || []
  }, [props.contextMenuItems])

  // 移除多余的 customNodeTypes 和 edgeTypes 的 useMemo，因为它们在 WorkflowInner 中已经处理了
  return (
    <ReactFlowProvider>
      <WorkflowInner
        {...props}
        initialNodes={props.nodes}
        initialEdges={props.edges}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        finalToolbarButtons={finalToolbarButtons}
        finalContextMenuItems={finalContextMenuItems}
      />
    </ReactFlowProvider>
  )
}

export default Workflow 