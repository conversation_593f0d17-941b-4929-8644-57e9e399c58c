{"name": "workflow-component", "version": "1.1.0", "description": "A reusable React workflow editor component based on ReactFlow", "main": "index.tsx", "types": "index.tsx", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "dev": "vite"}, "keywords": ["react", "workflow", "reactflow", "node-editor", "flow-chart", "typescript"], "author": "Your Name", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"reactflow": "^11.10.1"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "vite": "^4.0.0"}, "files": ["index.tsx", "types.ts", "constants.ts", "style.css", "components/", "hooks/", "README.md"]}