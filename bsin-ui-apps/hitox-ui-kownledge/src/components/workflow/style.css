.workflow-panel-animation .react-flow__viewport {
  transition: transform 0.3s ease-in-out;
}

.workflow-node-animation .react-flow__node {
  transition: transform 0.2s ease-in-out;
}

#workflow-container .react-flow__nodesselection-rect {
  border: 1px solid #528BFF;
  background: rgba(21, 94, 239, 0.05);
  cursor: move;
}

#workflow-container .react-flow__selection {
  border: 1px solid #528BFF;
  background: rgba(21, 94, 239, 0.05);
}

#workflow-container .react-flow__node-custom-note {
  z-index: -1000 !important;
}

#workflow-container .react-flow {}

/* 自定义节点样式 */
.custom-node {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  min-width: 150px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
}

.custom-node:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.custom-node.selected {
  border-color: #528BFF;
  box-shadow: 0 0 0 2px rgba(82, 139, 255, 0.2);
}

/* 节点内容样式 */
.node-content {
  padding: 12px;
  text-align: center;
}

.node-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.node-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.node-title {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 120px;
}

.node-description {
  font-size: 10px;
  color: #6b7280;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 6px;
}

.node-status {
  font-size: 10px;
  font-weight: 600;
  margin-top: 4px;
}

.node-variables {
  margin-top: 6px;
}

.variables-count {
  font-size: 9px;
  color: #9ca3af;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

/* 连接点样式 */
.node-handle {
  transition: all 0.2s ease;
}

.node-handle:hover {
  transform: scale(1.2);
}

/* 状态指示器 */
.node-status-indicator {
  border-radius: 8px 8px 0 0;
}

/* 节点状态样式 */
.node-running {
  border-color: #f59e0b;
  background: #fef3c7;
}

.node-success {
  border-color: #10b981;
  background: #d1fae5;
}

.node-error {
  border-color: #ef4444;
  background: #fee2e2;
}

/* 自定义边样式 */
.custom-edge {
  stroke: #94a3b8;
  stroke-width: 2;
}

.custom-edge:hover {
  stroke: #528BFF;
  stroke-width: 3;
}

.custom-edge.selected {
  stroke: #528BFF;
  stroke-width: 3;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 160px;
  padding: 4px 0;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f3f4f6;
}

.context-menu-item.disabled {
  color: #9ca3af;
  cursor: not-allowed;
}

.context-menu-divider {
  height: 1px;
  background-color: #e5e7eb;
  margin: 4px 0;
}

/* 工具栏样式 */
.workflow-toolbar {
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 10;
  display: flex;
  gap: 8px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-button {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  background: white;
  color: #374151;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 6px;
}

.toolbar-button:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-button.active {
  background: #528BFF;
  color: white;
  border-color: #528BFF;
}

/* 特殊按钮样式 */
.add-node-btn {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.add-node-btn:hover {
  background: #059669;
  border-color: #059669;
}

.node-selector-btn {
  background: #8b5cf6;
  color: white;
  border-color: #8b5cf6;
}

.node-selector-btn:hover {
  background: #7c3aed;
  border-color: #7c3aed;
}

/* 节点面板样式 */
.node-panel {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 10;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  min-width: 300px;
  max-width: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.panel-title {
  font-weight: 600;
  color: #111827;
}

.panel-close {
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.panel-close:hover {
  background-color: #f3f4f6;
}

/* 连接线样式 */
.connection-line {
  stroke: #528BFF;
  stroke-width: 2;
  stroke-dasharray: 5,5;
}

/* 背景网格样式 */
.workflow-background {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
} 