import type {
  Edge as ReactFlowEdge,
  Node as ReactFlowNode,
  Viewport,
} from 'reactflow'

// 节点类型枚举
export enum BlockEnum {
  Start = 'start',
  End = 'end',
  Answer = 'answer',
  LLM = 'llm',
  KnowledgeRetrieval = 'knowledge-retrieval',
  QuestionClassifier = 'question-classifier',
  IfElse = 'if-else',
  Code = 'code',
  TemplateTransform = 'template-transform',
  HttpRequest = 'http-request',
  VariableAssigner = 'variable-assigner',
  VariableAggregator = 'variable-aggregator',
  Tool = 'tool',
  ParameterExtractor = 'parameter-extractor',
  Iteration = 'iteration',
  DocExtractor = 'document-extractor',
  ListFilter = 'list-operator',
  IterationStart = 'iteration-start',
  Assigner = 'assigner',
  Agent = 'agent',
  Loop = 'loop',
  LoopStart = 'loop-start',
  Note = 'note',
}

// 控制模式
export enum ControlMode {
  Pointer = 'pointer',
  Hand = 'hand',
}

// 错误处理模式
export enum ErrorHandleMode {
  Terminated = 'terminated',
  ContinueOnError = 'continue-on-error',
  RemoveAbnormalOutput = 'remove-abnormal-output',
}

// 分支类型
export type Branch = {
  id: string
  name: string
}

// 节点运行状态
export enum NodeRunningStatus {
  NotStart = 'not-start',
  Waiting = 'waiting',
  Running = 'running',
  Succeeded = 'succeeded',
  Failed = 'failed',
  Exception = 'exception',
  Retry = 'retry',
}

// 工作流运行状态
export enum WorkflowRunningStatus {
  Waiting = 'waiting',
  Running = 'running',
  Succeeded = 'succeeded',
  Failed = 'failed',
  Stopped = 'stopped',
}

// 通用节点类型
export type CommonNodeType<T = {}> = {
  _connectedSourceHandleIds?: string[]
  _connectedTargetHandleIds?: string[]
  _targetBranches?: Branch[]
  _isSingleRun?: boolean
  _runningStatus?: NodeRunningStatus
  _runningBranchId?: string
  _singleRunningStatus?: NodeRunningStatus
  _isCandidate?: boolean
  _isBundled?: boolean
  _children?: string[]
  _isEntering?: boolean
  _showAddVariablePopup?: boolean
  _holdAddVariablePopup?: boolean
  _iterationLength?: number
  _iterationIndex?: number
  _inParallelHovering?: boolean
  _waitingRun?: boolean
  _retryIndex?: number
  isInIteration?: boolean
  iteration_id?: string
  selected?: boolean
  title: string
  desc: string
  type: BlockEnum
  width?: number
  height?: number
  _loopLength?: number
  _loopIndex?: number
  isInLoop?: boolean
  loop_id?: string
} & T

// 通用边类型
export type CommonEdgeType = {
  _hovering?: boolean
  _connectedNodeIsHovering?: boolean
  _connectedNodeIsSelected?: boolean
  _isBundled?: boolean
  _sourceRunningStatus?: NodeRunningStatus
  _targetRunningStatus?: NodeRunningStatus
  _waitingRun?: boolean
  isInIteration?: boolean
  iteration_id?: string
  isInLoop?: boolean
  loop_id?: string
  sourceType: BlockEnum
  targetType: BlockEnum
}

// 节点类型
export type Node<T = {}> = ReactFlowNode<CommonNodeType<T>>
export type SelectedNode = Pick<Node, 'id' | 'data'>
export type NodeProps<T = unknown> = { id: string; data: CommonNodeType<T> }
export type NodePanelProps<T> = {
  id: string
  data: CommonNodeType<T>
}

// 边类型
export type Edge = ReactFlowEdge<CommonEdgeType>

// 工作流数据更新器
export type WorkflowDataUpdater = {
  nodes: Node[]
  edges: Edge[]
  viewport: Viewport
}

// 值选择器
export type ValueSelector = string[]

// 变量类型
export type Variable = {
  variable: string
  label?: string | {
    nodeType: BlockEnum
    nodeName: string
    variable: string
  }
  value_selector: ValueSelector
  value?: string
  options?: string[]
  required?: boolean
  isParagraph?: boolean
}

// 环境变量
export type EnvironmentVariable = {
  id: string
  name: string
  value: any
  value_type: 'string' | 'number' | 'secret'
}

// 输入变量类型
export enum InputVarType {
  textInput = 'text-input',
  paragraph = 'paragraph',
  select = 'select',
  number = 'number',
  url = 'url',
  files = 'files',
  json = 'json',
  contexts = 'contexts',
  iterator = 'iterator',
  singleFile = 'file',
  multiFiles = 'file-list',
  loop = 'loop',
}

// 输入变量
export type InputVar = {
  type: InputVarType
  label: string | {
    nodeType: BlockEnum
    nodeName: string
    variable: string
    isChatVar?: boolean
  }
  variable: string
  max_length?: number
  default?: string
  required: boolean
  hint?: string
  options?: string[]
  value_selector?: ValueSelector
}

// 模型配置
export type ModelConfig = {
  provider: string
  name: string
  mode: string
  completion_params: Record<string, any>
}

// 提示角色
export enum PromptRole {
  system = 'system',
  user = 'user',
  assistant = 'assistant',
}

// 编辑类型
export enum EditionType {
  basic = 'basic',
  jinja2 = 'jinja2',
}

// 提示项
export type PromptItem = {
  id?: string
  role?: PromptRole
  text: string
  edition_type?: EditionType
  jinja2_text?: string
}

// 变量类型
export enum VarType {
  string = 'string',
  number = 'number',
  secret = 'secret',
  boolean = 'boolean',
  object = 'object',
  file = 'file',
  array = 'array',
  arrayString = 'array[string]',
  arrayNumber = 'array[number]',
  arrayObject = 'array[object]',
  arrayFile = 'array[file]',
  any = 'any',
}

// 变量
export type Var = {
  variable: string
  type: VarType
  children?: Var[]
  isParagraph?: boolean
  isSelect?: boolean
  options?: string[]
  required?: boolean
  des?: string
  isException?: boolean
}

// 节点输出变量
export type NodeOutPutVar = {
  nodeId: string
  title: string
  vars: Var[]
  isStartNode?: boolean
}

// 块类型
export type Block = {
  classification?: string
  type: BlockEnum
  title: string
  description?: string
}

// 节点默认值
export type NodeDefault<T> = {
  defaultValue: Partial<T>
  getAvailablePrevNodes: (isChatMode: boolean) => BlockEnum[]
  getAvailableNextNodes: (isChatMode: boolean) => BlockEnum[]
  checkValid: (payload: T, t: any, moreDataForCheckValid?: any) => { isValid: boolean; errorMessage?: string }
}

// 选择块回调
export type OnSelectBlock = (type: BlockEnum, toolDefaultValue?: any) => void

// 添加节点回调
export type OnNodeAdd = (
  newNodePayload: {
    nodeType: BlockEnum
    sourceHandle?: string
    targetHandle?: string
    toolDefaultValue?: any
  },
  oldNodesPayload: {
    prevNodeId?: string
    prevNodeSourceHandle?: string
    nextNodeId?: string
    nextNodeTargetHandle?: string
  }
) => void

// 检查有效性结果
export type CheckValidRes = {
  isValid: boolean
  errorMessage?: string
}

// 右键菜单项
export type ContextMenuItem = {
  id: string
  label: string
  icon?: string
  action: () => void
  disabled?: boolean
  divider?: boolean
}

// 工具栏按钮
export type ToolbarButton = {
  id: string
  label: string
  icon?: string
  action: () => void
  active?: boolean
  disabled?: boolean
}

// 工作流组件Props
export type WorkflowProps = {
  nodes: Node[]
  edges: Edge[]
  viewport?: Viewport
  onNodesChange?: (nodes: Node[]) => void
  onEdgesChange?: (edges: Edge[]) => void
  onNodeSelect?: (node: Node) => void
  onNodeAdd?: OnNodeAdd
  onNodeDelete?: (nodeId: string) => void
  onEdgeAdd?: (edge: Edge) => void
  onEdgeDelete?: (edgeId: string) => void
  onContextMenu?: (event: React.MouseEvent, node?: Node) => void
  onPaneContextMenu?: (event: React.MouseEvent) => void
  readOnly?: boolean
  showToolbar?: boolean
  showNodePanel?: boolean
  customNodeTypes?: Record<string, React.ComponentType<any>>
  customEdgeTypes?: Record<string, React.ComponentType<any>>
  contextMenuItems?: ContextMenuItem[]
  toolbarButtons?: ToolbarButton[]
  className?: string
  style?: React.CSSProperties
} 