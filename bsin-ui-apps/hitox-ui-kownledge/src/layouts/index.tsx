import { Outlet, useNavigate, useLocation } from "umi";
import { ConfigProvider, Layout, Menu, Typography } from "antd";
import {
  HomeOutlined,
  BookOutlined,
  TeamOutlined,
  SearchOutlined,
  ShopOutlined,
  EyeOutlined,
  CompassOutlined,
  BarChartOutlined,
  FileTextOutlined,
  DatabaseOutlined,
  FolderOutlined,
  FileOutlined,
  SettingOutlined,
  UserOutlined,
  AppstoreOutlined,
} from "@ant-design/icons";
import { useEffect, useState, createContext } from "react";

const { Sider, Content } = Layout;
const { Title } = Typography;

// 添加样式
const customStyles = `
  .custom-menu-scrollbar::-webkit-scrollbar {
    display: none;
  }
`;

// 创建布局上下文，让子组件知道是否有布局菜单
export const LayoutContext = createContext({
  hasLayoutSidebar: false,
  isQiankun: false,
});

export default (props) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isQiankun, setIsQiankun] = useState(false);

  useEffect(() => {
    // 检查是否在qiankun环境中运行
    setIsQiankun(!!window.__POWERED_BY_QIANKUN__);

    // 添加样式到head
    const style = document.createElement("style");
    style.textContent = customStyles;
    document.head.appendChild(style);

    // 处理重复侧边栏的 JavaScript 解决方案
    const handleDuplicateSidebars = () => {
      // 更精确地选择侧边栏 - 只选择主要的布局侧边栏
      const mainSidebars = document.querySelectorAll(".main-sidebar");

      // 如果找到了 main-sidebar，只处理这些
      if (mainSidebars.length > 1) {
        mainSidebars.forEach((sidebar, index) => {
          if (index > 0) {
            (sidebar as HTMLElement).style.display = "none";
          }
        });
      } else {
        // 如果没有找到足够的 main-sidebar，尝试其他选择器
        const allSidebars = document.querySelectorAll(
          '[class*="upms-layout-sider"]'
        );

        if (allSidebars.length > 1) {
          allSidebars.forEach((sidebar, index) => {
            if (index > 0) {
              (sidebar as HTMLElement).style.display = "none";
            }
          });
        }
      }
    };

    // 延迟执行，确保DOM已经渲染
    const timer = setTimeout(handleDuplicateSidebars, 100);

    // 也在DOM变化时检查
    const observer = new MutationObserver(handleDuplicateSidebars);
    observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      document.head.removeChild(style);
      clearTimeout(timer);
      observer.disconnect();
    };
  }, []);

  const menuItems = [
    {
      key: "/home",
      icon: <HomeOutlined />,
      label: "首页",
    },
    {
      key: "knowledge-management",
      icon: <BookOutlined />,
      label: "知识管理",
      children: [
        {
          key: "/personal-kb",
          icon: <UserOutlined />,
          label: "个人知识库",
        },
        {
          key: "/enterprise-kb",
          icon: <TeamOutlined />,
          label: "企业知识库",
        },
        {
          key: "/full-domain-search",
          icon: <SearchOutlined />,
          label: "全模域搜索",
        },
        {
          key: "/kb-plaza",
          icon: <ShopOutlined />,
          label: "知识库广场",
        },
      ],
    },
    {
      key: "sentinel-guide",
      icon: <CompassOutlined />,
      label: "哨兵向导",
      children: [
        {
          key: "/ai-sentinel",
          icon: <EyeOutlined />,
          label: "AI哨兵",
        },
        {
          key: "/ai-guide",
          icon: <CompassOutlined />,
          label: "AI向导",
        },
      ],
    },
    {
      key: "data-report",
      icon: <BarChartOutlined />,
      label: "数据报告",
      children: [
        {
          key: "/generate-report",
          icon: <FileTextOutlined />,
          label: "生成报告",
        },
        {
          key: "/data-graph",
          icon: <DatabaseOutlined />,
          label: "数据图谱",
        },
        {
          key: "/report-template",
          icon: <BookOutlined />,
          label: "报告模板",
        },
      ],
    },
    {
      key: "file-center",
      icon: <FolderOutlined />,
      label: "文件中心",
      children: [
        {
          key: "/file-management",
          icon: <FileOutlined />,
          label: "文件管理",
        },
        {
          key: "/batch-operations",
          icon: <AppstoreOutlined />,
          label: "批量操作",
        },
      ],
    },
    {
      key: "settings",
      icon: <SettingOutlined />,
      label: "设置",
      children: [
        {
          key: "/personal-settings",
          icon: <UserOutlined />,
          label: "个人设置",
        },
        {
          key: "/app-settings",
          icon: <AppstoreOutlined />,
          label: "应用设置",
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  // 如果在qiankun环境中，维持原状
  if (isQiankun) {
    return (
      <ConfigProvider
        prefixCls="upms"
        theme={{
          token: {
            colorPrimary: "#a08ee8",
          },
        }}
      >
        <LayoutContext.Provider
          value={{ hasLayoutSidebar: false, isQiankun: true }}
        >
          <Outlet />
        </LayoutContext.Provider>
      </ConfigProvider>
    );
  }

  // 非qiankun环境，显示带侧边栏的布局
  return (
    <ConfigProvider
      prefixCls="upms"
      theme={{
        token: {
          colorPrimary: "#a08ee8",
        },
      }}
    >
      <LayoutContext.Provider
        value={{ hasLayoutSidebar: true, isQiankun: false }}
      >
        <Layout style={{ minHeight: "100vh" }}>
          <Sider
            width={240}
            theme="light"
            style={{ borderRight: "1px solid #f0f0f0" }}
            id="main-sidebar"
            className="main-sidebar"
          >
            <div style={{ padding: "16px", borderBottom: "1px solid #f0f0f0" }}>
              <Title level={4} style={{ margin: 0, color: "#a08ee8" }}>
                知识库系统
              </Title>
            </div>
            <Menu
              mode="inline"
              selectedKeys={[location.pathname]}
              items={menuItems}
              onClick={handleMenuClick}
              style={{
                border: "none",
                height: "calc(100vh - 73px)",
                overflowY: "auto",
                scrollbarWidth: "none",
                msOverflowStyle: "none",
              }}
              className="custom-menu-scrollbar"
            />
          </Sider>
          <Layout>
            <Content>
              <Outlet />
            </Content>
          </Layout>
        </Layout>
      </LayoutContext.Provider>
    </ConfigProvider>
  );
};
