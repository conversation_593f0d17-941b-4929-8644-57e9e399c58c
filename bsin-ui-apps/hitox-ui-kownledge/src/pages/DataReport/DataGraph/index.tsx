import React from 'react';
import { Card, Button, Typography } from 'antd';
import { ImageIcon, FileText } from 'lucide-react';

const { Title, Text } = Typography;

export default function DataGraphPage() {
  return (
    <div style={{ padding: '32px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <Title level={3} style={{ marginBottom: '24px' }}>数据图谱</Title>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <Title level={4} style={{ margin: 0 }}>PersonCL</Title>
          <Button 
            size="small"
            style={{ 
              backgroundColor: '#aaa1ce', 
              borderColor: '#aaa1ce',
              color: 'white'
            }}
          >
            插入知识库
          </Button>
        </div>
        <div style={{
          background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
          borderRadius: '8px',
          padding: '32px',
          minHeight: '384px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative'
        }}>
          <div style={{ position: 'relative', width: '100%', height: '320px' }}>
            {/* 文件节点 */}
            <div style={{
              position: 'absolute',
              top: '48px',
              left: '64px',
              background: '#e3f2fd',
              border: '2px solid #2196f3',
              borderRadius: '12px',
              padding: '12px',
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
              opacity: 0.85,
              transition: 'transform 0.2s',
              cursor: 'pointer'
            }}>
              <ImageIcon style={{ width: '32px', height: '32px', color: '#1976d2', display: 'block', margin: '0 auto 8px' }} />
              <Text style={{ fontSize: '12px', fontWeight: 500, textAlign: 'center', display: 'block' }}>
                saturation(1).png
              </Text>
            </div>

            <div style={{
              position: 'absolute',
              top: '48px',
              right: '64px',
              background: '#e3f2fd',
              border: '2px solid #2196f3',
              borderRadius: '12px',
              padding: '12px',
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
              opacity: 0.85,
              transition: 'transform 0.2s',
              cursor: 'pointer'
            }}>
              <ImageIcon style={{ width: '32px', height: '32px', color: '#1976d2', display: 'block', margin: '0 auto 8px' }} />
              <Text style={{ fontSize: '12px', fontWeight: 500, textAlign: 'center', display: 'block' }}>
                20250620-105529.jpg
              </Text>
            </div>

            <div style={{
              position: 'absolute',
              bottom: '48px',
              left: '64px',
              background: '#e8f5e8',
              border: '2px solid #4caf50',
              borderRadius: '12px',
              padding: '12px',
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
              opacity: 0.85,
              transition: 'transform 0.2s',
              cursor: 'pointer'
            }}>
              <FileText style={{ width: '32px', height: '32px', color: '#388e3c', display: 'block', margin: '0 auto 8px' }} />
              <Text style={{ fontSize: '12px', fontWeight: 500, textAlign: 'center', display: 'block' }}>
                飞行手册.pdf
              </Text>
            </div>

            <div style={{
              position: 'absolute',
              bottom: '48px',
              right: '64px',
              background: '#f3e5f5',
              border: '2px solid #9c27b0',
              borderRadius: '12px',
              padding: '12px',
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
              opacity: 0.85,
              transition: 'transform 0.2s',
              cursor: 'pointer'
            }}>
              <FileText style={{ width: '32px', height: '32px', color: '#7b1fa2', display: 'block', margin: '0 auto 8px' }} />
              <Text style={{ fontSize: '12px', fontWeight: 500, textAlign: 'center', display: 'block' }}>
                技术规范文档.docx
              </Text>
            </div>

            {/* SVG连接线 */}
            <svg style={{ position: 'absolute', inset: 0, width: '100%', height: '100%', pointerEvents: 'none' }}>
              <defs>
                <marker id="arrowhead-blue" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                  <polygon points="0 0, 8 3, 0 6" fill="#2196f3" />
                </marker>
                <marker id="arrowhead-green" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                  <polygon points="0 0, 8 3, 0 6" fill="#4caf50" />
                </marker>
                <marker id="arrowhead-purple" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                  <polygon points="0 0, 8 3, 0 6" fill="#9c27b0" />
                </marker>
              </defs>
              <path d="M 140 50 Q 200 30 260 50" stroke="#2196f3" strokeWidth="2.5" fill="none" markerEnd="url(#arrowhead-blue)" />
              <path d="M 140 270 Q 200 290 260 270" stroke="#4caf50" strokeWidth="2.5" fill="none" markerEnd="url(#arrowhead-green)" />
              <path d="M 80 100 Q 60 160 80 230" stroke="#9c27b0" strokeWidth="2" fill="none" strokeDasharray="4,4" markerEnd="url(#arrowhead-purple)" opacity="0.7" />
              <path d="M 320 100 Q 340 160 320 230" stroke="#9c27b0" strokeWidth="2" fill="none" strokeDasharray="4,4" markerEnd="url(#arrowhead-purple)" opacity="0.7" />
            </svg>

            {/* 标签 */}
            <div style={{
              position: 'absolute',
              top: '24px',
              left: '50%',
              transform: 'translateX(-50%)',
              background: 'rgba(255,255,255,0.9)',
              backdropFilter: 'blur(4px)',
              padding: '6px 12px',
              borderRadius: '16px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              border: '1px solid #e3f2fd'
            }}>
              <Text style={{ fontSize: '12px', fontWeight: 500, color: '#1976d2' }}>
                图像资源关联
              </Text>
            </div>

            <div style={{
              position: 'absolute',
              bottom: '24px',
              left: '50%',
              transform: 'translateX(-50%)',
              background: 'rgba(255,255,255,0.9)',
              backdropFilter: 'blur(4px)',
              padding: '6px 12px',
              borderRadius: '16px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              border: '1px solid #e8f5e8'
            }}>
              <Text style={{ fontSize: '12px', fontWeight: 500, color: '#388e3c' }}>
                文档资源关联
              </Text>
            </div>

            {/* 中心节点 */}
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              background: 'rgba(255,255,255,0.95)',
              backdropFilter: 'blur(4px)',
              borderRadius: '8px',
              padding: '16px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              border: '1px solid #e0e0e0',
              textAlign: 'center'
            }}>
              <Title level={5} style={{ margin: '0 0 4px 0', color: '#333' }}>PersonCL</Title>
              <Text style={{ fontSize: '12px', color: '#666', display: 'block', marginBottom: '8px' }}>
                知识图谱
              </Text>
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px', fontSize: '12px', color: '#999' }}>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#2196f3', borderRadius: '50%' }}></span>
                <span>4个文件</span>
                <span style={{ width: '8px', height: '8px', backgroundColor: '#4caf50', borderRadius: '50%' }}></span>
                <span>6个关联</span>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
} 
