"use client"

import { useState } from "react"
import { Card, But<PERSON>, Tree, Checkbox, Typography } from "antd"
import { DownOutlined, RightOutlined, FolderOpenOutlined, FileTextOutlined, FileImageOutlined, CheckOutlined } from "@ant-design/icons"

const { Title, Text } = Typography

const fileItems = [
  { name: "saturation(1).png", type: "image", color: "#1890ff" },
  { name: "20250620-105529.jpg", type: "image", color: "#1890ff" },
  { name: "飞行手册.pdf", type: "file", color: "#52c41a" },
  { name: "技术规范文档.docx", type: "file", color: "#722ed1" },
]

export default function FileManagementPage() {
  const [selectedTreeItem, setSelectedTreeItem] = useState<string>("")
  const [expandedTreeItems, setExpandedTreeItems] = useState<string[]>(["personal-kb", "PersonCL", "enterprise-data"])
  const [isSelectionMode, setIsSelectionMode] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<string[]>([])

  const toggleTreeExpanded = (item: string) => {
    setExpandedTreeItems((prev) => (prev.includes(item) ? prev.filter((i) => i !== item) : [...prev, item]))
  }
  
  const toggleFileSelection = (fileName: string) => {
    setSelectedFiles((prev) => (prev.includes(fileName) ? prev.filter((f) => f !== fileName) : [...prev, fileName]))
  }

  const renderFileIcon = (file: any) => {
    return file.type === "image" ? (
      <FileImageOutlined style={{ color: file.color, fontSize: '16px' }} />
    ) : (
      <FileTextOutlined style={{ color: file.color, fontSize: '16px' }} />
    )
  }

  return (
    <div style={{ padding: '32px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <Title level={3} style={{ marginBottom: '24px' }}>文件中心</Title>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <Title level={4} style={{ margin: 0 }}>文件管理</Title>
          <Button
            size="small"
            style={{ 
              backgroundColor: '#aaa1ce', 
              borderColor: '#aaa1ce',
              color: 'white'
            }}
            onClick={() => setIsSelectionMode(!isSelectionMode)}
          >
            {isSelectionMode ? "取消选择" : "生成报告"}
          </Button>
        </div>
        
        <div style={{ marginBottom: '16px' }}>
          {/* 个人知识库 */}
          <div style={{ marginBottom: '8px' }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                cursor: 'pointer',
                padding: '8px',
                borderRadius: '6px',
                backgroundColor: selectedTreeItem === "personal-kb" ? '#e6f7ff' : 'transparent',
                transition: 'background-color 0.2s'
              }}
              onClick={() => {
                toggleTreeExpanded("personal-kb")
                setSelectedTreeItem("personal-kb")
              }}
              onMouseEnter={(e) => {
                if (selectedTreeItem !== "personal-kb") {
                  e.currentTarget.style.backgroundColor = '#f5f5f5'
                }
              }}
              onMouseLeave={(e) => {
                if (selectedTreeItem !== "personal-kb") {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }
              }}
            >
              {expandedTreeItems.includes("personal-kb") ? (
                <DownOutlined style={{ fontSize: '12px', color: '#666' }} />
              ) : (
                <RightOutlined style={{ fontSize: '12px', color: '#666' }} />
              )}
              <FolderOpenOutlined style={{ fontSize: '16px', color: '#1890ff' }} />
              <Text strong style={{ fontSize: '14px' }}>个人知识库</Text>
            </div>
            
            {expandedTreeItems.includes("personal-kb") && (
              <div style={{ marginLeft: '24px', marginTop: '8px' }}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    cursor: 'pointer',
                    padding: '8px',
                    borderRadius: '6px',
                    backgroundColor: selectedTreeItem === "PersonCL" ? '#e6f7ff' : 'transparent',
                    transition: 'background-color 0.2s'
                  }}
                  onClick={() => {
                    toggleTreeExpanded("PersonCL")
                    setSelectedTreeItem("PersonCL")
                  }}
                  onMouseEnter={(e) => {
                    if (selectedTreeItem !== "PersonCL") {
                      e.currentTarget.style.backgroundColor = '#f5f5f5'
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedTreeItem !== "PersonCL") {
                      e.currentTarget.style.backgroundColor = 'transparent'
                    }
                  }}
                >
                  {expandedTreeItems.includes("PersonCL") ? (
                    <DownOutlined style={{ fontSize: '12px', color: '#666' }} />
                  ) : (
                    <RightOutlined style={{ fontSize: '12px', color: '#666' }} />
                  )}
                  <FolderOpenOutlined style={{ fontSize: '16px', color: '#722ed1' }} />
                  <Text strong style={{ fontSize: '14px' }}>PersonCL</Text>
                </div>
                
                {expandedTreeItems.includes("PersonCL") && (
                  <div style={{ marginLeft: '24px', marginTop: '8px' }}>
                    {fileItems.map((file, index) => (
                      <div
                        key={index}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '12px',
                          padding: '12px',
                          backgroundColor: '#fff',
                          borderRadius: '6px',
                          border: '1px solid #d9d9d9',
                          cursor: 'move',
                          marginBottom: '8px',
                          transition: 'all 0.2s'
                        }}
                        draggable="true"
                        onDragStart={(e) => {
                          e.dataTransfer.setData("text/plain", file.name)
                          e.dataTransfer.effectAllowed = "move"
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = '#fafafa'
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = '#fff'
                        }}
                      >
                        {renderFileIcon(file)}
                        <div style={{ flex: 1 }}>
                          <Text strong style={{ fontSize: '14px', display: 'block' }}>{file.name}</Text>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginTop: '4px' }}>
                            <span style={{ 
                              fontSize: '12px', 
                              backgroundColor: '#f0f0f0', 
                              borderRadius: '2px', 
                              padding: '2px 4px',
                              color: '#666'
                            }}>
                              FILE
                            </span>
                            <Text style={{ fontSize: '12px', color: '#999' }}>2025/6/24</Text>
                            <Text style={{ fontSize: '12px', color: '#52c41a' }}>已完成</Text>
                          </div>
                        </div>
                        {isSelectionMode && (
                          <Checkbox
                            checked={selectedFiles.includes(file.name)}
                            onChange={(e) => {
                              e.stopPropagation()
                              toggleFileSelection(file.name)
                            }}
                          />
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* 企业数据 */}
          <div style={{ marginBottom: '8px' }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                cursor: 'pointer',
                padding: '8px',
                borderRadius: '6px',
                backgroundColor: selectedTreeItem === "enterprise-data" ? '#e6f7ff' : 'transparent',
                transition: 'background-color 0.2s'
              }}
              onClick={() => {
                toggleTreeExpanded("enterprise-data")
                setSelectedTreeItem("enterprise-data")
              }}
              onMouseEnter={(e) => {
                if (selectedTreeItem !== "enterprise-data") {
                  e.currentTarget.style.backgroundColor = '#f5f5f5'
                }
              }}
              onMouseLeave={(e) => {
                if (selectedTreeItem !== "enterprise-data") {
                  e.currentTarget.style.backgroundColor = 'transparent'
                }
              }}
            >
              {expandedTreeItems.includes("enterprise-data") ? (
                <DownOutlined style={{ fontSize: '12px', color: '#666' }} />
              ) : (
                <RightOutlined style={{ fontSize: '12px', color: '#666' }} />
              )}
              <FolderOpenOutlined style={{ fontSize: '16px', color: '#fa8c16' }} />
              <Text strong style={{ fontSize: '14px' }}>企业数据</Text>
            </div>
            
            {expandedTreeItems.includes("enterprise-data") && (
              <div style={{ marginLeft: '24px', marginTop: '8px' }}>
                {["xxx-5月考勤记录", "xxx-项目实施方案", "公司Q1绩效考核"].map((fileName, index) => (
                  <div
                    key={index}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      cursor: 'pointer',
                      padding: '8px',
                      borderRadius: '6px',
                      backgroundColor: selectedTreeItem === fileName ? '#e6f7ff' : 'transparent',
                      transition: 'background-color 0.2s'
                    }}
                    onClick={() => setSelectedTreeItem(fileName)}
                    onMouseEnter={(e) => {
                      if (selectedTreeItem !== fileName) {
                        e.currentTarget.style.backgroundColor = '#f5f5f5'
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (selectedTreeItem !== fileName) {
                        e.currentTarget.style.backgroundColor = 'transparent'
                      }
                    }}
                  >
                    <FileTextOutlined style={{ 
                      fontSize: '16px', 
                      color: index === 0 ? '#1890ff' : index === 1 ? '#52c41a' : '#722ed1' 
                    }} />
                    <Text style={{ fontSize: '14px', flex: 1 }}>{fileName}</Text>
                    {isSelectionMode && (
                      <Checkbox
                        checked={selectedFiles.includes(fileName)}
                        onChange={(e) => {
                          e.stopPropagation()
                          toggleFileSelection(fileName)
                        }}
                      />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
        
        {/* 拖拽区域 */}
        <div
          style={{
            border: '2px dashed #d9d9d9',
            borderRadius: '8px',
            padding: '32px',
            textAlign: 'center',
            color: '#999',
            backgroundColor: '#fafafa',
            transition: 'all 0.2s',
            marginTop: '24px'
          }}
          onDragOver={(e) => {
            e.preventDefault()
            e.dataTransfer.dropEffect = "move"
            e.currentTarget.style.borderColor = '#1890ff'
            e.currentTarget.style.backgroundColor = '#f6ffed'
          }}
          onDragLeave={(e) => {
            e.currentTarget.style.borderColor = '#d9d9d9'
            e.currentTarget.style.backgroundColor = '#fafafa'
          }}
          onDrop={(e) => {
            e.preventDefault()
            const fileName = e.dataTransfer.getData("text/plain")
            console.log("文件移动:", fileName)
            e.currentTarget.style.borderColor = '#d9d9d9'
            e.currentTarget.style.backgroundColor = '#fafafa'
          }}
        >
          <div style={{ fontSize: '32px', marginBottom: '8px' }}>📁</div>
          <Text style={{ display: 'block', marginBottom: '4px' }}>拖拽文件到此处进行移动</Text>
          <Text style={{ fontSize: '12px', color: '#bfbfbf' }}>支持拖拽重新组织文件结构</Text>
        </div>
      </Card>
    </div>
  )
} 
