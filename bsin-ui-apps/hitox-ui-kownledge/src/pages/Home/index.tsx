import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Avatar, List, Badge, Tag, Table, Typography, Space, Button } from 'antd';
import { 
  UserOutlined, 
  TeamOutlined, 
  SettingOutlined, 
  AppstoreOutlined,
  SafetyOutlined,
  ClusterOutlined,
  EyeOutlined,
  HistoryOutlined,
  RiseOutlined,
  FallOutlined
} from '@ant-design/icons';
import { Line, Pie, Column } from '@ant-design/charts';

const { Title, Text } = Typography;

const PermissionDashboard = () => {
  // 模拟数据
  const [dashboardData, setDashboardData] = useState({
    totalUsers: 1248,
    activeUsers: 892,
    totalRoles: 28,
    totalPermissions: 156,
    totalApps: 12,
    onlineUsers: 324
  });

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>知识库系统概览</Title>
        {/* <Text type="secondary">实时监控系统状态和用户活动</Text> */}
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={dashboardData.totalUsers}
              prefix={<UserOutlined style={{ color: '#1890ff' }} />}
              suffix={
                <div style={{ fontSize: '12px', color: '#52c41a' }}>
                  <RiseOutlined /> +12%
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={dashboardData.activeUsers}
              prefix={<TeamOutlined style={{ color: '#52c41a' }} />}
              suffix={
                <div style={{ fontSize: '12px', color: '#52c41a' }}>
                  <RiseOutlined /> +8%
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="在线用户"
              value={dashboardData.onlineUsers}
              prefix={<SafetyOutlined style={{ color: '#fa8c16' }} />}
              suffix={
                <div style={{ fontSize: '12px', color: '#fa8c16' }}>
                  <FallOutlined /> -3%
                </div>
              }
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="系统应用"
              value={dashboardData.totalApps}
              prefix={<AppstoreOutlined style={{ color: '#722ed1' }} />}
              suffix={
                <div style={{ fontSize: '12px', color: '#52c41a' }}>
                  <RiseOutlined /> +2
                </div>
              }
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default PermissionDashboard;