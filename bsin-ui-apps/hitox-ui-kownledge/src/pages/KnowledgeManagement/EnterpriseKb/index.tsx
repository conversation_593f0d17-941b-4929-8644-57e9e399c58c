import { KnowledgeProvider } from "@/components/Knowledge/KnowledgeContext";
import KnowledgeSidebarColumn from "@/components/Knowledge/KnowledgeSidebarColumn";
import KnowledgeDocListColumn from "@/components/Knowledge/KnowledgeDocListColumn";
import RightCard from "./rightCard";

export default function PersonalKbPage() {
  return (
    <KnowledgeProvider>
      <div style={{ 
        display: 'flex', 
        height: '100vh', 
        width: '100%', 
        padding: '1rem', 
        gap: '1.5rem',
        boxSizing: 'border-box',
        overflow: 'hidden'
      }}>
        <KnowledgeSidebarColumn />
        <KnowledgeDocListColumn />
        <RightCard />
      </div>
    </KnowledgeProvider>
  );
} 
