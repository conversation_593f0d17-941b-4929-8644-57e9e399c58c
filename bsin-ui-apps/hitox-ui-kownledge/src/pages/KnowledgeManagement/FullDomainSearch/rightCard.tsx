import { useState } from "react";
import { <PERSON>, Button, Input, Badge, Spin, Typography, Select, Divider, Empty, Tag, Space } from "antd";
import { LoadingOutlined, SendOutlined, FileTextOutlined, PictureOutlined, AudioOutlined, FilterOutlined, HistoryOutlined, CloseOutlined, SearchOutlined, CloudUploadOutlined, AlignLeftOutlined } from "@ant-design/icons";
import { toast } from "sonner";
import React from "react";
import { searchModal } from "@/services/knowledge-service";

const { Title, Text } = Typography;
const { Option } = Select;

export default function FullDomainSearchPage() {
  const [searchText, setSearchText] = useState("");
  const [searchMode, setSearchMode] = useState<'text' | 'voice' | 'image'>("text");
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showFilter, setShowFilter] = useState(false);
  const [dateRange, setDateRange] = useState<any>(null);
  const [selectedCreator, setSelectedCreator] = useState<string>("");
  const [selectedFormat, setSelectedFormat] = useState<string>("");
  const [showHistory, setShowHistory] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [searchHistory, setSearchHistory] = useState<any[]>([]);
  const [activeSearchTab, setActiveSearchTab] = useState("all");

  // 搜索主函数
  const handleCrossModalSearch = async () => {
    if (!searchText && searchMode === 'text') {
      toast.error('请输入搜索内容');
      return;
    }
    setShowHistory(false); // 搜索时关闭历史区
    setIsSearching(true);
    setHasSearched(true);
    try {
      const response = await searchModal({
        query: searchText,
        top_k: 20
      });
      setSearchResults(Array.isArray(response) ? response : []);
      toast.success('搜索完成');
    } catch (error: any) {
      toast.error(error.response?.detail || '搜索失败，请稍后重试');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
    // 记录历史
    if (searchText) {
      const newHistory = {
        id: Date.now().toString(),
        query: searchText,
        type: searchMode,
        time: new Date().toLocaleString()
      };
      setSearchHistory([newHistory, ...searchHistory.slice(0, 9)]);
    }
  };

  // 语音搜索
  const handleVoiceSearch = () => {
    toast.info('正在启动语音识别...');
    setSearchMode('voice');
    setTimeout(() => {
      setSearchText('智能OA系统设计文档');
      toast.success('语音识别成功');
      handleCrossModalSearch();
    }, 2000);
  };

  // 图片搜索
  const handleImageSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    toast.success(`${file.name} 上传成功`);
    setSearchMode('image');
    setTimeout(() => {
      setSearchText('产品设计图');
      handleCrossModalSearch();
    }, 1500);
  };

  // 历史点击
  const handleHistoryItemClick = (item: any) => {
    setSearchText(item.query);
    setSearchMode(item.type);
    setShowHistory(false);
    setTimeout(() => {
      handleCrossModalSearch();
    }, 0);
  };

  // 清空历史
  const clearSearchHistory = () => {
    setSearchHistory([]);
    toast.success('搜索历史已清除');
  };

  // 结果类型图标
  const getFileIcon = (type: string, format?: string) => {
    if (type === 'document') {
      switch (format) {
        case 'pdf': return <FileTextOutlined style={{ fontSize: 24, color: '#f5222d' }} />;
        case 'word': return <FileTextOutlined style={{ fontSize: 24, color: '#1890ff' }} />;
        case 'excel': return <FileTextOutlined style={{ fontSize: 24, color: '#52c41a' }} />;
        default: return <FileTextOutlined style={{ fontSize: 24, color: '#faad14' }} />;
      }
    } else if (type === 'image') {
      return <PictureOutlined style={{ fontSize: 24, color: '#722ed1' }} />;
    }
    return <FileTextOutlined style={{ fontSize: 24, color: '#faad14' }} />;
  };

  // 相关度颜色
  const getRelevanceColor = (relevance: number) => {
    if (relevance >= 90) return '#52c41a';
    if (relevance >= 70) return '#faad14';
    return '#d9d9d9';
  };

  // 筛选结果
  const filteredSearchResults = searchResults.filter(item => {
    if (activeSearchTab !== 'all' && item.type !== activeSearchTab) return false;
    if (selectedFormat && item.format !== selectedFormat) return false;
    if (selectedCreator && item.creator !== selectedCreator) return false;
    if (dateRange && item.createTime) {
      // 这里可补充日期筛选逻辑
    }
    return true;
  });

  return (
    <div style={{ flex: 1 }}>
      <Card style={{ height: '100%' }} styles={{ body: { padding: 16, display: 'flex', flexDirection: 'column', height: '100%' } }}>
        <div>
          <Title level={4} style={{ margin: '0 0 24px 0', fontSize: 18, fontWeight: 500 }}>
            全模域搜索
          </Title>
          
          <Card styles={{ body: { padding: 24 } }}>
            {/* 搜索框+按钮区 */}
            <div style={{ display: 'flex', alignItems: 'center', gap: 8, marginBottom: 24 }}>
              <Space.Compact style={{ flex:1}}>
                <Input placeholder="请输入搜索内容..."
                  value={searchText}
                  onChange={e => setSearchText(e.target.value)}
                  onPressEnter={handleCrossModalSearch}
                  style={{ flex: 1 }}
                />
                <Button 
                  type="primary"  
                  onClick={handleCrossModalSearch}
                  disabled={isSearching}
                  icon={isSearching ? <LoadingOutlined spin /> : <SendOutlined />}
                />
              </Space.Compact>
              
              {/* 文本按钮 */}
              <Button
                size="small"
                type={searchMode === 'text' ? 'primary' : 'default'}
                style={{
                  height: 32,
                  width: 32,
                  padding: 0,
                }}
                onClick={() => setSearchMode('text')}
                title="文本搜索"
                icon={<AlignLeftOutlined />}
              />
              
              {/* 语音按钮 */}
              <Button
                size="small"
                type={searchMode === 'voice' ? 'primary' : 'default'}
                style={{
                  height: 32,
                  width: 32,
                  padding: 0,
                }}
                onClick={handleVoiceSearch}
                title="语音搜索"
                icon={<AudioOutlined />}
              />
              
              {/* 图片按钮 */}
              <label
                style={{
                  height: 32,
                  width: 32,
                  padding: 0,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  cursor: 'pointer',
                  borderRadius: 6,
                  border: '1px solid #d9d9d9',
                }}
                title="图片搜索"
              >
                <CloudUploadOutlined />
                <input type="file" accept="image/*" style={{ display: 'none' }} onChange={handleImageSearch} />
              </label>
              
              {/* 筛选按钮 */}
              <Button
                size="small"
                style={{ height: 32, width: 32, padding: 0 }}
                onClick={() => setShowFilter(v => !v)}
                title="筛选"
                icon={<FilterOutlined />}
              />
              
              {/* 历史按钮 */}
              <Button
                size="small"
                style={{ height: 32, width: 32, padding: 0 }}
                onClick={() => setShowHistory(true)}
                title="历史"
                icon={<HistoryOutlined />}
              />
            </div>

            {/* 筛选弹窗 */}
            {showFilter && (
              <Card
                size="small"
                style={{
                  position: 'absolute',
                  zIndex: 20,
                  right: 0,
                  top: 80,
                  width: 320,
                  boxShadow: '0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05)'
                }}
                styles={{ body: { padding: 16 } }}
              >
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 8 }}>
                  <Text strong style={{ fontSize: 14 }}>筛选条件</Text>
                  <Button size="small" type="text" onClick={() => setShowFilter(false)} icon={<CloseOutlined />} />
                </div>
                
                <div style={{ marginBottom: 8 }}>
                  <Text style={{ fontSize: 12, display: 'block', marginBottom: 4 }}>类型</Text>
                  <Select
                    style={{ width: '100%' }}
                    size="small"
                    value={activeSearchTab}
                    onChange={setActiveSearchTab}
                  >
                    <Option value="all">全部</Option>
                    <Option value="document">文档</Option>
                    <Option value="image">图片</Option>
                  </Select>
                </div>
                
                <div style={{ marginBottom: 8 }}>
                  <Text style={{ fontSize: 12, display: 'block', marginBottom: 4 }}>格式</Text>
                  <Input
                    size="small"
                    value={selectedFormat}
                    onChange={e => setSelectedFormat(e.target.value)}
                    placeholder="如 pdf, word, excel, jpg..."
                  />
                </div>
                
                <div>
                  <Text style={{ fontSize: 12, display: 'block', marginBottom: 4 }}>创建人</Text>
                  <Input
                    size="small"
                    value={selectedCreator}
                    onChange={e => setSelectedCreator(e.target.value)}
                    placeholder="输入创建人"
                  />
                </div>
              </Card>
            )}

            {/* 搜索内容/历史区块 */}
            <div style={{ marginTop: 24 }}>
              {showHistory ? (
                <>
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 8 }}>
                    <Text strong style={{ fontSize: 14 }}>搜索历史</Text>
                    <Button size="small" type="text" onClick={() => setShowHistory(false)} icon={<CloseOutlined />} />
                  </div>
                  {searchHistory.length === 0 ? (
                    <Empty
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description="暂无历史记录"
                      style={{ padding: '32px 0' }}
                    />
                  ) : (
                    <>
                      <div style={{ marginBottom: 8 }}>
                        {searchHistory.map(item => (
                          <div
                            key={item.id}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              borderBottom: '1px solid #f0f0f0',
                              padding: '4px 0',
                              cursor: 'pointer'
                            }}
                            onClick={() => handleHistoryItemClick(item)}
                            onMouseEnter={e => e.currentTarget.style.backgroundColor = '#fafafa'}
                            onMouseLeave={e => e.currentTarget.style.backgroundColor = 'transparent'}
                          >
                            <div style={{ flex: 1, overflow: 'hidden' }}>
                              <Text style={{ fontSize: 12, color: '#333', marginRight: 8 }}>{item.query}</Text>
                              <Tag size="small" color="blue" style={{ fontSize: 10, marginRight: 8 }}>
                                {item.type === 'text' ? '文本' : item.type === 'voice' ? '语音' : '图片'}
                              </Tag>
                              <Text style={{ fontSize: 12, color: '#999' }}>{item.time}</Text>
                            </div>
                          </div>
                        ))}
                      </div>
                      <Button size="small" block onClick={clearSearchHistory}>
                        清空历史
                      </Button>
                    </>
                  )}
                </>
              ) : hasSearched && (
                isSearching ? (
                  <div style={{ textAlign: 'center', padding: '32px 0' }}>
                    <Spin size="large" />
                    <Text style={{ display: 'block', marginTop: 8, fontSize: 14, color: '#666' }}>
                      正在搜索...
                    </Text>
                  </div>
                ) : filteredSearchResults.length > 0 ? (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                    {filteredSearchResults.map((result, index) => (
                      <Card
                        key={index}
                        size="small"
                        hoverable
                        styles={{ body: { padding: 16 } }}
                      >
                        <div style={{ display: 'flex', alignItems: 'flex-start', gap: 12 }}>
                          <div style={{ flexShrink: 0 }}>
                            {getFileIcon(result.type, result.format)}
                          </div>
                          <div style={{ flex: 1 }}>
                            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 8 }}>
                              <Title level={5} style={{ margin: 0, fontSize: 14 }}>{result.title}</Title>
                              <Text style={{ fontSize: 12, color: getRelevanceColor(result.relevance) }}>
                                相关度: {result.relevance}%
                              </Text>
                            </div>
                            <Text style={{ fontSize: 14, color: '#666', display: 'block', marginBottom: 8 }}>
                              {result.content}
                            </Text>
                            {result.url && (
                              <a
                                href={result.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                style={{ fontSize: 12, color: '#1890ff' }}
                              >
                                查看原文件
                              </a>
                            )}
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <Empty
                    image={<FileTextOutlined style={{ fontSize: 32, color: '#d9d9d9' }} />}
                    description="未找到相关结果"
                    style={{ padding: '32px 0' }}
                  />
                )
              )}
            </div>
          </Card>
        </div>
      </Card>
    </div>
  );
} 
