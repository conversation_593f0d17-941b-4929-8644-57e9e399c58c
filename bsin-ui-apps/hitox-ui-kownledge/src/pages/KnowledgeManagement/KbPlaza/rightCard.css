.enterprise-kb-container {
  height: 100%;
  width: 100%;
  max-width: 100%;
}

.enterprise-kb-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.enterprise-kb-header {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.enterprise-kb-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
}

.enterprise-kb-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 12px;
  overflow-y: auto;
  flex: 1;
  padding-right: 8px;
}

.enterprise-kb-grid::-webkit-scrollbar {
  width: 6px;
}

.enterprise-kb-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.enterprise-kb-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.enterprise-kb-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.enterprise-kb-card {
  position: relative;
  overflow: hidden;
  max-width: 100%;
}

.enterprise-kb-share-btn {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
  background-color: #aaa1ce;
  border-color: #aaa1ce;
  color: white;
}

.enterprise-kb-link-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  background-color: #aaa1ce;
  border-color: #aaa1ce;
  color: white;
}

.enterprise-kb-link-btn.disabled {
  background-color: #9CA3AF;
  border-color: #9CA3AF;
}

.enterprise-kb-image-container {
  height: 128px;
  background: linear-gradient(135deg, #eff6ff 0%, #faf5ff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.enterprise-kb-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.enterprise-kb-card-content {
  padding: 16px;
}

.enterprise-kb-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.enterprise-kb-card-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
}

.enterprise-kb-category {
  font-size: 12px;
  background-color: #e6f7ff;
  color: #1890ff;
  border-radius: 4px;
  padding: 2px 8px;
  flex-shrink: 0;
}

.enterprise-kb-description {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .enterprise-kb-grid {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 769px) and (max-width: 1200px) {
  .enterprise-kb-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
