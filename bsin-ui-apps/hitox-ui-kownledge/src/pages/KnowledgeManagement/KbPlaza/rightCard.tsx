"use client"

import { useState } from "react"
import { <PERSON>, Button, Modal } from "antd"
import { ShareAltOutlined, BulbOutlined } from "@ant-design/icons"
import { buildStaticResourceUrl } from "@/services/backend-config"
import "./rightCard.css"

const enterpriseKnowledgeCards = [
  {
    id: "low-altitude-training",
    title: "低空经济政策培训",
    description: "全面解读低空经济发展政策，包括法规要求、申报流程和合规指导。",
    image: "images/dikongjingji.jpg",
    category: "政策培训",
  },
  {
    id: "drone-model-query",
    title: "无人机型号查询",
    description: "提供各类无人机型号参数查询，包括技术规格、适用场景和认证信息。",
    image: "images/wurenjixinghao.jpg",
    category: "设备查询",
  },
  {
    id: "pilot-exam-training",
    title: "无人机飞手考试培训",
    description: "专业的无人机操作员培训课程，涵盖理论知识和实操技能。",
    image: "images/wurenjifeishou.jpeg",
    category: "技能培训",
  },
  {
    id: "airspace-management",
    title: "空域管理系统",
    description: "低空空域申请与管理平台，提供飞行计划申报和审批服务。",
    image: "images/kongyuguanli.jpg",
    category: "空域管理",
  },
  {
    id: "safety-regulations",
    title: "飞行安全规范",
    description: "详细的无人机飞行安全操作规范和应急处置预案。",
    image: "images/feixinganquan.png",
    category: "安全规范",
  },
  {
    id: "weather-monitoring",
    title: "气象监测服务",
    description: "实时气象数据监测，为低空飞行提供精准的天气预报服务。",
    // image: "images/qixiang.jpg",
    category: "气象服务",
  },
]

export default function EnterpriseKbPage() {
  const [linkedCards, setLinkedCards] = useState<string[]>([])
  const [showLinkDialog, setShowLinkDialog] = useState(false)
  const [selectedCardForLink, setSelectedCardForLink] = useState<string>("")

  const handleLinkClick = (cardId: string) => {
    setSelectedCardForLink(cardId)
    setShowLinkDialog(true)
  }

  return (
    <div style={{ flex: 1 }}>
    <Card className="enterprise-kb-container">
      <div className="enterprise-kb-content">
        <div className="enterprise-kb-header">
          <h3 className="enterprise-kb-title">知识库广场</h3>
        </div>
        <div className="enterprise-kb-grid">
          {enterpriseKnowledgeCards.map((card) => (
            <Card
              key={card.id}
              hoverable
              className="enterprise-kb-card"
              style={{ width: 300 }}
              styles={{ body: { padding: 0 } }}
            >
              <Button
                size="small"
                shape="circle"
                icon={<ShareAltOutlined />}
                className="enterprise-kb-share-btn"
              />
              <Button
                size="small"
                shape="circle"
                icon={<BulbOutlined />}
                disabled={linkedCards.includes(card.id)}
                onClick={() => !linkedCards.includes(card.id) && handleLinkClick(card.id)}
                className={`enterprise-kb-link-btn ${linkedCards.includes(card.id) ? 'disabled' : ''}`}
              />
              <div className="enterprise-kb-image-container">
                <img
                  src={buildStaticResourceUrl(card.image || "/placeholder.svg")}
                  alt={card.title}
                  className="enterprise-kb-image"
                />
              </div>
              <div className="enterprise-kb-card-content">
                <div className="enterprise-kb-card-header">
                  <h4 className="enterprise-kb-card-title">{card.title}</h4>
                  <span className="enterprise-kb-category">
                    {card.category}
                  </span>
                </div>
                <p className="enterprise-kb-description">
                  {card.description}
                </p>
              </div>
            </Card>
          ))}
        </div>
      </div>
    </Card>
    </div>
  )
} 
