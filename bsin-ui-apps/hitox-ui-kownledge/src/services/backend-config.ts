// 后端配置 - 统一管理后端地址
export const getBackendUrl = () => {
  // 检查是否有容器环境变量
  const PROTOCOL = process.env.PROTOCOL;
  const DOMAIN = process.env.DOMAIN;
  
  // 如果有环境变量，使用环境变量组成的地址
  if (PROTOCOL && DOMAIN) {
    return `${PROTOCOL}://${DOMAIN}`;
  }
  
  // 否则使用本地开发地址
  return 'http://localhost:8001';
};

// 获取文档服务端口
export const getDocumentPort = () => {
  // 检查是否有容器环境变量
  const PROTOCOL = process.env.PROTOCOL;
  const DOMAIN = process.env.DOMAIN;
  
  // 如果有环境变量，说明是正式环境，使用31112端口
  if (PROTOCOL && DOMAIN) {
    return '31112';
  }
  
  // 否则是开发环境，使用31121端口
  return '31121';
};

// 获取文档服务完整URL
export const getDocumentServiceUrl = () => {
  const port = getDocumentPort();
  return `http://*************:${port}`;
};

// 构建静态资源URL
export const buildStaticResourceUrl = (path: string) => {
  const normalizedPath = path.startsWith('/') ? path : `/${path}`;
  
  // 开发环境直接使用相对路径
  if (process.env.NODE_ENV === 'development') {
    return normalizedPath
  }
  // 生产环境使用配置的base路径
  const prefix = '/hitox-ui-knowledge'
  return `${prefix}/${normalizedPath}`
};

// 导出常用的 API 路径构建函数
export const buildApiUrl = (path: string) => {
  const baseUrl = getBackendUrl();
  return `${baseUrl}${path}`;
};