import { request } from '@umijs/max';

// 获取知识库基础路径
// /hitox/sg/api/v1
const getKnowledgeBasePath = () => {
  const basePath = process.env.NODE_ENV === 'development' 
    ? '' 
    : (process.env.baseUrl || 'http://snbb.hitox.top:31114');
  
  return `${basePath}${process.env.contextPath_knowledge || '/hitox/sg/api/v1'}`;
};
// /fe-sg/api
const getChatBasePath = () => {
  const basePath = process.env.NODE_ENV === 'development' 
    ? '' 
    : (process.env.baseUrl || 'http://snbb.hitox.top:31114');
  
  return `${basePath}${process.env.contextPath_chat || '/fe-sg/api'}`;
};

// 获取当前用户信息
export const getCurrentUser = () => {
  return request(`${getKnowledgeBasePath()}/users/me`, {
    method: 'GET',
  });
};

//-------------------  知识库相关  ----------------//
// 获取个人知识库列表
export const getPersonalKnowledgeBases = (params?: any) => {
  return request(`${getKnowledgeBasePath()}/knowledge/bases`, {
    method: 'GET',
    params,
  });
};

// 创建知识库
export const createKnowledgeBase = (params: any) => {  
  return request(`${getKnowledgeBasePath()}/knowledge/bases`, {
    method: 'POST',
    data: params,
  });
};

// 删除知识库
export const deleteKnowledgeBase = (kbId: number) => {
  return request(`${getKnowledgeBasePath()}/knowledge/bases/${kbId}`, {
    method: 'DELETE',
  });
};

// 上传文件
export const oploadFile = (kbId: number, formData: any) => {
  return request(`${getKnowledgeBasePath()}/knowledge/bases/${kbId}/documents/upload`, {
    method: 'POST',
    data: formData,
    headers: { 'Content-Type': 'multipart/form-data' }
  });
};

// 获取文件们
export const getDocuments = (kbId: number) => {
  return request(`${getKnowledgeBasePath()}/knowledge/bases/${kbId}/documents`, {
    method: 'GET',
  });
};

// 获取文件
export const getDocument = (kbId: number, docId: number) => {
  return request(`${getKnowledgeBasePath()}/knowledge/bases/${kbId}/documents/${docId}`, {
    method: 'GET',
  });
};

// 删除文件
export const delDocument = (kbId: number, docId: number) => {
  return request(`${getKnowledgeBasePath()}/knowledge/bases/${kbId}/documents/${docId}`, {
    method: 'DELETE',
  });
};

// 全模域搜索
export const searchModal = (params: any) => {  
  return request(`${getKnowledgeBasePath()}/knowledge/cross_modal_search`, {
    method: 'POST',
    data: params,
  });
};

//-------------------   RAGFlow 相关  ----------------/
// 获取聊天助手
export const getChatAssistant = () => {
  return request(`${getKnowledgeBasePath()}/ragflow/chat-assistant`, {
    method: 'GET',
  });
};

// 创建聊天助手
export const postChatAssistant = (params:any) => {
  return request(`${getKnowledgeBasePath()}/ragflow/chat-assistant`, {
    method: 'POST',
    data: params,  
  });
};

// 更新聊天助手
export const putChatAssistant = (id:string,params:any) => {
  return request(`${getKnowledgeBasePath()}/ragflow/chat-assistant/${id}`, {
    method: 'PUT',
    data: params,  
  });
};

export const postSession = (params:any) => {
  return request(`${getChatBasePath()}/session`, {
    method: 'POST',
    data: params,  
  });
};

export const postChat = (params:any) => {
  return request(`${getChatBasePath()}/chat`, {
    method: 'POST',
    data: params,  
  });
};