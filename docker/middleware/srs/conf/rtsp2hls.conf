listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
srs_log_file        ./logs/srs.log;
srs_log_level       info;

http_api {
    enabled         on;
    listen          1985;
    raw_api {
        enabled     on;
        allow_query on;
    }
    http_status {
        enabled     on;
        path        /api/status;
    }
}

http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
    crossdomain     on;
}

rtc_server {
    enabled         on;
    listen          8000;
    candidate       $CANDIDATE;
}

vhost __defaultVhost__ {
    hls {
        enabled             on;
        hls_fragment        2;
        hls_window          10;
        hls_wait_keyframe   on;
        hls_path            ./objs/nginx/html;
        hls_m3u8_file       [app]/[stream].m3u8;
        hls_ts_file         [app]/[stream]-[seqno].ts;
        hls_cleanup         on;
        hls_dispose         10;
        hls_on_error        continue;
        hls_acodec          aac;
        hls_vcodec          h264;
    }

    http_remux {
        enabled     on;
        mount       [vhost]/[app]/[stream].flv;
    }

    rtsp {
        enabled     on;
        listen      554;
        no_wait     off;
    }

    dash {
        enabled     on;
        dash_fragment   2;
        dash_update_period  1;
        dash_timeshift  10;
        dash_path       ./objs/nginx/html;
        dash_mpd_file   [app]/[stream].mpd;
    }

    dvr {
        enabled     on;
        dvr_path    ./objs/nginx/html/[app]/[stream]/[2006]/[01]/[02]/[15].[04].[05].[999].flv;
        dvr_plan    session;
    }

    # 以下是推流转发配置，方便测试时使用
    transcode {
        enabled     off;
        ffmpeg      ./objs/ffmpeg/bin/ffmpeg;
        
        engine rtsp {
            enabled         on;
            vfilter {
                i               rtsp://$(RTSP_URL);
            }
            vcodec          copy;
            acodec          copy;
            output          rtmp://127.0.0.1:[port]/live/[stream];
        }
    }
}

# 自动拉取RTSP流的配置示例（需要手动取消注释并配置）
# vhost rtsp.bsin.com {
#     ingest camera1 {
#         enabled     on;
#         input {
#             type    stream;
#             url     rtsp://admin:password@*************:554/Streaming/Channels/101;
#         }
#         ffmpeg      ./objs/ffmpeg/bin/ffmpeg;
#         engine {
#             enabled     on;
#             output      rtmp://127.0.0.1:1935/live/camera1;
#         }
#     }
# }