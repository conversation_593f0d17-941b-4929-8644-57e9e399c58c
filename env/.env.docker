# bsin local .env
## common
# zookeeper
BSIN_SOFA_RPC_REGISTRY_ADDRESS=zookeeper://***********:2181
BSIN_SOFA_RPC_BIND_NETWORK_INTERFACE=ens5

# shenyu
BSIN_SHENYU_REGISTER_SERVERLISTS=http://***********:9095
BSIN_SHENYU_REGISTER_PROPS_USERNAME=admin
BSIN_SHENYU_REGISTER_PROPS_PASSWORD=123456
shenyu.httpPath=http://***********:9095


# dubbo
BSIN_DOUBBO_REGISTRY_ADDRESS=nacos://***********:8848
BSIN_DOUBBO_REGISTRY_USERNAME=nacos
BSIN_DOUBBO_REGISTRY_PASSWORD=nacos

# websocket
BSIN_WEBSOCKET_HOST_PORT=***********:9095

# nacos
BSIN_NACOS_VERSION=v3.0.1
BSIN_NACOS_NAMESPACE=public
BSIN_NACOS_GROUP=SEATA_GROUP
BSIN_NACOS_SEATA_NAMESPACE=public
BSIN_NACOS_SERVER_ADDR=***********:8848
BSIN_NACOS_MODE=standalone
BSIN_NACOS_USERNAME=nacos
BSIN_NACOS_PASSWORD=nacos
BSIN_NACOS_AUTH_TOKEN=SecretKey012345678901234567890123456789012345678901234567890123456789

## mysql
NACOS_MYSQL_DB_URL=jdbc:mysql://***********:3306/bsin-nacos-config
NACOS_MYSQL_DB_USERNAME=root
NACOS_MYSQL_DB_PASSWORD=123456

PREFER_HOST_MODE=hostname
MODE=standalone
SPRING_DATASOURCE_PLATFORM=mysql
MYSQL_SERVICE_HOST=***********
MYSQL_SERVICE_DB_NAME=bsin-nacos-config
MYSQL_SERVICE_PORT=3306
MYSQL_SERVICE_USER=root
MYSQL_SERVICE_PASSWORD=123456
MYSQL_SERVICE_DB_PARAM=characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useUnicode=true&useSSL=false&serverTimezone=Asia/Shanghai&allowPublicKeyRetrieval=true
NACOS_AUTH_IDENTITY_KEY=nacos
NACOS_AUTH_IDENTITY_VALUE=nacos
NACOS_AUTH_TOKEN=VGhpc0lzTXlDdXN0b21TZWNyZXRLZXkwMTIzNDU2Nzg=


# milvus
MILVUS_HOST=***********
MILVUS_PORT=19530
MILVUS_USERNAME=root
MILVUS_PASSWORD=riit


# oss-ipfs
BSIN_OSS_IPFS_GATEWAY=http://ipfs.s11edao.com/ipfs/
BSIN_OSS_IPFS_API=http://ipfsadmin.s11edao.com/api/v0

# oss
BSIN_OSS_MESSAGE_UP_PATH=/home/<USER>/ipfsfile/
SBIN_OSS_MESSAGE_PRE_IMG_URL=http://file.s11edao.com

# AI
AI_DASHSCOPE=sk-12345


## TARGE-GATEWAY-ADMIN
# mysql
TARGE_GATEWAY_ADMIN_MYSQL_DB_URL=jdbc:mysql://***********:3306/bsin-shenyu-gateway
TARGE_GATEWAY_ADMIN_MYSQL_DB_USERNAME=root
TARGE_GATEWAY_ADMIN_MYSQL_DB_PASSWORD=123456

# redis
TARGE_GATEWAY_REDIS_HOST=***********
TARGE_GATEWAY_REDIS_PORT=6379
TARGE_GATEWAY_REDIS_PASSWORD=123456
TARGE_GATEWAY_REDIS_DB=0


## UPMS
# mysql
UPMS_MYSQL_DB_URL=jdbc:mysql://***********:3306/bsin-upms
UPMS_MYSQL_DB_USERNAME=root
UPMS_MYSQL_DB_PASSWORD=123456

# redis
UPMS_REDIS_HOST=***********
UPMS_REDIS_PORT=6379
UPMS_REDIS_PASSWORD=123456
UPMS_REDIS_DB=0

#DROMARA_X_FILE_STORAGE_DEFAULT_PLATFORM=aliyun-oss-1
#ALIYUN_OSS_PLATFORM=aliyun-oss-1
#ALIYUN_OSS_ACCESS_KEY=LTAI5tHCe8Xs4XHhcxNW3xDD
#ALIYUN_OSS_SECRET_KEY=******************************
#ALIYUN_OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com
#ALIYUN_OSS_BUCKET_NAME=bsin-jinan
#ALIYUN_OSS_DOMAIN=https://bsin-jinan.oss-cn-beijing.aliyuncs.com/
#ALIYUN_OSS_BASE_PATH=admin/

DROMARA_X_FILE_STORAGE_DEFAULT_PLATFORM=aliyun-oss-1
ALIYUN_OSS_PLATFORM=aliyun-oss-1
ALIYUN_OSS_ACCESS_KEY=LTAI5tLmTV6RTZZhREU26m8o
ALIYUN_OSS_SECRET_KEY=******************************
ALIYUN_OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com
ALIYUN_OSS_BUCKET_NAME=yue17-miniapp
ALIYUN_OSS_DOMAIN=https://yue17-miniapp.oss-cn-beijing.aliyuncs.com/
ALIYUN_OSS_BASE_PATH=admin/

# FILE
# mysql
FILE_MYSQL_DB_URL=jdbc:mysql://***********:3306/bsin-file
FILE_MYSQL_DB_USERNAME=root
FILE_MYSQL_DB_PASSWORD=123456

# redis
FILE_REDIS_HOST=***********
FILE_REDIS_PORT=6379
FILE_REDIS_PASSWORD=123456
FILE_REDIS_DB=0

# CRM
# mysql
CRM_MYSQL_DB_URL=jdbc:mysql://***********:3306/bsin-crm
CRM_MYSQL_DB_USERNAME=root
CRM_MYSQL_DB_PASSWORD=123456

# redis
CRM_REDIS_HOST=***********
CRM_REDIS_PORT=6379
CRM_REDIS_PASSWORD=123456
CRM_REDIS_DB=0


# COMMUNITY
# mysql
COMMUNITY_MYSQL_DB_URL=********************************************
COMMUNITY_MYSQL_DB_USERNAME=root
COMMUNITY_MYSQL_DB_PASSWORD=123456

# redis
COMMUNITY_REDIS_HOST=***********
COMMUNITY_REDIS_PORT=6379
COMMUNITY_REDIS_PASSWORD=123456
COMMUNITY_REDIS_DB=0

## WAAS
# mysql
WAAS_MYSQL_DB_URL=***************************************
WAAS_MYSQL_DB_USERNAME=root
WAAS_MYSQL_DB_PASSWORD=123456

# redis
WAAS_REDIS_HOST=***********
WAAS_REDIS_PORT=6379
WAAS_REDIS_PASSWORD=123456
WAAS_REDIS_DB=0

## OMS
# mysql
OMS_MYSQL_DB_URL=**************************************
OMS_MYSQL_DB_USERNAME=root
OMS_MYSQL_DB_PASSWORD=123456

# redis
OMS_REDIS_HOST=***********
OMS_REDIS_PORT=6379
OMS_REDIS_PASSWORD=123456
OMS_REDIS_DB=0

# tenantId
OMS_BSIN_TENANT_ID=1801458632269893632


# BRMS
# mysql
BRMS_MYSQL_DB_URL=***************************************
BRMS_MYSQL_DB_USERNAME=root
BRMS_MYSQL_DB_PASSWORD=123456
BRMS_MYSQL_DB_DRIVER_CLASS_NAME=com.mysql.cj.jdbc.Driver

# redis
BRMS_REDIS_HOST=***********
BRMS_REDIS_PORT=6379
BRMS_REDIS_PASSWORD=123456
BRMS_REDIS_DB=0


## APP-AGENT
# mysql
APP_AGENT_MYSQL_DB_URL=*************************************
APP_AGENT_MYSQL_DB_USERNAME=root
APP_AGENT_MYSQL_DB_PASSWORD=123456

# redis
APP_AGENT_REDIS_HOST=***********
APP_AGENT_REDIS_PORT=6379
APP_AGENT_REDIS_PASSWORD=123456
APP_AGENT_REDIS_DB=0


## WORKFLOW
# mysql
WORKFLOW_MYSQL_DB_USERNAME=*******************************************
WORKFLOW_MYSQL_DB_USERNAME=root
WORKFLOW_MYSQL_DB_PASSWORD=123456

# redis
WORKFLOW_REDIS_HOST=***********
WORKFLOW_REDIS_PORT=6379
WORKFLOW_REDIS_PASSWORD=123456
WORKFLOW_REDIS_DB=0

## WORKFLOW-ADMIN
# mysql
WORKFLOW_ADMIN_MYSQL_DB_URL=*******************************************-admin
WORKFLOW_ADMIN_MYSQL_DB_USERNAME=root
WORKFLOW_ADMIN_MYSQL_DB_PASSWORD=123456

# redis
WORKFLOW_ADMIN_REDIS_HOST=***********
WORKFLOW_ADMIN_REDIS_PORT=6379
WORKFLOW_ADMIN_REDIS_PASSWORD=123456
WORKFLOW_ADMIN_REDIS_DB=0


## IOT
# mysql
IOT_MYSQL_DB_URL=**************************************
IOT_MYSQL_DB_USERNAME=root
IOT_MYSQL_DB_PASSWORD=123456

# easymedia
EASYMEDIA_FLV_HOST=http://***********:8866
EASYMEDIA_API=http://***********:8865
EASYMEDIA_FLV_WS_LIVE=ws://***********:8866/live

# redis
IOT_REDIS_HOST=***********
IOT_REDIS_PORT=6379
IOT_REDIS_PASSWORD=123456
IOT_REDIS_DB=0

# emqx
EMQX_HOST=***********
EMQX_PORT=1883
EMQX_USERNAME=admin
EMQX_PASSWORD=public
EMQX_CLIENT_ID=123456


## search
# mysql
SEARCH_MYSQL_DB_URL=*****************************************
SEARCH_MYSQL_DB_USERNAME=root
SEARCH_MYSQL_DB_PASSWORD=123456

# redis
SEARCH_REDIS_HOST=***********
SEARCH_REDIS_PORT=6379
SEARCH_REDIS_PASSWORD=123456
SEARCH_REDIS_DB=0



